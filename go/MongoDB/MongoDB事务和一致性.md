# MongoDB事务和一致性

## 1. 事务基础

### ACID特性
- **原子性（Atomicity）**：事务中的操作要么全部成功，要么全部失败
- **一致性（Consistency）**：事务执行前后数据库状态一致
- **隔离性（Isolation）**：并发事务之间相互隔离
- **持久性（Durability）**：事务提交后数据持久保存

### MongoDB事务发展
- **MongoDB 4.0**：支持复制集内多文档事务
- **MongoDB 4.2**：支持分片集群多文档事务
- **MongoDB 5.0**：增强事务性能和功能

## 2. 单文档事务

### 原子性保证
```go
// 单文档操作天然具有原子性
func atomicUpdate(collection *mongo.Collection) error {
    filter := bson.M{"_id": "user123"}
    update := bson.M{
        "$inc": bson.M{"balance": -100},
        "$push": bson.M{"transactions": bson.M{
            "type":   "withdraw",
            "amount": 100,
            "date":   time.Now(),
        }},
    }
    
    _, err := collection.UpdateOne(context.Background(), filter, update)
    return err
}
```

### 文档设计优化
```go
// 通过嵌入文档减少跨文档事务需求
type Account struct {
    ID           primitive.ObjectID `bson:"_id,omitempty"`
    UserID       string            `bson:"user_id"`
    Balance      float64           `bson:"balance"`
    Transactions []Transaction     `bson:"transactions"`
}

type Transaction struct {
    ID     string    `bson:"id"`
    Type   string    `bson:"type"`
    Amount float64   `bson:"amount"`
    Date   time.Time `bson:"date"`
}
```

## 3. 多文档事务

### 基本事务操作
```go
func transferMoney(client *mongo.Client, fromAccount, toAccount string, amount float64) error {
    // 开始会话
    session, err := client.StartSession()
    if err != nil {
        return err
    }
    defer session.EndSession(context.Background())
    
    // 定义事务函数
    callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
        collection := client.Database("bank").Collection("accounts")
        
        // 扣减转出账户余额
        _, err := collection.UpdateOne(
            sessCtx,
            bson.M{"account_id": fromAccount},
            bson.M{"$inc": bson.M{"balance": -amount}},
        )
        if err != nil {
            return nil, err
        }
        
        // 增加转入账户余额
        _, err = collection.UpdateOne(
            sessCtx,
            bson.M{"account_id": toAccount},
            bson.M{"$inc": bson.M{"balance": amount}},
        )
        if err != nil {
            return nil, err
        }
        
        return nil, nil
    }
    
    // 执行事务
    _, err = session.WithTransaction(context.Background(), callback)
    return err
}
```

### 事务选项配置
```go
func configureTransaction(client *mongo.Client) error {
    session, err := client.StartSession()
    if err != nil {
        return err
    }
    defer session.EndSession(context.Background())
    
    // 配置事务选项
    txnOpts := options.Transaction().
        SetReadConcern(readconcern.Snapshot()).
        SetWriteConcern(writeconcern.New(writeconcern.WMajority())).
        SetReadPreference(readpref.Primary())
    
    callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
        // 事务操作
        return nil, nil
    }
    
    _, err = session.WithTransaction(context.Background(), callback, txnOpts)
    return err
}
```

## 4. 读关注（Read Concern）

### 读关注级别
```go
import "go.mongodb.org/mongo-driver/mongo/readconcern"

func demonstrateReadConcern(collection *mongo.Collection) {
    // local：读取本地数据，可能读到未提交的数据
    opts1 := options.Find().SetReadConcern(readconcern.Local())
    
    // available：读取可用数据，分片环境下可能读到孤儿文档
    opts2 := options.Find().SetReadConcern(readconcern.Available())
    
    // majority：读取已被大多数节点确认的数据
    opts3 := options.Find().SetReadConcern(readconcern.Majority())
    
    // snapshot：读取快照数据，事务中使用
    opts4 := options.Find().SetReadConcern(readconcern.Snapshot())
    
    // linearizable：线性化读取，保证读取最新数据
    opts5 := options.Find().SetReadConcern(readconcern.Linearizable())
}
```

### 读关注应用场景
```go
func readConcernExamples(collection *mongo.Collection) {
    // 金融应用：需要强一致性
    financialOpts := options.Find().SetReadConcern(readconcern.Majority())
    
    // 实时分析：可以接受最终一致性
    analyticsOpts := options.Find().SetReadConcern(readconcern.Local())
    
    // 事务中：使用快照隔离
    transactionOpts := options.Find().SetReadConcern(readconcern.Snapshot())
}
```

## 5. 写关注（Write Concern）

### 写关注配置
```go
import "go.mongodb.org/mongo-driver/mongo/writeconcern"

func demonstrateWriteConcern(collection *mongo.Collection) {
    // w: 1 - 只需要Primary确认
    wc1 := writeconcern.New(writeconcern.W(1))
    
    // w: majority - 需要大多数节点确认
    wcMajority := writeconcern.New(writeconcern.WMajority())
    
    // w: 3 - 需要3个节点确认
    wc3 := writeconcern.New(writeconcern.W(3))
    
    // j: true - 需要写入journal
    wcJournal := writeconcern.New(writeconcern.J(true))
    
    // 超时设置
    wcTimeout := writeconcern.New(
        writeconcern.WMajority(),
        writeconcern.WTimeout(5*time.Second),
    )
    
    // 应用写关注
    opts := options.Collection().SetWriteConcern(wcMajority)
    safeCollection := collection.Database().Collection("safe_collection", opts)
}
```

### 写关注权衡
```go
func writeConcernTradeoffs() {
    // 高性能，低可靠性
    fastWrite := writeconcern.New(writeconcern.W(1))
    
    // 平衡性能和可靠性
    balancedWrite := writeconcern.New(
        writeconcern.WMajority(),
        writeconcern.WTimeout(1*time.Second),
    )
    
    // 高可靠性，低性能
    safeWrite := writeconcern.New(
        writeconcern.WMajority(),
        writeconcern.J(true),
        writeconcern.WTimeout(10*time.Second),
    )
}
```

## 6. 事务隔离级别

### 快照隔离
```go
func snapshotIsolation(client *mongo.Client) error {
    session, err := client.StartSession()
    if err != nil {
        return err
    }
    defer session.EndSession(context.Background())
    
    callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
        collection := client.Database("test").Collection("accounts")
        
        // 在事务开始时建立快照
        // 所有读操作都基于这个快照
        var account1, account2 bson.M
        
        err := collection.FindOne(sessCtx, bson.M{"_id": "account1"}).Decode(&account1)
        if err != nil {
            return nil, err
        }
        
        // 即使其他事务修改了account2，这里读到的仍是快照时的数据
        err = collection.FindOne(sessCtx, bson.M{"_id": "account2"}).Decode(&account2)
        if err != nil {
            return nil, err
        }
        
        // 基于快照数据进行业务逻辑处理
        return nil, nil
    }
    
    txnOpts := options.Transaction().SetReadConcern(readconcern.Snapshot())
    _, err = session.WithTransaction(context.Background(), callback, txnOpts)
    return err
}
```

## 7. 事务性能优化

### 减少事务范围
```go
// 不好的做法：事务范围过大
func badTransaction(client *mongo.Client) error {
    session, err := client.StartSession()
    if err != nil {
        return err
    }
    defer session.EndSession(context.Background())
    
    callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
        // 大量的数据库操作
        for i := 0; i < 1000; i++ {
            // 数据库操作...
        }
        
        // 复杂的业务逻辑计算
        time.Sleep(5 * time.Second)
        
        return nil, nil
    }
    
    _, err = session.WithTransaction(context.Background(), callback)
    return err
}

// 好的做法：最小化事务范围
func goodTransaction(client *mongo.Client) error {
    // 在事务外进行复杂计算
    result := complexCalculation()
    
    session, err := client.StartSession()
    if err != nil {
        return err
    }
    defer session.EndSession(context.Background())
    
    callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
        // 只在事务内进行必要的数据库操作
        collection := client.Database("test").Collection("data")
        _, err := collection.UpdateOne(sessCtx, bson.M{"_id": "key"}, bson.M{"$set": result})
        return nil, err
    }
    
    _, err = session.WithTransaction(context.Background(), callback)
    return err
}
```

### 批量操作优化
```go
func optimizedBatchTransaction(client *mongo.Client) error {
    session, err := client.StartSession()
    if err != nil {
        return err
    }
    defer session.EndSession(context.Background())
    
    callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
        collection := client.Database("test").Collection("orders")
        
        // 使用批量操作而不是循环单个操作
        var operations []mongo.WriteModel
        
        for i := 0; i < 100; i++ {
            operation := mongo.NewUpdateOneModel().
                SetFilter(bson.M{"order_id": fmt.Sprintf("order_%d", i)}).
                SetUpdate(bson.M{"$set": bson.M{"status": "processed"}})
            operations = append(operations, operation)
        }
        
        opts := options.BulkWrite().SetOrdered(false)
        _, err := collection.BulkWrite(sessCtx, operations, opts)
        return nil, err
    }
    
    _, err = session.WithTransaction(context.Background(), callback)
    return err
}
```

## 8. 错误处理和重试

### 事务重试机制
```go
func robustTransaction(client *mongo.Client) error {
    maxRetries := 3
    
    for attempt := 0; attempt < maxRetries; attempt++ {
        session, err := client.StartSession()
        if err != nil {
            continue
        }
        
        callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
            // 事务操作
            return nil, nil
        }
        
        _, err = session.WithTransaction(context.Background(), callback)
        session.EndSession(context.Background())
        
        if err == nil {
            return nil // 成功
        }
        
        // 检查是否是可重试的错误
        if isRetryableError(err) {
            time.Sleep(time.Duration(attempt+1) * 100 * time.Millisecond)
            continue
        }
        
        return err // 不可重试的错误
    }
    
    return fmt.Errorf("transaction failed after %d attempts", maxRetries)
}

func isRetryableError(err error) bool {
    // 检查错误类型，判断是否可重试
    if mongo.IsTimeout(err) {
        return true
    }
    
    if mongo.IsNetworkError(err) {
        return true
    }
    
    // 检查特定的错误代码
    if cmdErr, ok := err.(mongo.CommandError); ok {
        switch cmdErr.Code {
        case 112: // WriteConflict
            return true
        case 117: // ConflictingOperationInProgress
            return true
        default:
            return false
        }
    }
    
    return false
}
```

## 9. 面试要点

### 事务相关
1. **MongoDB何时支持事务？**
   - 4.0版本开始支持复制集事务
   - 4.2版本支持分片集群事务

2. **事务的性能影响？**
   - 增加延迟、降低吞吐量
   - 需要额外的资源开销

3. **如何优化事务性能？**
   - 最小化事务范围
   - 使用批量操作
   - 合理设置超时时间

### 一致性相关
1. **读关注的作用？**
   - 控制读取数据的一致性级别
   - 平衡性能和一致性

2. **写关注的意义？**
   - 控制写入确认的级别
   - 影响数据持久性和性能

3. **如何选择一致性级别？**
   - 根据业务需求权衡
   - 考虑性能和可靠性要求

### 最佳实践
- **优先使用单文档操作**：天然原子性
- **合理设计文档结构**：减少跨文档事务
- **监控事务性能**：及时发现问题
- **处理事务冲突**：实现重试机制

### 一句话总结
> MongoDB通过多文档事务、读写关注等机制提供ACID保证，需要在一致性和性能之间找到平衡
