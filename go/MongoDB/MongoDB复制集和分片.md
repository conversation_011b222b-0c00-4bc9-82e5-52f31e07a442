# MongoDB复制集和分片

## 1. 复制集（Replica Set）

### 基本概念
复制集是MongoDB的高可用解决方案，通过数据冗余和自动故障转移保证服务可用性。

### 复制集架构
```
Primary (主节点)
├── Secondary (从节点1)
├── Secondary (从节点2)
└── Arbiter (仲裁节点) - 可选
```

### 节点类型
- **Primary**：接受写操作，唯一的写入节点
- **Secondary**：从Primary同步数据，可以提供读操作
- **Arbiter**：只参与选举，不存储数据

## 2. 复制集配置

### 初始化复制集
```javascript
// 连接到MongoDB实例
mongo --port 27017

// 初始化复制集
rs.initiate({
  _id: "myReplicaSet",
  members: [
    { _id: 0, host: "mongodb1.example.com:27017" },
    { _id: 1, host: "mongodb2.example.com:27017" },
    { _id: 2, host: "mongodb3.example.com:27017" }
  ]
})
```

### Go语言连接复制集
```go
package main

import (
    "context"
    "go.mongodb.org/mongo-driver/mongo"
    "go.mongodb.org/mongo-driver/mongo/options"
)

func connectReplicaSet() (*mongo.Client, error) {
    // 复制集连接字符串
    uri := "mongodb://mongodb1.example.com:27017,mongodb2.example.com:27017,mongodb3.example.com:27017/?replicaSet=myReplicaSet"
    
    clientOptions := options.Client().ApplyURI(uri)
    
    // 读偏好设置
    clientOptions.SetReadPreference(readpref.SecondaryPreferred())
    
    client, err := mongo.Connect(context.Background(), clientOptions)
    if err != nil {
        return nil, err
    }
    
    return client, nil
}
```

### 读偏好设置
```go
import "go.mongodb.org/mongo-driver/mongo/readpref"

// 不同的读偏好
func setReadPreferences() {
    // 只从Primary读取
    primary := readpref.Primary()
    
    // 优先从Secondary读取
    secondaryPreferred := readpref.SecondaryPreferred()
    
    // 只从Secondary读取
    secondary := readpref.Secondary()
    
    // 优先从Primary读取
    primaryPreferred := readpref.PrimaryPreferred()
    
    // 从最近的节点读取
    nearest := readpref.Nearest()
}
```

## 3. 分片（Sharding）

### 基本概念
分片是MongoDB的水平扩展解决方案，将数据分布到多个服务器上。

### 分片架构
```
Client Application
        ↓
    mongos (路由)
        ↓
Config Servers (配置服务器)
        ↓
Shard1    Shard2    Shard3
(复制集)  (复制集)  (复制集)
```

### 组件说明
- **mongos**：查询路由器，客户端连接点
- **Config Servers**：存储集群元数据
- **Shard**：实际存储数据的分片

## 4. 分片配置

### 启动分片集群
```bash
# 1. 启动配置服务器
mongod --configsvr --replSet configReplSet --port 27019 --dbpath /data/configdb

# 2. 启动分片服务器
mongod --shardsvr --replSet shard1ReplSet --port 27018 --dbpath /data/shard1

# 3. 启动mongos路由
mongos --configdb configReplSet/config1.example.com:27019 --port 27017
```

### 配置分片
```javascript
// 连接到mongos
mongo --port 27017

// 添加分片
sh.addShard("shard1ReplSet/shard1.example.com:27018")
sh.addShard("shard2ReplSet/shard2.example.com:27018")
sh.addShard("shard3ReplSet/shard3.example.com:27018")

// 启用数据库分片
sh.enableSharding("myDatabase")

// 创建分片键
sh.shardCollection("myDatabase.users", {"user_id": 1})
```

## 5. 分片键设计

### 分片键选择原则
1. **高基数**：分片键值要有足够的唯一性
2. **均匀分布**：避免热点分片
3. **查询友好**：常用查询包含分片键

### 分片键示例
```go
// 好的分片键设计
type User struct {
    ID       primitive.ObjectID `bson:"_id"`
    UserID   string            `bson:"user_id"`   // 高基数
    Region   string            `bson:"region"`    // 地理分布
    CreateAt time.Time         `bson:"created_at"`
}

// 复合分片键
func setupSharding() {
    // 基于用户ID和地区的复合分片键
    // sh.shardCollection("app.users", {"user_id": 1, "region": 1})
}
```

### 避免的分片键
```go
// 不好的分片键示例
type BadExample struct {
    ID        primitive.ObjectID `bson:"_id"`        // 单调递增
    Timestamp time.Time         `bson:"timestamp"`   // 单调递增
    Status    string            `bson:"status"`      // 低基数
}
```

## 6. 性能优化

### 查询优化
```go
func optimizedQueries(collection *mongo.Collection) {
    // 包含分片键的查询（路由到特定分片）
    filter := bson.M{
        "user_id": "user123",
        "region":  "us-east",
    }
    
    // 不包含分片键的查询（广播到所有分片）
    badFilter := bson.M{
        "email": "<EMAIL>",
    }
    
    // 使用hint指定索引
    opts := options.Find().SetHint(bson.M{"user_id": 1, "region": 1})
    cursor, _ := collection.Find(context.Background(), filter, opts)
}
```

### 写入优化
```go
func optimizedWrites(collection *mongo.Collection) {
    // 批量写入
    var operations []mongo.WriteModel
    
    for i := 0; i < 1000; i++ {
        doc := bson.M{
            "user_id": fmt.Sprintf("user%d", i),
            "region":  getRegion(i),
            "data":    generateData(),
        }
        
        operation := mongo.NewInsertOneModel().SetDocument(doc)
        operations = append(operations, operation)
    }
    
    // 批量执行
    opts := options.BulkWrite().SetOrdered(false)
    collection.BulkWrite(context.Background(), operations, opts)
}
```

## 7. 监控和维护

### 集群状态监控
```go
func monitorCluster(client *mongo.Client) {
    // 检查分片状态
    db := client.Database("admin")
    
    // 获取分片统计
    var result bson.M
    err := db.RunCommand(context.Background(), bson.M{"listShards": 1}).Decode(&result)
    if err != nil {
        log.Printf("Error getting shard info: %v", err)
        return
    }
    
    fmt.Printf("Shards: %+v\n", result)
    
    // 检查块分布
    err = db.RunCommand(context.Background(), bson.M{"sh.status": 1}).Decode(&result)
    if err != nil {
        log.Printf("Error getting chunk distribution: %v", err)
        return
    }
}
```

### 平衡器管理
```javascript
// 检查平衡器状态
sh.getBalancerState()

// 启用/禁用平衡器
sh.enableBalancing("myDatabase.users")
sh.disableBalancing("myDatabase.users")

// 设置平衡器窗口
use config
db.settings.update(
   { _id: "balancer" },
   { $set: { activeWindow : { start : "23:00", stop : "6:00" } } },
   { upsert: true }
)
```

## 8. 故障处理

### 复制集故障转移
```go
func handleReplicaSetFailover(client *mongo.Client) {
    // 设置重试写入
    wc := writeconcern.New(writeconcern.WMajority(), writeconcern.WTimeout(5*time.Second))
    opts := options.Collection().SetWriteConcern(wc)
    
    collection := client.Database("mydb").Collection("mycoll", opts)
    
    // 写入操作会自动重试
    _, err := collection.InsertOne(context.Background(), bson.M{"data": "test"})
    if err != nil {
        log.Printf("Write failed: %v", err)
    }
}
```

### 分片故障处理
```go
func handleShardFailure(client *mongo.Client) {
    // 设置读偏好，允许从Secondary读取
    opts := options.Find().SetReadPreference(readpref.SecondaryPreferred())
    
    collection := client.Database("mydb").Collection("mycoll")
    cursor, err := collection.Find(context.Background(), bson.M{}, opts)
    if err != nil {
        log.Printf("Read failed: %v", err)
        return
    }
    defer cursor.Close(context.Background())
}
```

## 9. 面试要点

### 复制集相关
1. **复制集的作用？**
   - 高可用性、数据冗余、读扩展

2. **Primary选举机制？**
   - 基于优先级、心跳检测、多数派原则

3. **读偏好的影响？**
   - 数据一致性 vs 读性能的权衡

### 分片相关
1. **分片键如何选择？**
   - 高基数、均匀分布、查询友好

2. **分片的优缺点？**
   - 优点：水平扩展、负载分散
   - 缺点：复杂性增加、跨分片查询性能

3. **如何避免热点分片？**
   - 合理选择分片键、预分片、监控分布

### 最佳实践
- **复制集至少3个节点**：保证高可用
- **分片键不可修改**：设计时要慎重考虑
- **监控集群健康**：及时发现和处理问题
- **定期备份**：数据安全的最后保障

### 一句话总结
> 复制集提供高可用性和读扩展，分片提供水平扩展能力，两者结合构建高性能分布式MongoDB集群
