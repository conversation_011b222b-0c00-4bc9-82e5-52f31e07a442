# MongoDB索引策略与优化

## 索引基础概念

### 什么是索引
索引是数据库中用于快速定位数据的数据结构，类似于书籍的目录。MongoDB使用B-tree结构存储索引。

### 索引的作用
- **提高查询速度**：避免全集合扫描
- **支持排序**：利用索引顺序进行排序
- **保证唯一性**：唯一索引防止重复数据
- **支持地理查询**：地理空间索引

## 索引类型

### 1. 单字段索引
```go
// 创建单字段索引
func CreateSingleFieldIndex(collection *mongo.Collection) error {
    indexModel := mongo.IndexModel{
        Keys: bson.D{{"user_id", 1}}, // 1表示升序，-1表示降序
    }
    
    _, err := collection.Indexes().CreateOne(context.Background(), indexModel)
    return err
}

// 查询会使用索引
filter := bson.M{"user_id": "12345"}
cursor, err := collection.Find(context.Background(), filter)
```

### 2. 复合索引
```go
// 创建复合索引
func CreateCompoundIndex(collection *mongo.Collection) error {
    indexModel := mongo.IndexModel{
        Keys: bson.D{
            {"status", 1},
            {"created_at", -1},
            {"user_id", 1},
        },
    }
    
    _, err := collection.Indexes().CreateOne(context.Background(), indexModel)
    return err
}

// 支持的查询模式（遵循最左前缀原则）
// ✓ {status: "active"}
// ✓ {status: "active", created_at: {$gte: date}}
// ✓ {status: "active", created_at: {$gte: date}, user_id: "123"}
// ✗ {created_at: {$gte: date}} // 不能使用索引
// ✗ {user_id: "123"} // 不能使用索引
```

### 3. 多键索引
```go
type User struct {
    ID    primitive.ObjectID `bson:"_id,omitempty"`
    Name  string            `bson:"name"`
    Tags  []string          `bson:"tags"` // 数组字段
    Roles []string          `bson:"roles"`
}

// 为数组字段创建索引
func CreateMultikeyIndex(collection *mongo.Collection) error {
    indexModel := mongo.IndexModel{
        Keys: bson.D{{"tags", 1}},
    }
    
    _, err := collection.Indexes().CreateOne(context.Background(), indexModel)
    return err
}

// 查询数组元素
filter := bson.M{"tags": "golang"} // 查找包含"golang"标签的用户
```

### 4. 文本索引
```go
// 创建文本索引
func CreateTextIndex(collection *mongo.Collection) error {
    indexModel := mongo.IndexModel{
        Keys: bson.D{
            {"title", "text"},
            {"content", "text"},
        },
        Options: options.Index().SetDefaultLanguage("english"),
    }
    
    _, err := collection.Indexes().CreateOne(context.Background(), indexModel)
    return err
}

// 文本搜索
func TextSearch(collection *mongo.Collection, searchText string) ([]bson.M, error) {
    filter := bson.M{"$text": bson.M{"$search": searchText}}
    cursor, err := collection.Find(context.Background(), filter)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())
    
    var results []bson.M
    err = cursor.All(context.Background(), &results)
    return results, err
}
```

### 5. 地理空间索引
```go
type Location struct {
    Type        string    `bson:"type"`
    Coordinates []float64 `bson:"coordinates"`
}

type Store struct {
    ID       primitive.ObjectID `bson:"_id,omitempty"`
    Name     string            `bson:"name"`
    Location Location          `bson:"location"`
}

// 创建2dsphere索引
func CreateGeoIndex(collection *mongo.Collection) error {
    indexModel := mongo.IndexModel{
        Keys: bson.D{{"location", "2dsphere"}},
    }
    
    _, err := collection.Indexes().CreateOne(context.Background(), indexModel)
    return err
}

// 地理查询
func FindNearbyStores(collection *mongo.Collection, lng, lat float64, maxDistance int) ([]Store, error) {
    filter := bson.M{
        "location": bson.M{
            "$near": bson.M{
                "$geometry": bson.M{
                    "type":        "Point",
                    "coordinates": []float64{lng, lat},
                },
                "$maxDistance": maxDistance,
            },
        },
    }
    
    cursor, err := collection.Find(context.Background(), filter)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(context.Background())
    
    var stores []Store
    err = cursor.All(context.Background(), &stores)
    return stores, err
}
```

## 索引优化策略

### 1. 索引选择性
**高选择性**：索引字段的不同值越多，选择性越高，索引效果越好

```go
// 分析字段选择性
func AnalyzeSelectivity(collection *mongo.Collection, field string) {
    // 统计总文档数
    totalDocs, _ := collection.CountDocuments(context.Background(), bson.M{})
    
    // 统计不同值的数量
    pipeline := []bson.M{
        {"$group": bson.M{"_id": "$" + field}},
        {"$count": "distinct_values"},
    }
    
    cursor, _ := collection.Aggregate(context.Background(), pipeline)
    var result []bson.M
    cursor.All(context.Background(), &result)
    
    if len(result) > 0 {
        distinctValues := result[0]["distinct_values"]
        selectivity := float64(distinctValues.(int32)) / float64(totalDocs)
        fmt.Printf("Field %s selectivity: %.2f\n", field, selectivity)
    }
}
```

### 2. 索引基数
**原则**：将高基数字段放在复合索引的前面

```go
// 好的复合索引设计
indexModel := mongo.IndexModel{
    Keys: bson.D{
        {"user_id", 1},    // 高基数：每个用户唯一
        {"status", 1},     // 中基数：几种状态
        {"type", 1},       // 低基数：几种类型
    },
}
```

### 3. 查询模式分析
```go
// 分析查询模式，设计合适的索引
type QueryPattern struct {
    Filter bson.M
    Sort   bson.M
    Count  int64
}

func AnalyzeQueryPatterns(collection *mongo.Collection) []QueryPattern {
    // 常见查询模式
    patterns := []QueryPattern{
        {Filter: bson.M{"status": "active"}, Count: 1000},
        {Filter: bson.M{"user_id": bson.M{"$exists": true}}, Sort: bson.M{"created_at": -1}, Count: 800},
        {Filter: bson.M{"status": "active", "type": "premium"}, Count: 500},
    }
    
    // 根据查询频率和模式设计索引
    for _, pattern := range patterns {
        fmt.Printf("Query pattern: %v, Count: %d\n", pattern.Filter, pattern.Count)
    }
    
    return patterns
}
```

## 索引性能监控

### 1. 查询计划分析
```go
func ExplainQuery(collection *mongo.Collection, filter bson.M) {
    // 获取查询执行计划
    cursor, err := collection.Find(context.Background(), filter)
    if err != nil {
        return
    }
    defer cursor.Close(context.Background())
    
    // 使用explain分析
    explainResult, err := collection.Find(context.Background(), filter).Explain(context.Background(), options.ExplainVerbosity("executionStats"))
    if err != nil {
        return
    }
    
    fmt.Printf("Query execution stats: %+v\n", explainResult)
}
```

### 2. 索引使用统计
```go
func GetIndexStats(collection *mongo.Collection) {
    // 获取索引统计信息
    cursor, err := collection.Aggregate(context.Background(), []bson.M{
        {"$indexStats": bson.M{}},
    })
    if err != nil {
        return
    }
    defer cursor.Close(context.Background())
    
    var stats []bson.M
    cursor.All(context.Background(), &stats)
    
    for _, stat := range stats {
        fmt.Printf("Index: %s, Usage: %+v\n", stat["name"], stat["accesses"])
    }
}
```

### 3. 慢查询分析
```go
func AnalyzeSlowQueries(db *mongo.Database) {
    // 启用profiling
    db.RunCommand(context.Background(), bson.M{
        "profile": 2,
        "slowms":  100, // 记录超过100ms的查询
    })
    
    // 查询慢查询日志
    profileColl := db.Collection("system.profile")
    cursor, err := profileColl.Find(context.Background(), bson.M{
        "ts": bson.M{"$gte": time.Now().Add(-time.Hour)},
    })
    if err != nil {
        return
    }
    defer cursor.Close(context.Background())
    
    var profiles []bson.M
    cursor.All(context.Background(), &profiles)
    
    for _, profile := range profiles {
        fmt.Printf("Slow query: %+v\n", profile)
    }
}
```

## 索引维护

### 1. 索引重建
```go
func RebuildIndexes(collection *mongo.Collection) error {
    // 获取所有索引
    cursor, err := collection.Indexes().List(context.Background())
    if err != nil {
        return err
    }
    defer cursor.Close(context.Background())
    
    var indexes []bson.M
    cursor.All(context.Background(), &indexes)
    
    for _, index := range indexes {
        indexName := index["name"].(string)
        if indexName != "_id_" { // 跳过默认索引
            // 重建索引
            err := collection.Indexes().DropOne(context.Background(), indexName)
            if err != nil {
                continue
            }
            
            // 重新创建索引
            keys := index["key"].(bson.M)
            indexModel := mongo.IndexModel{Keys: keys}
            collection.Indexes().CreateOne(context.Background(), indexModel)
        }
    }
    
    return nil
}
```

### 2. 索引清理
```go
func CleanupUnusedIndexes(collection *mongo.Collection) error {
    // 获取索引使用统计
    cursor, err := collection.Aggregate(context.Background(), []bson.M{
        {"$indexStats": bson.M{}},
    })
    if err != nil {
        return err
    }
    defer cursor.Close(context.Background())
    
    var stats []bson.M
    cursor.All(context.Background(), &stats)
    
    for _, stat := range stats {
        indexName := stat["name"].(string)
        accesses := stat["accesses"].(bson.M)
        ops := accesses["ops"].(int64)
        
        // 删除未使用的索引
        if ops == 0 && indexName != "_id_" {
            fmt.Printf("Dropping unused index: %s\n", indexName)
            collection.Indexes().DropOne(context.Background(), indexName)
        }
    }
    
    return nil
}
```

## 面试高频问题

### Q1: 复合索引的最左前缀原则？
复合索引只能从最左边的字段开始使用，不能跳过前面的字段。

**示例**：索引`{a: 1, b: 1, c: 1}`
- ✓ 可以使用：`{a: 1}`, `{a: 1, b: 1}`, `{a: 1, b: 1, c: 1}`
- ✗ 不能使用：`{b: 1}`, `{c: 1}`, `{b: 1, c: 1}`

### Q2: 如何选择索引字段的顺序？
1. **等值查询字段**放在前面
2. **高选择性字段**放在前面
3. **排序字段**放在后面
4. **范围查询字段**放在最后

### Q3: 索引对写操作的影响？
- **插入**：需要更新所有相关索引
- **更新**：如果更新索引字段，需要重新排序
- **删除**：需要从索引中移除记录
- **建议**：避免过多索引，平衡读写性能

### Q4: 什么情况下索引会失效？
- 使用`$regex`正则表达式（除非以^开头）
- 使用`$ne`、`$nin`操作符
- 对索引字段进行函数操作
- 数据类型不匹配

### Q5: 如何监控索引性能？
- 使用`explain()`分析查询计划
- 启用profiling记录慢查询
- 使用`$indexStats`查看索引使用情况
- 监控`executionStats`中的关键指标

## 最佳实践

### 索引设计原则
1. **基于查询模式**：分析实际查询需求
2. **考虑写入性能**：避免过多索引
3. **定期维护**：清理无用索引，重建碎片化索引
4. **监控性能**：持续监控索引效果

### 性能优化技巧
- 为高频查询创建专门的索引
- 使用部分索引减少索引大小
- 合理使用稀疏索引
- 考虑使用哈希索引处理等值查询
