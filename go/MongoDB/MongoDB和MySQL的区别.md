# MongoDB和MySQL的区别

## 1. 核心对比

| 特性 | MongoDB | MySQL |
|------|---------|-------|
| 数据模型 | 文档型(BSON) | 关系型(表格) |
| Schema | 灵活，无固定结构 | 严格，预定义结构 |
| 查询语言 | MongoDB查询语法 | SQL |
| 事务支持 | 4.0+支持ACID | 完整ACID支持 |
| 扩展方式 | 水平扩展(分片) | 垂直扩展为主 |
| JOIN操作 | 有限支持 | 强大的JOIN |

## 2. 数据存储模式

### MySQL - 关系型
```sql
-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY,
    name VARCHAR(100),
    email VARCHAR(100)
);

-- 订单表
CREATE TABLE orders (
    id INT PRIMARY KEY,
    user_id INT,
    amount DECIMAL(10,2),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### MongoDB - 文档型
```javascript
// 用户文档（可以嵌入订单信息）
{
  "_id": ObjectId("..."),
  "name": "张三",
  "email": "<EMAIL>",
  "orders": [
    {
      "id": 1,
      "amount": 299.99,
      "date": ISODate("2024-01-01")
    }
  ]
}
```

## 3. 性能特点

### MongoDB优势
- **无JOIN操作**：数据嵌入减少关联查询
- **灵活Schema**：无需ALTER TABLE操作
- **水平扩展**：天然支持分片
- **内存映射**：利用操作系统缓存

### MySQL优势
- **成熟优化**：查询优化器强大
- **事务完整性**：ACID特性完善
- **复杂查询**：SQL表达能力强
- **生态丰富**：工具和经验丰富

## 4. 适用场景

### MongoDB适合
- **内容管理**：博客、CMS系统
- **实时分析**：日志分析、用户行为
- **物联网**：传感器数据收集
- **快速原型**：敏捷开发、需求变化频繁

### MySQL适合
- **金融系统**：银行、支付、会计
- **电商平台**：订单、库存、用户管理
- **企业应用**：ERP、CRM系统
- **数据仓库**：复杂报表、数据分析

## 5. 性能对比

### 读写性能
```go
// MongoDB - 单文档读取
db.users.findOne({"_id": ObjectId("...")})

// MySQL - 关联查询
SELECT u.*, o.* FROM users u
LEFT JOIN orders o ON u.id = o.user_id
WHERE u.id = 1
```

### 扩展性能
- **MongoDB**：水平分片，线性扩展
- **MySQL**：读写分离，分库分表复杂

## 6. 事务处理

### MongoDB事务（4.0+）
```javascript
session = db.getMongo().startSession()
session.startTransaction()

try {
    db.accounts.updateOne(
        {"_id": "A"},
        {"$inc": {"balance": -100}},
        {"session": session}
    )
    db.accounts.updateOne(
        {"_id": "B"},
        {"$inc": {"balance": 100}},
        {"session": session}
    )
    session.commitTransaction()
} catch (error) {
    session.abortTransaction()
}
```

### MySQL事务
```sql
START TRANSACTION;
UPDATE accounts SET balance = balance - 100 WHERE id = 'A';
UPDATE accounts SET balance = balance + 100 WHERE id = 'B';
COMMIT;
```

## 7. 面试要点

### 核心问题
1. **什么时候选择MongoDB？**
   - 数据结构灵活、需要快速迭代
   - 需要水平扩展、处理大量非结构化数据

2. **什么时候选择MySQL？**
   - 需要强一致性、复杂事务
   - 数据关系复杂、需要复杂查询

3. **MongoDB的缺点？**
   - 内存消耗大、JOIN能力有限
   - 事务支持相对较弱

4. **如何迁移？**
   - 数据模型重新设计
   - 应用层逻辑调整
   - 渐进式迁移策略

### 技术选型原则
- **数据结构**：结构化选MySQL，半结构化选MongoDB
- **扩展需求**：水平扩展选MongoDB，垂直扩展选MySQL
- **一致性要求**：强一致性选MySQL，最终一致性可选MongoDB
- **团队技能**：考虑团队对技术栈的熟悉程度

### 一句话总结
> MongoDB适合灵活数据模型和水平扩展场景，MySQL适合强一致性和复杂关系查询场景