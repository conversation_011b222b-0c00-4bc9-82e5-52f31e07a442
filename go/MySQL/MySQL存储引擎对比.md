## MySQL存储引擎对比

### 主要存储引擎

| 特性 | InnoDB | MyISAM | Memory | Archive |
|------|--------|--------|---------|---------|
| **事务支持** | ✓ | ✗ | ✗ | ✗ |
| **行级锁** | ✓ | ✗ | ✓ | ✗ |
| **外键约束** | ✓ | ✗ | ✗ | ✗ |
| **崩溃恢复** | ✓ | ✗ | ✗ | ✗ |
| **MVCC** | ✓ | ✗ | ✗ | ✗ |
| **全文索引** | ✓(5.6+) | ✓ | ✗ | ✗ |
| **压缩** | ✓ | ✓ | ✗ | ✓ |
| **存储限制** | 256TB | 256TB | RAM | 无限制 |

### InnoDB存储引擎

#### 特点
- **事务安全**：支持ACID事务特性
- **行级锁定**：支持高并发访问
- **外键约束**：维护数据完整性
- **崩溃恢复**：自动崩溃恢复机制
- **MVCC**：多版本并发控制

#### 适用场景
- 需要事务支持的应用
- 高并发读写场景
- 数据一致性要求高
- 需要外键约束

#### 存储结构
```
表空间 (Tablespace)
├── 段 (Segment)
│   ├── 区 (Extent) - 1MB，64个页
│   │   └── 页 (Page) - 16KB
│   │       └── 行 (Row)
```

### MyISAM存储引擎

#### 特点
- **表级锁定**：并发性能较差
- **不支持事务**：无法回滚
- **全文索引**：支持全文搜索
- **压缩存储**：节省存储空间
- **快速计数**：COUNT(*)操作很快

#### 适用场景
- 读多写少的应用
- 不需要事务支持
- 需要全文索引功能
- 数据仓库、日志系统

#### 文件结构
- **.frm**：表结构定义
- **.MYD**：数据文件
- **.MYI**：索引文件

### Memory存储引擎

#### 特点
- **内存存储**：数据存储在RAM中
- **快速访问**：读写速度极快
- **易失性**：服务器重启数据丢失
- **固定长度**：只支持固定长度行

#### 适用场景
- 临时表
- 缓存表
- 查找表
- 会话数据

### Archive存储引擎

#### 特点
- **高压缩比**：使用zlib压缩
- **只支持INSERT和SELECT**：不支持UPDATE和DELETE
- **适合归档**：历史数据存储

#### 适用场景
- 日志归档
- 历史数据存储
- 数据仓库

### 存储引擎选择

#### 选择InnoDB的情况
- 需要事务支持
- 高并发读写
- 数据一致性要求高
- 需要外键约束
- 需要崩溃恢复

#### 选择MyISAM的情况
- 读多写少
- 不需要事务
- 需要全文索引
- 存储空间敏感
- 简单的数据结构

#### 选择Memory的情况
- 临时数据存储
- 高速缓存
- 查找表
- 会话数据

### 性能对比

#### 读性能
1. **Memory** - 最快（内存访问）
2. **MyISAM** - 较快（无事务开销）
3. **InnoDB** - 中等（事务和锁开销）

#### 写性能
1. **Memory** - 最快（内存写入）
2. **InnoDB** - 中等（行级锁，并发好）
3. **MyISAM** - 较慢（表级锁，并发差）

#### 并发性能
1. **InnoDB** - 最好（行级锁）
2. **Memory** - 中等（表级锁但速度快）
3. **MyISAM** - 最差（表级锁）

### 迁移注意事项

#### MyISAM到InnoDB
```sql
-- 修改存储引擎
ALTER TABLE table_name ENGINE=InnoDB;

-- 注意事项
-- 1. 自增列行为可能不同
-- 2. 全文索引需要重建
-- 3. 表级锁变为行级锁
-- 4. 存储空间可能增加
```

#### 检查存储引擎
```sql
-- 查看表的存储引擎
SHOW TABLE STATUS WHERE Name = 'table_name';

-- 查看所有表的存储引擎
SELECT TABLE_NAME, ENGINE 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'database_name';
```

### 面试要点

**Q: InnoDB和MyISAM的主要区别？**
A: 事务支持、锁粒度、外键约束、崩溃恢复、MVCC支持

**Q: 什么时候选择InnoDB？**
A: 需要事务、高并发、数据一致性要求高的场景

**Q: MyISAM的COUNT(*)为什么比InnoDB快？**
A: MyISAM维护了行数计数器，InnoDB需要实时计算

**Q: InnoDB如何实现MVCC？**
A: 通过Undo Log和Read View实现多版本并发控制
