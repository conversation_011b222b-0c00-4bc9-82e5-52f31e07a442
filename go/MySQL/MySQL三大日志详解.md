## MySQL三大日志：Binlog、Redo Log、Undo Log

### 三大日志对比

| 日志类型 | 作用 | 记录内容 | 存储位置 | 主要用途 |
|---------|------|----------|----------|----------|
| **Binlog** | 数据恢复、主从复制 | SQL语句或行变更 | mysql-bin.* | 主从同步、数据恢复 |
| **Redo Log** | 保证持久性 | 数据页物理变更 | ib_logfile* | 崩溃恢复、事务持久性 |
| **Undo Log** | 事务回滚、MVCC | 事务前的旧数据 | 系统表空间 | 事务回滚、多版本控制 |

### Binlog（二进制日志）

**功能**：记录所有数据变更操作（INSERT、UPDATE、DELETE）

**格式类型**：
- **Statement**：记录SQL语句
- **Row**：记录行数据变更
- **Mixed**：自动选择合适格式

**主要用途**：
- 主从复制的数据源
- 数据恢复到指定时间点
- 数据审计和追踪

### Redo Log（重做日志）

**功能**：记录数据页的物理修改，保证事务持久性

**特点**：
- 固定大小的循环缓冲区
- 预写式日志（WAL）机制
- 事务提交前必须先写入Redo Log

**工作原理**：
1. 事务修改数据页时，先记录到Redo Log
2. 事务提交时，Redo Log持久化到磁盘
3. 数据页可以异步刷新到磁盘

### Undo Log（回滚日志）

**功能**：记录事务修改前的数据，支持事务回滚和MVCC

**主要作用**：
- **事务回滚**：撤销未提交事务的修改
- **MVCC**：为其他事务提供一致性读视图
- **崩溃恢复**：回滚未完成的事务

### 工作流程

1. **事务开始**：生成Undo Log记录原始数据
2. **数据修改**：记录变更到Redo Log
3. **事务提交**：
   - 写入Redo Log（保证持久性）
   - 写入Binlog（用于复制）
   - 两阶段提交确保一致性
4. **后台刷盘**：异步将数据页写入磁盘

### 面试要点

**Q: 为什么需要两阶段提交？**
A: 保证Redo Log和Binlog的一致性，避免主从数据不一致

**Q: Undo Log什么时候清理？**
A: 当没有事务需要该版本数据时，由purge线程清理

**Q: Redo Log写满了怎么办？**
A: 会阻塞写操作，等待checkpoint将脏页刷新到磁盘

**Q: Binlog和Redo Log的区别？**
A: Binlog是MySQL Server层的逻辑日志，Redo Log是InnoDB引擎层的物理日志
