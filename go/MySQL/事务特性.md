## MySQL事务特性与隔离级别

### 事务四大特性（ACID）

1. **原子性（Atomicity）**
   - 事务是不可分割的操作单元，要么全部成功，要么全部回滚
   - 实现：通过Undo Log实现回滚操作

2. **一致性（Consistency）**
   - 事务执行前后，数据库状态保持一致，满足完整性约束
   - 实现：通过约束（主键、外键、唯一性约束）和触发器

3. **隔离性（Isolation）**
   - 多个事务并发执行时，事务间相互隔离
   - 实现：通过锁机制或MVCC（多版本并发控制）

4. **持久性（Durability）**
   - 事务提交后，结果永久保存，系统崩溃也不丢失
   - 实现：通过Redo Log记录已提交事务的操作

### MySQL四种隔离级别

| 隔离级别 | 脏读 | 不可重复读 | 幻读 | 说明 |
|---------|------|-----------|------|------|
| 读未提交（Read Uncommitted） | ✓ | ✓ | ✓ | 可读取未提交数据 |
| 读已提交（Read Committed） | ✗ | ✓ | ✓ | 只读取已提交数据 |
| 可重复读（Repeatable Read） | ✗ | ✗ | ✓ | MySQL默认级别 |
| 可串行化（Serializable） | ✗ | ✗ | ✗ | 最高隔离级别 |

### 并发问题详解

**脏读**：读取到其他事务未提交的数据
**不可重复读**：同一事务中多次读取同一数据结果不同
**幻读**：同一事务中多次查询结果集行数不同

### MySQL如何解决幻读

MySQL在可重复读级别下通过**Next-Key Lock（间隙锁）**解决幻读：
- 锁住查询范围内的记录及其间隙
- 防止其他事务在间隙中插入新数据

### 面试要点

1. **默认隔离级别**：MySQL InnoDB默认使用可重复读
2. **MVCC机制**：通过版本链和ReadView实现非锁定读
3. **锁的类型**：记录锁、间隙锁、临键锁的区别和应用场景