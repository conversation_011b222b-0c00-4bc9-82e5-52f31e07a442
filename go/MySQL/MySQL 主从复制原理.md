## MySQL主从复制原理与实现

### 复制原理

MySQL主从复制通过三个核心组件实现：

1. **Binary Log（主库）**
   - 记录所有数据变更操作（INSERT、UPDATE、DELETE）
   - 按事务顺序记录，是复制的数据源

2. **I/O线程（从库）**
   - 连接主库，请求并拉取Binary Log
   - 将日志写入本地Relay Log（中继日志）

3. **SQL线程（从库）**
   - 读取Relay Log中的SQL语句
   - 在从库上重放这些操作，保持数据同步

### 复制模式

| 模式 | 特点 | 优缺点 |
|------|------|--------|
| **异步复制** | 主库不等待从库确认 | 性能好，但可能丢数据 |
| **半同步复制** | 主库等待至少一个从库确认 | 平衡性能和安全性 |
| **全同步复制** | 主库等待所有从库确认 | 最安全，但性能差 |

### 复制格式

1. **Statement-Based（SBR）**：复制SQL语句
2. **Row-Based（RBR）**：复制行数据变更
3. **Mixed-Based（MBR）**：自动选择合适格式

### 配置步骤

**1. 主库配置**
```ini
# my.cnf
[mysqld]
server-id=1
log-bin=mysql-bin
binlog-format=ROW
```

**2. 创建复制用户**
```sql
CREATE USER 'repl'@'%' IDENTIFIED BY 'password';
GRANT REPLICATION SLAVE ON *.* TO 'repl'@'%';
SHOW MASTER STATUS; -- 记录File和Position
```

**3. 从库配置**
```ini
# my.cnf
[mysqld]
server-id=2
```

**4. 启动复制**
```sql
CHANGE MASTER TO
    MASTER_HOST='主库IP',
    MASTER_USER='repl',
    MASTER_PASSWORD='password',
    MASTER_LOG_FILE='mysql-bin.000001',
    MASTER_LOG_POS=154;
START SLAVE;
```

**5. 检查状态**
```sql
SHOW SLAVE STATUS\G;
-- 确认 Slave_IO_Running 和 Slave_SQL_Running 都是 Yes
```

### 优势与问题

**优势**
- 读写分离，提高并发能力
- 数据备份和容灾
- 负载均衡

**常见问题**
- **复制延迟**：从库数据滞后于主库
- **主库故障**：需要手动或自动故障转移
- **数据不一致**：异步复制可能丢失数据

### 面试要点

1. **复制原理**：Binary Log → I/O线程 → Relay Log → SQL线程
2. **复制模式**：异步、半同步、全同步的区别
3. **延迟监控**：通过`SHOW SLAVE STATUS`查看`Seconds_Behind_Master`
4. **故障处理**：主库宕机后的切换策略