## MySQL高性能索引策略

### 索引数据结构

#### B+Tree特点
- **内节点**：只存储键值，不存储数据
- **叶子节点**：存储所有数据，通过链表连接
- **平衡性**：所有叶子节点深度相同，查询效率O(log n)
- **范围查询**：叶子节点链表结构支持高效范围查询

#### 存储引擎差异

**MyISAM索引**
- 主索引和辅助索引都存储数据记录的物理地址
- 索引文件与数据文件分离

**InnoDB索引**
- 聚集索引：主键索引，叶节点存储完整数据记录
- 辅助索引：叶节点存储主键值，需要回表查询

### 核心优化策略

#### 1. 最左前缀原则
联合索引`(a, b, c)`的使用规则：
- ✅ `WHERE a = 1`
- ✅ `WHERE a = 1 AND b = 2`
- ✅ `WHERE a = 1 AND b = 2 AND c = 3`
- ❌ `WHERE b = 2` (跳过最左列)
- ❌ `WHERE a = 1 AND c = 3` (跳过中间列)

#### 2. 覆盖索引
查询的所有字段都包含在索引中，避免回表操作：
```sql
-- 创建覆盖索引
CREATE INDEX idx_customer_cover ON orders(customer_id, order_date, total_amount);
-- 查询只访问索引，不需要回表
SELECT customer_id, order_date, total_amount FROM orders WHERE customer_id = 123;
```

#### 3. 前缀索引
对长字符串字段使用前缀索引：
```sql
-- 分析前缀长度选择性
SELECT COUNT(DISTINCT LEFT(email, 10)) / COUNT(*) as selectivity FROM users;
-- 创建前缀索引
CREATE INDEX idx_email_prefix ON users(email(10));
```

### 索引失效场景

| 场景 | 失效示例 | 优化方案 |
|------|----------|----------|
| 函数操作 | `WHERE YEAR(date) = 2024` | `WHERE date >= '2024-01-01' AND date < '2025-01-01'` |
| 类型转换 | `WHERE phone = 123` (phone是varchar) | `WHERE phone = '123'` |
| 通配符开头 | `WHERE name LIKE '%John%'` | `WHERE name LIKE 'John%'` |
| 不等于操作 | `WHERE status != 'deleted'` | `WHERE status IN ('active', 'pending')` |

### 索引设计原则

1. **选择性高的列优先**：区分度高的字段建索引效果更好
2. **最左前缀匹配**：联合索引按查询频率和选择性排序
3. **避免过多索引**：每个索引都有维护成本
4. **监控索引使用**：定期检查未使用的索引

### 面试要点

**Q: 什么时候不建索引？**
A: 小表、频繁更新的列、低选择性列、临时表

**Q: 如何判断索引有效？**
A: 使用EXPLAIN分析执行计划，监控慢查询日志

**Q: 联合索引顺序如何确定？**
A: 遵循最左前缀原则，选择性高的列优先，范围查询列放最后