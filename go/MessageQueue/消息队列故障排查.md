# 消息队列故障排查

## 1. 常见问题分类

### 消息丢失
- **生产者端丢失**：网络异常、服务重启
- **服务器端丢失**：磁盘故障、配置错误
- **消费者端丢失**：处理异常、自动确认

### 消息重复
- **网络重传**：网络不稳定导致重复发送
- **消费者重启**：未确认消息重新投递
- **分区重平衡**：Kafka消费者组重平衡

### 消息积压
- **生产速度 > 消费速度**：消费者处理能力不足
- **消费者故障**：消费者宕机或处理异常
- **网络问题**：消费者无法正常拉取消息

### 消息乱序
- **并发消费**：多个消费者并行处理
- **重试机制**：失败消息重试打乱顺序
- **分区策略**：消息分配到不同分区

## 2. 排查工具和方法

### Kafka监控
```bash
# 查看Topic信息
kafka-topics.sh --describe --topic my-topic --bootstrap-server localhost:9092

# 查看消费者组状态
kafka-consumer-groups.sh --describe --group my-group --bootstrap-server localhost:9092

# 查看分区详情
kafka-log-dirs.sh --describe --bootstrap-server localhost:9092 --json
```

### 日志分析
```go
// 生产者日志
func logProducerMetrics() {
    metrics := producer.GetMetrics()
    log.Printf("Messages sent: %d", metrics.MessagesSent)
    log.Printf("Send errors: %d", metrics.SendErrors)
    log.Printf("Retry count: %d", metrics.RetryCount)
}

// 消费者日志
func logConsumerMetrics() {
    metrics := consumer.GetMetrics()
    log.Printf("Messages consumed: %d", metrics.MessagesConsumed)
    log.Printf("Processing errors: %d", metrics.ProcessingErrors)
    log.Printf("Lag: %d", metrics.ConsumerLag)
}
```

### 性能监控
```go
// 监控指标
var (
    messageLatency = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "message_processing_latency_seconds",
            Help: "Message processing latency",
        },
        []string{"topic", "partition"},
    )
    
    queueDepth = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "queue_depth",
            Help: "Current queue depth",
        },
        []string{"queue"},
    )
)
```

## 3. 具体问题排查

### 消息丢失排查
```go
// 1. 检查生产者配置
func checkProducerConfig() {
    config := kafka.ConfigMap{
        "acks": "all", // 等待所有副本确认
        "retries": 3,  // 重试次数
        "enable.idempotence": true, // 启用幂等性
    }
}

// 2. 检查消费者确认
func reliableConsume() {
    consumer.Subscribe("my-topic", nil)
    
    for {
        msg, err := consumer.ReadMessage(-1)
        if err != nil {
            log.Printf("Consumer error: %v", err)
            continue
        }
        
        // 处理消息
        if processMessage(msg) {
            // 手动提交offset
            consumer.CommitMessage(msg)
        }
    }
}

// 3. 检查服务器配置
func checkBrokerConfig() {
    // min.insync.replicas >= 2
    // unclean.leader.election.enable = false
    // log.flush.interval.messages = 1
}
```

### 消息积压排查
```go
// 1. 监控消费延迟
func monitorConsumerLag() {
    adminClient := kafka.NewAdminClient(&kafka.ConfigMap{
        "bootstrap.servers": "localhost:9092",
    })
    
    groups, _ := adminClient.ListConsumerGroups(context.Background())
    for _, group := range groups {
        offsets, _ := adminClient.ListConsumerGroupOffsets(
            context.Background(), group.GroupID)
        
        for topic, partitions := range offsets {
            for partition, offset := range partitions {
                highWaterMark := getHighWaterMark(topic, partition)
                lag := highWaterMark - offset.Offset
                
                if lag > 1000 { // 积压超过1000条
                    log.Printf("High lag detected: topic=%s, partition=%d, lag=%d",
                        topic, partition, lag)
                }
            }
        }
    }
}

// 2. 动态扩容消费者
func scaleConsumers(currentLag int64) {
    if currentLag > 10000 {
        // 启动更多消费者实例
        for i := 0; i < 3; i++ {
            go startConsumer(fmt.Sprintf("consumer-%d", i))
        }
    }
}
```

### 消息重复排查
```go
// 1. 实现幂等性
func idempotentProcessor() {
    processed := make(map[string]bool)
    var mu sync.RWMutex
    
    return func(message *kafka.Message) error {
        messageID := string(message.Key)
        
        mu.RLock()
        if processed[messageID] {
            mu.RUnlock()
            return nil // 已处理，跳过
        }
        mu.RUnlock()
        
        // 处理消息
        err := processMessage(message)
        if err == nil {
            mu.Lock()
            processed[messageID] = true
            mu.Unlock()
        }
        
        return err
    }
}

// 2. 检查重复原因
func analyzeduplicates() {
    // 检查网络稳定性
    // 检查消费者组配置
    // 检查自动提交设置
}
```

## 4. 性能优化

### 生产者优化
```go
func optimizeProducer() *kafka.Producer {
    config := &kafka.ConfigMap{
        "bootstrap.servers": "localhost:9092",
        "batch.size": 16384,        // 批量大小
        "linger.ms": 5,             // 等待时间
        "compression.type": "lz4",   // 压缩算法
        "buffer.memory": 33554432,   // 缓冲区大小
        "max.in.flight.requests.per.connection": 5,
    }
    
    producer, _ := kafka.NewProducer(config)
    return producer
}
```

### 消费者优化
```go
func optimizeConsumer() *kafka.Consumer {
    config := &kafka.ConfigMap{
        "bootstrap.servers": "localhost:9092",
        "group.id": "my-group",
        "fetch.min.bytes": 1024,     // 最小拉取字节数
        "fetch.max.wait.ms": 500,    // 最大等待时间
        "max.partition.fetch.bytes": 1048576, // 最大拉取字节数
        "enable.auto.commit": false,  // 手动提交
    }
    
    consumer, _ := kafka.NewConsumer(config)
    return consumer
}
```

### 集群优化
```bash
# 服务器配置优化
num.network.threads=8
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
num.replica.fetchers=4

# JVM参数优化
-Xmx6g -Xms6g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=20
-XX:InitiatingHeapOccupancyPercent=35
```

## 5. 告警和监控

### 关键指标
```go
type MetricsCollector struct {
    MessagesSent     prometheus.Counter
    MessagesConsumed prometheus.Counter
    ProcessingErrors prometheus.Counter
    ConsumerLag      prometheus.Gauge
    ProcessingTime   prometheus.Histogram
}

func (m *MetricsCollector) CollectMetrics() {
    // 收集生产者指标
    m.MessagesSent.Add(float64(getMessagesSent()))
    
    // 收集消费者指标
    m.MessagesConsumed.Add(float64(getMessagesConsumed()))
    m.ConsumerLag.Set(float64(getConsumerLag()))
    
    // 收集错误指标
    m.ProcessingErrors.Add(float64(getProcessingErrors()))
}
```

### 告警规则
```yaml
# Prometheus告警规则
groups:
- name: kafka.rules
  rules:
  - alert: KafkaConsumerLag
    expr: kafka_consumer_lag > 1000
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Kafka consumer lag is high"
      
  - alert: KafkaProducerErrors
    expr: rate(kafka_producer_errors_total[5m]) > 0.1
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "High producer error rate"
```

## 6. 面试要点

### 故障排查思路
1. **定位问题范围**：生产者、服务器、消费者
2. **收集监控数据**：日志、指标、配置
3. **分析根本原因**：网络、配置、代码逻辑
4. **制定解决方案**：临时措施、根本解决

### 常见问题处理
- **消息丢失**：检查ACK配置、持久化设置、确认机制
- **消息积压**：扩容消费者、优化处理逻辑、调整分区
- **消息重复**：实现幂等性、检查网络稳定性
- **性能问题**：调整批量参数、优化序列化、硬件升级

### 预防措施
- **完善监控**：关键指标监控、告警机制
- **容量规划**：评估峰值流量、预留资源
- **故障演练**：定期进行故障模拟和恢复演练
- **文档维护**：更新运维手册、故障处理流程

### 一句话总结
> 消息队列故障排查需要系统性思维，通过监控数据定位问题，结合配置优化和代码改进解决根本原因
