# 消息队列对比与选型

## 主流消息队列对比

### 1. Kafka
**特点**：高吞吐量、分布式、持久化
**适用场景**：大数据处理、日志收集、实时流处理

```go
// Kafka生产者示例
producer, err := sarama.NewSyncProducer([]string{"localhost:9092"}, nil)
if err != nil {
    log.Fatal(err)
}
defer producer.Close()

msg := &sarama.ProducerMessage{
    Topic: "test-topic",
    Value: sarama.StringEncoder("Hello Kafka"),
}

partition, offset, err := producer.SendMessage(msg)
```

**优势**：
- 高吞吐量（百万级TPS）
- 水平扩展能力强
- 消息持久化存储
- 支持多种消费模式

**劣势**：
- 运维复杂度高
- 消息延迟相对较高
- 不支持消息优先级

### 2. RabbitMQ
**特点**：功能丰富、可靠性高、支持多种协议
**适用场景**：企业级应用、复杂路由、事务处理

```go
// RabbitMQ生产者示例
conn, err := amqp.Dial("amqp://guest:guest@localhost:5672/")
if err != nil {
    log.Fatal(err)
}
defer conn.Close()

ch, err := conn.Channel()
if err != nil {
    log.Fatal(err)
}
defer ch.Close()

err = ch.Publish(
    "",           // exchange
    "task_queue", // routing key
    false,        // mandatory
    false,        // immediate
    amqp.Publishing{
        ContentType: "text/plain",
        Body:        []byte("Hello RabbitMQ"),
    })
```

**优势**：
- 功能丰富（路由、交换机、队列）
- 消息可靠性高
- 支持事务和确认机制
- 管理界面友好

**劣势**：
- 吞吐量相对较低
- 集群配置复杂
- 内存消耗较大

### 3. Redis Stream
**特点**：轻量级、高性能、支持持久化
**适用场景**：轻量级消息队列、缓存场景

```go
// Redis Stream示例
rdb := redis.NewClient(&redis.Options{
    Addr: "localhost:6379",
})

// 发送消息
err := rdb.XAdd(ctx, &redis.XAddArgs{
    Stream: "mystream",
    Values: map[string]interface{}{
        "message": "Hello Redis Stream",
    },
}).Err()

// 消费消息
msgs, err := rdb.XRead(ctx, &redis.XReadArgs{
    Streams: []string{"mystream", "$"},
    Block:   0,
}).Result()
```

**优势**：
- 性能优秀
- 部署简单
- 支持消费者组
- 内存占用小

**劣势**：
- 功能相对简单
- 集群方案复杂
- 持久化依赖配置

### 4. NSQ
**特点**：去中心化、简单易用、高可用
**适用场景**：微服务通信、实时消息推送

```go
// NSQ生产者示例
producer, err := nsq.NewProducer("127.0.0.1:4150", nsq.NewConfig())
if err != nil {
    log.Fatal(err)
}

err = producer.Publish("test-topic", []byte("Hello NSQ"))
if err != nil {
    log.Fatal(err)
}
```

**优势**：
- 去中心化架构
- 部署运维简单
- 自动负载均衡
- 内置监控

**劣势**：
- 功能相对简单
- 社区相对较小
- 消息顺序性保证有限

## 选型对比表

| 特性 | Kafka | RabbitMQ | Redis Stream | NSQ |
|------|-------|----------|--------------|-----|
| 吞吐量 | 极高 | 中等 | 高 | 高 |
| 延迟 | 中等 | 低 | 低 | 低 |
| 可靠性 | 高 | 极高 | 中等 | 高 |
| 扩展性 | 极好 | 好 | 中等 | 好 |
| 运维复杂度 | 高 | 中等 | 低 | 低 |
| 消息顺序 | 分区有序 | 支持 | 支持 | 有限 |
| 持久化 | 支持 | 支持 | 支持 | 支持 |

## 消息队列核心概念

### 1. 消息可靠性
**At Most Once**：最多一次，可能丢失
**At Least Once**：至少一次，可能重复
**Exactly Once**：精确一次，理想状态

### 2. 消息顺序性
**全局有序**：所有消息严格有序
**分区有序**：同一分区内消息有序
**无序**：不保证消息顺序

### 3. 消费模式
**Push模式**：服务端主动推送消息
**Pull模式**：客户端主动拉取消息

## 高可用设计

### 1. 集群部署
```yaml
# Kafka集群配置示例
server.1=kafka1:2888:3888
server.2=kafka2:2888:3888
server.3=kafka3:2888:3888
```

### 2. 数据复制
- **同步复制**：强一致性，性能较低
- **异步复制**：高性能，可能丢失数据
- **半同步复制**：平衡性能和一致性

### 3. 故障转移
- **主从切换**：自动故障检测和切换
- **负载均衡**：请求分发到健康节点
- **数据恢复**：从副本恢复数据

## 性能优化

### 1. 生产者优化
```go
// 批量发送
config := sarama.NewConfig()
config.Producer.Flush.Messages = 100
config.Producer.Flush.Frequency = 10 * time.Millisecond

// 压缩
config.Producer.Compression = sarama.CompressionSnappy

// 异步发送
producer, err := sarama.NewAsyncProducer(brokers, config)
```

### 2. 消费者优化
```go
// 批量消费
config := sarama.NewConfig()
config.Consumer.Fetch.Min = 1024
config.Consumer.Fetch.Default = 1024 * 1024

// 并发消费
for i := 0; i < numWorkers; i++ {
    go func() {
        for message := range consumer.Messages() {
            processMessage(message)
        }
    }()
}
```

### 3. 存储优化
- **分区策略**：合理设置分区数
- **压缩算法**：选择合适的压缩方式
- **清理策略**：设置合理的数据保留时间

## 面试要点
1. **选型依据**：根据业务场景选择合适的消息队列
2. **可靠性保证**：消息不丢失、不重复的实现方案
3. **性能优化**：提高吞吐量和降低延迟的方法
4. **高可用设计**：集群部署、故障转移、数据复制
5. **监控运维**：关键指标监控和故障排查
