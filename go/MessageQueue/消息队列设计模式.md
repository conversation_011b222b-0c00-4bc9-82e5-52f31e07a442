# 消息队列设计模式

## 1. 基础模式

### 点对点模式（Point-to-Point）
- **特点**：一个生产者，一个消费者
- **场景**：任务分发、命令处理
- **实现**：队列保证消息只被消费一次

```go
// 生产者
func sendTask(queue string, task Task) {
    message := serialize(task)
    mq.Send(queue, message)
}

// 消费者
func processTask(queue string) {
    message := mq.Receive(queue)
    task := deserialize(message)
    handleTask(task)
}
```

### 发布订阅模式（Pub/Sub）
- **特点**：一个生产者，多个消费者
- **场景**：事件通知、数据广播
- **实现**：Topic机制，消息被多个订阅者接收

```go
// 发布者
func publishEvent(topic string, event Event) {
    message := serialize(event)
    mq.Publish(topic, message)
}

// 订阅者
func subscribeEvent(topic string, handler func(Event)) {
    mq.Subscribe(topic, func(message []byte) {
        event := deserialize(message)
        handler(event)
    })
}
```

## 2. 高级模式

### 请求响应模式（Request-Reply）
- **特点**：异步请求，等待响应
- **场景**：RPC调用、服务间通信
- **实现**：使用临时队列或correlation ID

```go
func asyncRequest(request Request) Response {
    correlationID := generateID()
    replyQueue := "reply_" + correlationID
    
    // 发送请求
    mq.Send("request_queue", Message{
        Body: serialize(request),
        ReplyTo: replyQueue,
        CorrelationID: correlationID,
    })
    
    // 等待响应
    response := mq.Receive(replyQueue)
    return deserialize(response.Body)
}
```

### 工作队列模式（Work Queue）
- **特点**：多个消费者竞争消费
- **场景**：任务分发、负载均衡
- **实现**：轮询分发或能力分发

```go
func startWorkers(queueName string, workerCount int) {
    for i := 0; i < workerCount; i++ {
        go func(workerID int) {
            for {
                task := mq.Receive(queueName)
                processTask(task, workerID)
                mq.Ack(task) // 确认处理完成
            }
        }(i)
    }
}
```

### 路由模式（Routing）
- **特点**：根据路由键分发消息
- **场景**：日志分级、消息分类
- **实现**：Exchange + Routing Key

```go
func routeMessage(exchange string, routingKey string, message []byte) {
    mq.PublishWithRouting(exchange, routingKey, message)
}

// 消费者绑定特定路由键
func bindQueue(queueName, exchange, routingKey string) {
    mq.BindQueue(queueName, exchange, routingKey)
}
```

## 3. 可靠性模式

### 消息确认模式
- **生产者确认**：确保消息成功发送
- **消费者确认**：确保消息成功处理
- **实现**：ACK/NACK机制

```go
func reliableSend(queue string, message []byte) error {
    return mq.SendWithConfirm(queue, message, func(ack bool) {
        if !ack {
            // 重试或记录失败
            log.Error("Message send failed")
        }
    })
}

func reliableConsume(queue string) {
    mq.ConsumeWithAck(queue, func(message Message) {
        if processMessage(message) {
            mq.Ack(message)
        } else {
            mq.Nack(message, true) // 重新入队
        }
    })
}
```

### 死信队列模式
- **目的**：处理无法消费的消息
- **场景**：消息重试失败、格式错误
- **实现**：TTL + DLX或重试计数

```go
func setupDeadLetterQueue() {
    // 主队列配置
    mq.DeclareQueue("main_queue", QueueOptions{
        TTL: 30000, // 30秒TTL
        DeadLetterExchange: "dlx",
        DeadLetterRoutingKey: "failed",
    })
    
    // 死信队列
    mq.DeclareQueue("dead_letter_queue", QueueOptions{})
    mq.BindQueue("dead_letter_queue", "dlx", "failed")
}
```

### 幂等性模式
- **目的**：防止消息重复处理
- **实现**：消息去重、业务幂等

```go
var processedMessages = sync.Map{}

func idempotentProcess(message Message) {
    messageID := message.ID
    
    // 检查是否已处理
    if _, exists := processedMessages.Load(messageID); exists {
        return // 已处理，直接返回
    }
    
    // 处理消息
    if processMessage(message) {
        processedMessages.Store(messageID, true)
    }
}
```

## 4. 性能模式

### 批量处理模式
- **目的**：提高处理效率
- **实现**：批量发送、批量消费

```go
func batchSend(queue string, messages []Message) {
    batch := make([]Message, 0, 100)
    
    for _, msg := range messages {
        batch = append(batch, msg)
        
        if len(batch) >= 100 {
            mq.SendBatch(queue, batch)
            batch = batch[:0]
        }
    }
    
    // 发送剩余消息
    if len(batch) > 0 {
        mq.SendBatch(queue, batch)
    }
}
```

### 预取模式
- **目的**：减少网络往返
- **实现**：设置prefetch count

```go
func optimizedConsumer(queue string) {
    mq.SetPrefetchCount(10) // 预取10条消息
    
    mq.Consume(queue, func(messages []Message) {
        for _, msg := range messages {
            processMessage(msg)
        }
    })
}
```

### 分区模式
- **目的**：并行处理，提高吞吐量
- **实现**：按key分区

```go
func partitionedSend(topic string, key string, message []byte) {
    partition := hash(key) % partitionCount
    mq.SendToPartition(topic, partition, message)
}
```

## 5. 监控模式

### 健康检查模式
```go
func healthCheck() bool {
    // 检查连接状态
    if !mq.IsConnected() {
        return false
    }
    
    // 检查队列状态
    queueInfo := mq.GetQueueInfo("health_check")
    return queueInfo.ConsumerCount > 0
}
```

### 指标收集模式
```go
var (
    messagesSent = prometheus.NewCounterVec(
        prometheus.CounterOpts{Name: "messages_sent_total"},
        []string{"queue"},
    )
    
    processingDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{Name: "message_processing_duration_seconds"},
        []string{"queue"},
    )
)

func instrumentedProcess(queue string, message Message) {
    start := time.Now()
    defer func() {
        processingDuration.WithLabelValues(queue).Observe(time.Since(start).Seconds())
    }()
    
    processMessage(message)
    messagesSent.WithLabelValues(queue).Inc()
}
```

## 6. 面试要点

### 设计模式选择
1. **点对点 vs 发布订阅？**
   - 根据消费者数量和消息处理需求选择

2. **如何保证消息不丢失？**
   - 生产者确认 + 消息持久化 + 消费者确认

3. **如何处理消息重复？**
   - 幂等性设计 + 消息去重

4. **如何提高处理性能？**
   - 批量处理 + 并行消费 + 预取优化

### 最佳实践
- **合理设置队列参数**：TTL、最大长度、死信队列
- **监控关键指标**：消息积压、处理延迟、错误率
- **优雅关闭**：确保消息处理完成后再关闭
- **错误处理**：重试机制 + 死信队列 + 告警

### 常见陷阱
- **忘记消息确认**：导致消息重复处理
- **死循环重试**：没有设置最大重试次数
- **内存泄漏**：消息积压导致内存耗尽
- **顺序问题**：并行消费破坏消息顺序

### 一句话总结
> 消息队列设计模式通过不同的消息传递和处理策略，解决分布式系统中的解耦、可靠性和性能问题
