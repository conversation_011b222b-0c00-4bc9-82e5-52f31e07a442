# RabbitMQ消息队列详解

## 1. RabbitMQ核心概念

### 基本组件
- **Producer**：消息生产者，发送消息
- **Consumer**：消息消费者，接收消息
- **Queue**：消息队列，存储消息
- **Exchange**：交换机，路由消息
- **Binding**：绑定，连接Exchange和Queue
- **Routing Key**：路由键，消息路由规则

### AMQP协议
- **Advanced Message Queuing Protocol**：高级消息队列协议
- **特点**：可靠性、安全性、互操作性
- **消息确认机制**：确保消息不丢失
- **事务支持**：保证消息的原子性

## 2. Exchange类型

### Direct Exchange
```go
// 直接交换机 - 精确匹配路由键
err := channel.ExchangeDeclare(
    "direct_exchange", // 交换机名称
    "direct",          // 交换机类型
    true,              // 持久化
    false,             // 自动删除
    false,             // 内部使用
    false,             // 等待服务器确认
    nil,               // 额外参数
)

// 绑定队列
err = channel.QueueBind(
    "user_queue",      // 队列名称
    "user.created",    // 路由键
    "direct_exchange", // 交换机名称
    false,             // 等待服务器确认
    nil,               // 额外参数
)
```

### Topic Exchange
```go
// 主题交换机 - 模式匹配路由键
err := channel.ExchangeDeclare(
    "topic_exchange",
    "topic",
    true,
    false,
    false,
    false,
    nil,
)

// 绑定模式：*.user.* 匹配 create.user.profile
err = channel.QueueBind(
    "user_queue",
    "*.user.*",
    "topic_exchange",
    false,
    nil,
)
```

### Fanout Exchange
```go
// 扇出交换机 - 广播消息
err := channel.ExchangeDeclare(
    "fanout_exchange",
    "fanout",
    true,
    false,
    false,
    false,
    nil,
)

// 绑定时不需要路由键
err = channel.QueueBind(
    "notification_queue",
    "",                // 空路由键
    "fanout_exchange",
    false,
    nil,
)
```

### Headers Exchange
```go
// 头部交换机 - 基于消息头路由
err := channel.ExchangeDeclare(
    "headers_exchange",
    "headers",
    true,
    false,
    false,
    false,
    nil,
)

// 绑定时使用headers参数
headers := amqp.Table{
    "x-match": "all",  // all或any
    "type":    "image",
    "format":  "jpeg",
}

err = channel.QueueBind(
    "image_queue",
    "",
    "headers_exchange",
    false,
    headers,
)
```

## 3. 消息可靠性保证

### 消息持久化
```go
// 声明持久化队列
queue, err := channel.QueueDeclare(
    "persistent_queue",
    true,  // 持久化
    false, // 非自动删除
    false, // 非独占
    false, // 等待服务器确认
    nil,
)

// 发送持久化消息
err = channel.Publish(
    "",           // 交换机
    queue.Name,   // 路由键
    false,        // 强制发送
    false,        // 立即发送
    amqp.Publishing{
        DeliveryMode: amqp.Persistent, // 消息持久化
        ContentType:  "text/plain",
        Body:         []byte("Hello World"),
    },
)
```

### 发布确认
```go
// 启用发布确认模式
err := channel.Confirm(false)
if err != nil {
    log.Fatal(err)
}

// 发送消息
err = channel.Publish(
    "exchange",
    "routing.key",
    false,
    false,
    amqp.Publishing{
        ContentType: "text/plain",
        Body:        []byte("message"),
    },
)

// 等待确认
confirmed := channel.NotifyPublish(make(chan amqp.Confirmation, 1))
select {
case confirm := <-confirmed:
    if confirm.Ack {
        log.Println("Message confirmed")
    } else {
        log.Println("Message rejected")
    }
case <-time.After(5 * time.Second):
    log.Println("Confirmation timeout")
}
```

### 消费者确认
```go
// 手动确认模式
msgs, err := channel.Consume(
    queue.Name,
    "",    // 消费者标签
    false, // 手动确认
    false, // 非独占
    false, // 不等待服务器确认
    false, // 额外参数
    nil,
)

for msg := range msgs {
    // 处理消息
    err := processMessage(msg.Body)
    
    if err != nil {
        // 处理失败，拒绝消息并重新入队
        msg.Nack(false, true)
    } else {
        // 处理成功，确认消息
        msg.Ack(false)
    }
}
```

## 4. 高级特性

### 死信队列（DLX）
```go
// 声明主队列，配置死信交换机
args := amqp.Table{
    "x-dead-letter-exchange":    "dlx_exchange",
    "x-dead-letter-routing-key": "failed",
    "x-message-ttl":             30000, // 30秒TTL
    "x-max-length":              1000,  // 最大长度
}

queue, err := channel.QueueDeclare(
    "main_queue",
    true,
    false,
    false,
    false,
    args,
)

// 声明死信交换机和队列
err = channel.ExchangeDeclare("dlx_exchange", "direct", true, false, false, false, nil)
dlxQueue, err := channel.QueueDeclare("dlx_queue", true, false, false, false, nil)
err = channel.QueueBind(dlxQueue.Name, "failed", "dlx_exchange", false, nil)
```

### 延迟队列
```go
// 使用TTL + DLX实现延迟队列
func SendDelayedMessage(channel *amqp.Channel, message string, delay time.Duration) error {
    // 创建临时队列，设置TTL和死信交换机
    args := amqp.Table{
        "x-message-ttl":             int64(delay / time.Millisecond),
        "x-dead-letter-exchange":    "delayed_exchange",
        "x-dead-letter-routing-key": "process",
    }
    
    tempQueue, err := channel.QueueDeclare(
        "",    // 临时队列名
        false, // 非持久化
        true,  // 自动删除
        true,  // 独占
        false,
        args,
    )
    
    // 发送消息到临时队列
    return channel.Publish(
        "",
        tempQueue.Name,
        false,
        false,
        amqp.Publishing{
            ContentType: "text/plain",
            Body:        []byte(message),
        },
    )
}
```

### 优先级队列
```go
// 声明优先级队列
args := amqp.Table{
    "x-max-priority": 10, // 最大优先级
}

queue, err := channel.QueueDeclare(
    "priority_queue",
    true,
    false,
    false,
    false,
    args,
)

// 发送带优先级的消息
err = channel.Publish(
    "",
    queue.Name,
    false,
    false,
    amqp.Publishing{
        ContentType: "text/plain",
        Body:        []byte("High priority message"),
        Priority:    8, // 优先级
    },
)
```

## 5. 集群与高可用

### 集群配置
```bash
# 节点1
rabbitmq-server -detached
rabbitmqctl stop_app
rabbitmqctl reset
rabbitmqctl start_app

# 节点2
rabbitmq-server -detached
rabbitmqctl stop_app
rabbitmqctl reset
rabbitmqctl join_cluster rabbit@node1
rabbitmqctl start_app

# 查看集群状态
rabbitmqctl cluster_status
```

### 镜像队列
```bash
# 设置镜像队列策略
rabbitmqctl set_policy ha-all "^ha\." '{"ha-mode":"all","ha-sync-mode":"automatic"}'

# 设置镜像队列数量
rabbitmqctl set_policy ha-two "^ha\." '{"ha-mode":"exactly","ha-params":2,"ha-sync-mode":"automatic"}'
```

## 6. 性能优化

### 连接池管理
```go
type ConnectionPool struct {
    connections chan *amqp.Connection
    factory     func() (*amqp.Connection, error)
    maxSize     int
}

func NewConnectionPool(maxSize int, factory func() (*amqp.Connection, error)) *ConnectionPool {
    return &ConnectionPool{
        connections: make(chan *amqp.Connection, maxSize),
        factory:     factory,
        maxSize:     maxSize,
    }
}

func (p *ConnectionPool) Get() (*amqp.Connection, error) {
    select {
    case conn := <-p.connections:
        if conn.IsClosed() {
            return p.factory()
        }
        return conn, nil
    default:
        return p.factory()
    }
}

func (p *ConnectionPool) Put(conn *amqp.Connection) {
    if !conn.IsClosed() {
        select {
        case p.connections <- conn:
        default:
            conn.Close()
        }
    }
}
```

### 批量操作
```go
// 批量发送消息
func BatchPublish(channel *amqp.Channel, messages []string) error {
    for _, message := range messages {
        err := channel.Publish(
            "exchange",
            "routing.key",
            false,
            false,
            amqp.Publishing{
                ContentType: "text/plain",
                Body:        []byte(message),
            },
        )
        if err != nil {
            return err
        }
    }
    return nil
}
```

## 7. 监控与管理

### 管理界面
- **Web管理界面**：http://localhost:15672
- **默认用户**：guest/guest
- **功能**：队列管理、交换机管理、连接监控

### 监控指标
```bash
# 查看队列状态
rabbitmqctl list_queues name messages consumers

# 查看交换机状态
rabbitmqctl list_exchanges name type

# 查看连接状态
rabbitmqctl list_connections

# 查看内存使用
rabbitmqctl status
```

## 8. 面试要点

### 核心问题
1. **RabbitMQ的Exchange类型及使用场景？**
   - Direct：精确匹配，点对点通信
   - Topic：模式匹配，发布订阅
   - Fanout：广播，消息通知
   - Headers：基于消息头路由

2. **如何保证消息不丢失？**
   - 消息持久化、队列持久化
   - 发布确认、消费者确认
   - 集群镜像队列

3. **死信队列的作用？**
   - 处理失败消息、消息过期
   - 实现延迟队列、重试机制

4. **RabbitMQ vs Kafka的区别？**
   - RabbitMQ：低延迟、复杂路由、事务支持
   - Kafka：高吞吐、持久化、流处理

### 实践经验
- **消息设计**：幂等性、顺序性
- **性能调优**：连接池、批量操作
- **故障处理**：监控告警、容灾恢复
- **运维管理**：集群部署、版本升级

### 一句话总结
> RabbitMQ是基于AMQP协议的消息中间件，通过Exchange路由机制实现灵活的消息分发和可靠的消息传递
