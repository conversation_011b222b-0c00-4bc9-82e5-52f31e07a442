# Context并发控制详解

## Context基本概念

Context是Go语言中用于跨goroutine传递取消信号、超时信息和请求范围数据的标准方式。

### 核心接口
```go
type Context interface {
    Deadline() (deadline time.Time, ok bool)
    Done() <-chan struct{}
    Err() error
    Value(key interface{}) interface{}
}
```

## Context类型

### 1. Background和TODO
```go
// 根Context，通常用于main函数、初始化和测试
ctx := context.Background()

// 当不确定使用哪个Context时的占位符
ctx := context.TODO()
```

### 2. WithCancel - 取消控制
```go
func WithCancelExample() {
    ctx, cancel := context.WithCancel(context.Background())
    defer cancel() // 确保资源释放
    
    go func() {
        select {
        case <-time.After(5 * time.Second):
            cancel() // 5秒后取消
        }
    }()
    
    select {
    case <-ctx.Done():
        fmt.Println("操作被取消:", ctx.Err())
    case <-time.After(10 * time.Second):
        fmt.Println("操作超时")
    }
}
```

### 3. WithTimeout - 超时控制
```go
func WithTimeoutExample() {
    ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
    defer cancel()
    
    result := make(chan string, 1)
    go func() {
        // 模拟耗时操作
        time.Sleep(5 * time.Second)
        result <- "操作完成"
    }()
    
    select {
    case res := <-result:
        fmt.Println(res)
    case <-ctx.Done():
        fmt.Println("操作超时:", ctx.Err())
    }
}
```

### 4. WithDeadline - 截止时间控制
```go
func WithDeadlineExample() {
    deadline := time.Now().Add(2 * time.Second)
    ctx, cancel := context.WithDeadline(context.Background(), deadline)
    defer cancel()
    
    select {
    case <-time.After(3 * time.Second):
        fmt.Println("操作完成")
    case <-ctx.Done():
        fmt.Println("到达截止时间:", ctx.Err())
    }
}
```

### 5. WithValue - 传递数据
```go
type key string

func WithValueExample() {
    ctx := context.WithValue(context.Background(), key("userID"), "12345")
    
    processRequest(ctx)
}

func processRequest(ctx context.Context) {
    userID := ctx.Value(key("userID")).(string)
    fmt.Printf("处理用户 %s 的请求\n", userID)
}
```

## 实际应用场景

### 1. HTTP请求处理
```go
func httpHandler(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    
    // 设置处理超时
    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    result, err := processWithContext(ctx)
    if err != nil {
        if err == context.DeadlineExceeded {
            http.Error(w, "请求超时", http.StatusRequestTimeout)
            return
        }
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    w.Write([]byte(result))
}

func processWithContext(ctx context.Context) (string, error) {
    select {
    case <-time.After(3 * time.Second):
        return "处理完成", nil
    case <-ctx.Done():
        return "", ctx.Err()
    }
}
```

### 2. 数据库操作
```go
func queryWithContext(ctx context.Context, db *sql.DB, query string) (*sql.Rows, error) {
    // 使用Context控制查询超时
    return db.QueryContext(ctx, query)
}

func databaseExample() {
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()
    
    rows, err := queryWithContext(ctx, db, "SELECT * FROM users")
    if err != nil {
        if err == context.DeadlineExceeded {
            log.Println("数据库查询超时")
        }
        return
    }
    defer rows.Close()
    
    // 处理查询结果
}
```

### 3. 并发任务控制
```go
func parallelTasks(ctx context.Context) error {
    g, ctx := errgroup.WithContext(ctx)
    
    // 启动多个并发任务
    for i := 0; i < 5; i++ {
        i := i
        g.Go(func() error {
            return task(ctx, i)
        })
    }
    
    return g.Wait()
}

func task(ctx context.Context, id int) error {
    select {
    case <-time.After(time.Duration(id) * time.Second):
        fmt.Printf("任务 %d 完成\n", id)
        return nil
    case <-ctx.Done():
        fmt.Printf("任务 %d 被取消\n", id)
        return ctx.Err()
    }
}
```

## 最佳实践

### 1. Context传递规则
- Context应该作为函数的第一个参数
- 不要将Context存储在结构体中
- 不要传递nil Context，使用context.TODO()

### 2. 正确的取消处理
```go
func correctCancellation(ctx context.Context) {
    ctx, cancel := context.WithCancel(ctx)
    defer cancel() // 确保资源释放
    
    go func() {
        defer cancel() // 子goroutine也要能取消父Context
        // 执行任务
    }()
    
    select {
    case <-ctx.Done():
        return
    }
}
```

### 3. 避免Context值滥用
```go
// 好的做法：使用强类型key
type contextKey string
const UserIDKey contextKey = "userID"

// 避免：使用字符串作为key
// ctx = context.WithValue(ctx, "userID", "123")
```

## 面试要点
1. **Context接口**：四个方法的作用和使用场景
2. **传播机制**：Context如何在goroutine间传播取消信号
3. **内存泄漏**：不调用cancel函数可能导致的问题
4. **使用原则**：Context的传递规则和最佳实践
5. **性能考虑**：Context.Value的查找是O(n)复杂度
