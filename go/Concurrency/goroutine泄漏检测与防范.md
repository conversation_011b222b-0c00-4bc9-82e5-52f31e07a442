# Goroutine泄漏检测与防范

## 什么是Goroutine泄漏？

### 定义
Goroutine泄漏是指程序中创建的goroutine无法正常退出，持续占用系统资源（内存、调度器资源等），最终可能导致程序性能下降或崩溃。

### 常见泄漏场景
1. **Channel阻塞**：goroutine在channel操作上永久阻塞
2. **死循环**：goroutine陷入无限循环
3. **资源等待**：等待永远不会到来的资源或信号
4. **Context未正确使用**：没有正确处理context取消信号

## 常见的Goroutine泄漏模式

### 1. Channel发送阻塞
```go
// 错误示例：可能导致goroutine泄漏
func badChannelSend() {
    ch := make(chan int)
    
    go func() {
        // 如果没有接收者，这里会永久阻塞
        ch <- 1
        fmt.Println("发送完成") // 永远不会执行
    }()
    
    // 主goroutine退出，但发送goroutine仍在阻塞
}

// 正确示例：使用带缓冲的channel或select
func goodChannelSend() {
    ch := make(chan int, 1) // 带缓冲
    
    go func() {
        select {
        case ch <- 1:
            fmt.Println("发送完成")
        case <-time.After(time.Second):
            fmt.Println("发送超时")
        }
    }()
}
```

### 2. Channel接收阻塞
```go
// 错误示例
func badChannelReceive() {
    ch := make(chan int)
    
    go func() {
        // 如果没有发送者，这里会永久阻塞
        val := <-ch
        fmt.Println("接收到:", val)
    }()
}

// 正确示例：使用context控制
func goodChannelReceive(ctx context.Context) {
    ch := make(chan int)
    
    go func() {
        select {
        case val := <-ch:
            fmt.Println("接收到:", val)
        case <-ctx.Done():
            fmt.Println("接收被取消")
            return
        }
    }()
}
```

### 3. HTTP请求未设置超时
```go
// 错误示例
func badHTTPRequest() {
    go func() {
        // 没有设置超时，可能永久阻塞
        resp, err := http.Get("http://example.com")
        if err != nil {
            return
        }
        defer resp.Body.Close()
        // 处理响应...
    }()
}

// 正确示例
func goodHTTPRequest() {
    go func() {
        client := &http.Client{
            Timeout: 10 * time.Second,
        }
        
        ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
        defer cancel()
        
        req, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com", nil)
        resp, err := client.Do(req)
        if err != nil {
            return
        }
        defer resp.Body.Close()
        // 处理响应...
    }()
}
```

### 4. 等待组使用不当
```go
// 错误示例
func badWaitGroup() {
    var wg sync.WaitGroup
    
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(id int) {
            // 如果这里panic或者某些条件下不调用Done()
            if id == 5 {
                return // 忘记调用wg.Done()
            }
            defer wg.Done()
            // 执行任务...
        }(i)
    }
    
    wg.Wait() // 可能永久等待
}

// 正确示例
func goodWaitGroup() {
    var wg sync.WaitGroup
    
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done() // 确保总是调用Done()
            
            defer func() {
                if r := recover(); r != nil {
                    log.Printf("Goroutine %d panic: %v", id, r)
                }
            }()
            
            // 执行任务...
        }(i)
    }
    
    wg.Wait()
}
```

## Goroutine泄漏检测方法

### 1. 运行时检测
```go
func detectGoroutineLeaks() {
    // 记录初始goroutine数量
    initialCount := runtime.NumGoroutine()
    
    // 执行可能泄漏的代码
    suspiciousFunction()
    
    // 强制GC，等待一段时间
    runtime.GC()
    time.Sleep(100 * time.Millisecond)
    
    // 检查goroutine数量
    finalCount := runtime.NumGoroutine()
    if finalCount > initialCount {
        log.Printf("可能存在goroutine泄漏: 初始=%d, 最终=%d", 
                   initialCount, finalCount)
    }
}
```

### 2. 使用pprof分析
```go
import (
    _ "net/http/pprof"
    "net/http"
)

func main() {
    // 启动pprof服务
    go func() {
        log.Println(http.ListenAndServe("localhost:6060", nil))
    }()
    
    // 你的应用代码...
}

// 命令行分析：
// go tool pprof http://localhost:6060/debug/pprof/goroutine
```

### 3. 自定义监控
```go
type GoroutineMonitor struct {
    threshold int
    interval  time.Duration
    alertFunc func(count int)
}

func NewGoroutineMonitor(threshold int, interval time.Duration) *GoroutineMonitor {
    return &GoroutineMonitor{
        threshold: threshold,
        interval:  interval,
        alertFunc: func(count int) {
            log.Printf("警告：goroutine数量过多: %d", count)
        },
    }
}

func (gm *GoroutineMonitor) Start(ctx context.Context) {
    ticker := time.NewTicker(gm.interval)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            count := runtime.NumGoroutine()
            if count > gm.threshold {
                gm.alertFunc(count)
            }
        case <-ctx.Done():
            return
        }
    }
}
```

### 4. 使用第三方工具
```go
// 使用goleak库进行测试
import "go.uber.org/goleak"

func TestNoGoroutineLeaks(t *testing.T) {
    defer goleak.VerifyNone(t)
    
    // 执行可能泄漏的代码
    suspiciousFunction()
}
```

## 防范Goroutine泄漏的最佳实践

### 1. 使用Context控制生命周期
```go
func workerWithContext(ctx context.Context) {
    ticker := time.NewTicker(time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            // 执行定期任务
            doWork()
        case <-ctx.Done():
            log.Println("Worker停止")
            return
        }
    }
}

func main() {
    ctx, cancel := context.WithCancel(context.Background())
    
    go workerWithContext(ctx)
    
    // 程序退出时取消context
    defer cancel()
    
    // 应用逻辑...
}
```

### 2. 设置合理的超时
```go
func operationWithTimeout() error {
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()
    
    resultCh := make(chan result, 1)
    
    go func() {
        // 执行耗时操作
        res := doLongRunningOperation()
        select {
        case resultCh <- res:
        case <-ctx.Done():
            // 操作被取消，清理资源
            cleanup()
        }
    }()
    
    select {
    case res := <-resultCh:
        return processResult(res)
    case <-ctx.Done():
        return ctx.Err()
    }
}
```

### 3. 正确关闭Channel
```go
type Worker struct {
    taskCh chan Task
    done   chan struct{}
    wg     sync.WaitGroup
}

func NewWorker() *Worker {
    w := &Worker{
        taskCh: make(chan Task),
        done:   make(chan struct{}),
    }
    
    w.wg.Add(1)
    go w.run()
    
    return w
}

func (w *Worker) run() {
    defer w.wg.Done()
    
    for {
        select {
        case task := <-w.taskCh:
            if task != nil {
                task.Execute()
            }
        case <-w.done:
            return
        }
    }
}

func (w *Worker) Stop() {
    close(w.done)
    w.wg.Wait()
}

func (w *Worker) Submit(task Task) {
    select {
    case w.taskCh <- task:
    case <-w.done:
        // Worker已停止，拒绝新任务
    }
}
```

### 4. 使用defer确保资源清理
```go
func processWithCleanup() {
    resource := acquireResource()
    defer resource.Release() // 确保资源被释放
    
    ctx, cancel := context.WithCancel(context.Background())
    defer cancel() // 确保context被取消
    
    go func() {
        defer func() {
            if r := recover(); r != nil {
                log.Printf("Goroutine panic: %v", r)
            }
        }()
        
        select {
        case <-doWork():
            // 工作完成
        case <-ctx.Done():
            // 被取消
        }
    }()
    
    // 主逻辑...
}
```

## 高级检测技术

### 1. 自动化泄漏检测
```go
type LeakDetector struct {
    baseline    int
    tolerance   int
    checkPoints []int
    mu          sync.Mutex
}

func NewLeakDetector() *LeakDetector {
    return &LeakDetector{
        baseline:  runtime.NumGoroutine(),
        tolerance: 5,
    }
}

func (ld *LeakDetector) CheckPoint(name string) {
    ld.mu.Lock()
    defer ld.mu.Unlock()
    
    current := runtime.NumGoroutine()
    ld.checkPoints = append(ld.checkPoints, current)
    
    if current > ld.baseline+ld.tolerance {
        log.Printf("检测点 %s: 可能的goroutine泄漏 (当前: %d, 基线: %d)", 
                   name, current, ld.baseline)
        
        // 输出goroutine堆栈信息
        buf := make([]byte, 1<<16)
        stackSize := runtime.Stack(buf, true)
        log.Printf("Goroutine堆栈:\n%s", buf[:stackSize])
    }
}
```

### 2. 集成监控系统
```go
import "github.com/prometheus/client_golang/prometheus"

var (
    goroutineCount = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "goroutine_count",
            Help: "当前goroutine数量",
        },
        []string{"service"},
    )
)

func init() {
    prometheus.MustRegister(goroutineCount)
}

func monitorGoroutines(serviceName string) {
    go func() {
        ticker := time.NewTicker(10 * time.Second)
        defer ticker.Stop()
        
        for range ticker.C {
            count := runtime.NumGoroutine()
            goroutineCount.WithLabelValues(serviceName).Set(float64(count))
        }
    }()
}
```

## 面试要点总结

1. **泄漏原因**：理解常见的goroutine泄漏场景和原因
2. **检测方法**：掌握多种检测goroutine泄漏的方法
3. **防范策略**：了解使用context、超时、正确关闭channel等防范措施
4. **工具使用**：熟悉pprof、goleak等检测工具
5. **最佳实践**：掌握编写不会泄漏的goroutine代码的最佳实践
6. **性能影响**：理解goroutine泄漏对系统性能的影响
