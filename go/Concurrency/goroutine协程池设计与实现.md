# Goroutine协程池设计与实现

## 为什么需要协程池？

### 核心问题
- **资源控制**：限制并发数量，防止系统资源耗尽
- **性能优化**：复用goroutine，减少创建销毁开销
- **任务管理**：统一管理和监控goroutine生命周期

## 基本实现

### 核心结构
```go
type Pool struct {
    capacity  int32         // 池容量
    running   int32         // 运行中的worker数量
    taskQueue chan func()   // 任务队列
    closed    int32         // 关闭状态
    once      sync.Once     // 确保只关闭一次
}

func NewPool(capacity int) *Pool {
    p := &Pool{
        capacity:  int32(capacity),
        taskQueue: make(chan func(), capacity),
    }
    return p
}
```

### 提交任务
```go
func (p *Pool) Submit(task func()) error {
    if atomic.LoadInt32(&p.closed) == 1 {
        return errors.New("pool is closed")
    }

    // 尝试启动新worker
    if atomic.LoadInt32(&p.running) < atomic.LoadInt32(&p.capacity) {
        if atomic.CompareAndSwapInt32(&p.running,
            atomic.LoadInt32(&p.running),
            atomic.LoadInt32(&p.running)+1) {
            go p.worker()
        }
    }

    select {
    case p.taskQueue <- task:
        return nil
    default:
        return errors.New("task queue is full")
    }
}
```

### Worker实现
```go
func (p *Pool) worker() {
    defer atomic.AddInt32(&p.running, -1)

    for {
        select {
        case task := <-p.taskQueue:
            p.safeExecute(task)
        case <-time.After(time.Minute): // 空闲超时退出
            return
        }
    }
}

func (p *Pool) safeExecute(task func()) {
    defer func() {
        if r := recover(); r != nil {
            log.Printf("Task panic: %v", r)
        }
    }()
    task()
}
```

## 高级特性

### 1. 池大小设置
```go
func OptimalPoolSize(taskType string) int {
    switch taskType {
    case "cpu":
        return runtime.NumCPU()           // CPU密集型
    case "io":
        return runtime.NumCPU() * 2       // IO密集型
    default:
        return runtime.NumCPU() + 1       // 混合型
    }
}
```

### 2. 超时控制
```go
func (p *Pool) SubmitWithTimeout(task func(), timeout time.Duration) error {
    ctx, cancel := context.WithTimeout(context.Background(), timeout)
    defer cancel()

    return p.Submit(func() {
        done := make(chan struct{})
        go func() {
            defer close(done)
            task()
        }()

        select {
        case <-done:
        case <-ctx.Done():
            log.Printf("Task timeout after %v", timeout)
        }
    })
}
```

### 3. 优雅关闭
```go
func (p *Pool) Close() {
    p.once.Do(func() {
        atomic.StoreInt32(&p.closed, 1)
        close(p.taskQueue)

        // 等待所有worker退出
        for atomic.LoadInt32(&p.running) > 0 {
            time.Sleep(10 * time.Millisecond)
        }
    })
}
```

## 性能监控

### 关键指标
```go
type PoolStats struct {
    Capacity    int   `json:"capacity"`
    Running     int   `json:"running"`
    QueueLength int   `json:"queue_length"`
    Completed   int64 `json:"completed"`
    Failed      int64 `json:"failed"`
}

func (p *Pool) Stats() PoolStats {
    return PoolStats{
        Capacity:    int(atomic.LoadInt32(&p.capacity)),
        Running:     int(atomic.LoadInt32(&p.running)),
        QueueLength: len(p.taskQueue),
    }
}
```

## 面试要点
1. **设计原理**：worker复用、任务队列、并发控制
2. **性能考虑**：池大小设置、内存复用、锁竞争
3. **错误处理**：panic恢复、超时控制、优雅关闭
4. **适用场景**：CPU密集型vs IO密集型任务的不同策略
