# 高级锁机制与同步原语

## 互斥锁深度解析

### 1. sync.Mutex内部实现

#### Mutex状态位
```go
// Mutex的内部状态（简化版）
const (
    mutexLocked = 1 << iota // 锁定状态位
    mutexWoken              // 唤醒状态位
    mutexStarving           // 饥饿状态位
    mutexWaiterShift = iota // 等待者数量的位移
)

// Mutex结构体
type Mutex struct {
    state int32  // 状态字段
    sema  uint32 // 信号量
}
```

#### 锁的获取过程
```go
func (m *Mutex) Lock() {
    // 快速路径：尝试直接获取锁
    if atomic.CompareAndSwapInt32(&m.state, 0, mutexLocked) {
        return
    }
    
    // 慢速路径：需要等待
    m.lockSlow()
}

func (m *Mutex) lockSlow() {
    var waitStartTime int64
    starving := false
    awoke := false
    iter := 0
    old := m.state
    
    for {
        // 在正常模式下，如果锁被持有且不在饥饿状态，尝试自旋
        if old&(mutexLocked|mutexStarving) == mutexLocked && runtime_canSpin(iter) {
            // 尝试设置唤醒标志
            if !awoke && old&mutexWoken == 0 && old>>mutexWaiterShift != 0 &&
                atomic.CompareAndSwapInt32(&m.state, old, old|mutexWoken) {
                awoke = true
            }
            runtime_doSpin()
            iter++
            old = m.state
            continue
        }
        
        new := old
        
        // 如果不在饥饿状态，尝试获取锁
        if old&mutexStarving == 0 {
            new |= mutexLocked
        }
        
        // 如果锁被持有或在饥饿状态，增加等待者数量
        if old&(mutexLocked|mutexStarving) != 0 {
            new += 1 << mutexWaiterShift
        }
        
        // 如果当前goroutine饥饿且锁被持有，切换到饥饿模式
        if starving && old&mutexLocked != 0 {
            new |= mutexStarving
        }
        
        if awoke {
            new &^= mutexWoken
        }
        
        if atomic.CompareAndSwapInt32(&m.state, old, new) {
            // 成功获取锁或需要等待
            if old&(mutexLocked|mutexStarving) == 0 {
                break // 获取到锁
            }
            
            // 等待信号量
            queueLifo := waitStartTime != 0
            if waitStartTime == 0 {
                waitStartTime = runtime_nanotime()
            }
            runtime_SemacquireMutex(&m.sema, queueLifo, 1)
            
            // 检查是否应该进入饥饿状态
            starving = starving || runtime_nanotime()-waitStartTime > starvationThresholdNs
            old = m.state
            
            if old&mutexStarving != 0 {
                // 在饥饿模式下被唤醒
                new = (old - 1<<mutexWaiterShift) | mutexLocked
                if !starving || old>>mutexWaiterShift == 1 {
                    new &^= mutexStarving
                }
                m.state = new
                break
            }
            
            awoke = true
            iter = 0
        } else {
            old = m.state
        }
    }
}
```

### 2. 自适应自旋锁

#### 自旋锁实现
```go
type SpinLock struct {
    flag int32
}

func (sl *SpinLock) Lock() {
    for !atomic.CompareAndSwapInt32(&sl.flag, 0, 1) {
        // 自旋等待
        for atomic.LoadInt32(&sl.flag) == 1 {
            runtime.Gosched() // 让出CPU时间片
        }
    }
}

func (sl *SpinLock) Unlock() {
    atomic.StoreInt32(&sl.flag, 0)
}

// 带退避的自旋锁
type BackoffSpinLock struct {
    flag int32
}

func (bsl *BackoffSpinLock) Lock() {
    backoff := 1
    for !atomic.CompareAndSwapInt32(&bsl.flag, 0, 1) {
        for i := 0; i < backoff; i++ {
            if atomic.LoadInt32(&bsl.flag) == 0 {
                break
            }
            runtime.Gosched()
        }
        if backoff < 1024 {
            backoff *= 2
        }
    }
}

func (bsl *BackoffSpinLock) Unlock() {
    atomic.StoreInt32(&bsl.flag, 0)
}
```

### 3. 读写锁优化

#### 读写锁的内部实现
```go
type RWMutex struct {
    w           Mutex  // 写锁
    writerSem   uint32 // 写者信号量
    readerSem   uint32 // 读者信号量
    readerCount int32  // 读者数量
    readerWait  int32  // 等待的读者数量
}

const rwmutexMaxReaders = 1 << 30

func (rw *RWMutex) RLock() {
    if atomic.AddInt32(&rw.readerCount, 1) < 0 {
        // 有写者等待，需要等待
        runtime_SemacquireMutex(&rw.readerSem, false, 0)
    }
}

func (rw *RWMutex) RUnlock() {
    if r := atomic.AddInt32(&rw.readerCount, -1); r < 0 {
        // 可能有写者等待
        rw.rUnlockSlow(r)
    }
}

func (rw *RWMutex) rUnlockSlow(r int32) {
    if r+1 == 0 || r+1 == -rwmutexMaxReaders {
        panic("sync: RUnlock of unlocked RWMutex")
    }
    
    // 如果是最后一个读者，唤醒写者
    if atomic.AddInt32(&rw.readerWait, -1) == 0 {
        runtime_Semrelease(&rw.writerSem, false, 1)
    }
}

func (rw *RWMutex) Lock() {
    // 获取写锁
    rw.w.Lock()
    
    // 通知读者有写者等待
    r := atomic.AddInt32(&rw.readerCount, -rwmutexMaxReaders) + rwmutexMaxReaders
    
    // 等待所有读者完成
    if r != 0 && atomic.AddInt32(&rw.readerWait, r) != 0 {
        runtime_SemacquireMutex(&rw.writerSem, false, 0)
    }
}

func (rw *RWMutex) Unlock() {
    // 恢复读者计数
    r := atomic.AddInt32(&rw.readerCount, rwmutexMaxReaders)
    if r >= rwmutexMaxReaders {
        panic("sync: Unlock of unlocked RWMutex")
    }
    
    // 唤醒所有等待的读者
    for i := 0; i < int(r); i++ {
        runtime_Semrelease(&rw.readerSem, false, 0)
    }
    
    // 释放写锁
    rw.w.Unlock()
}
```

## 高级同步原语

### 1. sync.Cond条件变量

#### 条件变量的使用
```go
type Buffer struct {
    mu    sync.Mutex
    cond  *sync.Cond
    items []interface{}
    size  int
}

func NewBuffer(size int) *Buffer {
    b := &Buffer{
        items: make([]interface{}, 0, size),
        size:  size,
    }
    b.cond = sync.NewCond(&b.mu)
    return b
}

func (b *Buffer) Put(item interface{}) {
    b.mu.Lock()
    defer b.mu.Unlock()
    
    // 等待缓冲区有空间
    for len(b.items) == b.size {
        b.cond.Wait()
    }
    
    b.items = append(b.items, item)
    b.cond.Signal() // 通知等待的消费者
}

func (b *Buffer) Get() interface{} {
    b.mu.Lock()
    defer b.mu.Unlock()
    
    // 等待缓冲区有数据
    for len(b.items) == 0 {
        b.cond.Wait()
    }
    
    item := b.items[0]
    b.items = b.items[1:]
    b.cond.Signal() // 通知等待的生产者
    return item
}
```

#### 广播通知模式
```go
type EventBroadcaster struct {
    mu        sync.Mutex
    cond      *sync.Cond
    event     interface{}
    triggered bool
}

func NewEventBroadcaster() *EventBroadcaster {
    eb := &EventBroadcaster{}
    eb.cond = sync.NewCond(&eb.mu)
    return eb
}

func (eb *EventBroadcaster) Wait() interface{} {
    eb.mu.Lock()
    defer eb.mu.Unlock()
    
    for !eb.triggered {
        eb.cond.Wait()
    }
    
    return eb.event
}

func (eb *EventBroadcaster) Trigger(event interface{}) {
    eb.mu.Lock()
    defer eb.mu.Unlock()
    
    if !eb.triggered {
        eb.event = event
        eb.triggered = true
        eb.cond.Broadcast() // 唤醒所有等待者
    }
}
```

### 2. sync.Once单次执行

#### Once的内部实现
```go
type Once struct {
    done uint32
    m    Mutex
}

func (o *Once) Do(f func()) {
    // 快速路径：检查是否已执行
    if atomic.LoadUint32(&o.done) == 0 {
        o.doSlow(f)
    }
}

func (o *Once) doSlow(f func()) {
    o.m.Lock()
    defer o.m.Unlock()
    
    // 双重检查
    if o.done == 0 {
        defer atomic.StoreUint32(&o.done, 1)
        f()
    }
}
```

#### Once的应用场景
```go
// 单例模式
type Singleton struct {
    data string
}

var (
    instance *Singleton
    once     sync.Once
)

func GetSingleton() *Singleton {
    once.Do(func() {
        instance = &Singleton{data: "singleton"}
    })
    return instance
}

// 延迟初始化
type LazyResource struct {
    once     sync.Once
    resource *ExpensiveResource
    err      error
}

func (lr *LazyResource) Get() (*ExpensiveResource, error) {
    lr.once.Do(func() {
        lr.resource, lr.err = NewExpensiveResource()
    })
    return lr.resource, lr.err
}
```

### 3. sync.WaitGroup等待组

#### WaitGroup的内部实现
```go
type WaitGroup struct {
    noCopy noCopy
    state1 [3]uint32
}

func (wg *WaitGroup) state() (statep *uint64, semap *uint32) {
    if uintptr(unsafe.Pointer(&wg.state1))%8 == 0 {
        return (*uint64)(unsafe.Pointer(&wg.state1)), &wg.state1[2]
    } else {
        return (*uint64)(unsafe.Pointer(&wg.state1[1])), &wg.state1[0]
    }
}

func (wg *WaitGroup) Add(delta int) {
    statep, semap := wg.state()
    state := atomic.AddUint64(statep, uint64(delta)<<32)
    v := int32(state >> 32)
    w := uint32(state)
    
    if v < 0 {
        panic("sync: negative WaitGroup counter")
    }
    
    if w != 0 && delta > 0 && v == int32(delta) {
        panic("sync: WaitGroup misuse: Add called concurrently with Wait")
    }
    
    if v > 0 || w == 0 {
        return
    }
    
    // 计数器为0且有等待者，唤醒所有等待者
    *statep = 0
    for ; w != 0; w-- {
        runtime_Semrelease(semap, false, 0)
    }
}

func (wg *WaitGroup) Done() {
    wg.Add(-1)
}

func (wg *WaitGroup) Wait() {
    statep, semap := wg.state()
    for {
        state := atomic.LoadUint64(statep)
        v := int32(state >> 32)
        w := uint32(state)
        
        if v == 0 {
            return
        }
        
        if atomic.CompareAndSwapUint64(statep, state, state+1) {
            runtime_SemacquireMutex(semap, false, 0)
            if *statep != 0 {
                panic("sync: WaitGroup is reused before previous Wait has returned")
            }
            return
        }
    }
}
```

#### WaitGroup的高级用法
```go
// 限制并发数量的WaitGroup
type LimitedWaitGroup struct {
    wg  sync.WaitGroup
    sem chan struct{}
}

func NewLimitedWaitGroup(limit int) *LimitedWaitGroup {
    return &LimitedWaitGroup{
        sem: make(chan struct{}, limit),
    }
}

func (lwg *LimitedWaitGroup) Add() {
    lwg.sem <- struct{}{} // 获取信号量
    lwg.wg.Add(1)
}

func (lwg *LimitedWaitGroup) Done() {
    <-lwg.sem // 释放信号量
    lwg.wg.Done()
}

func (lwg *LimitedWaitGroup) Wait() {
    lwg.wg.Wait()
}

// 使用示例
func processWithLimit() {
    lwg := NewLimitedWaitGroup(10) // 最多10个并发
    
    for i := 0; i < 100; i++ {
        lwg.Add()
        go func(id int) {
            defer lwg.Done()
            // 处理任务
            processTask(id)
        }(i)
    }
    
    lwg.Wait()
}
```

## 自定义同步原语

### 1. 信号量实现

#### 计数信号量
```go
type Semaphore struct {
    permits int
    mu      sync.Mutex
    cond    *sync.Cond
}

func NewSemaphore(permits int) *Semaphore {
    s := &Semaphore{permits: permits}
    s.cond = sync.NewCond(&s.mu)
    return s
}

func (s *Semaphore) Acquire() {
    s.mu.Lock()
    defer s.mu.Unlock()
    
    for s.permits <= 0 {
        s.cond.Wait()
    }
    s.permits--
}

func (s *Semaphore) TryAcquire() bool {
    s.mu.Lock()
    defer s.mu.Unlock()
    
    if s.permits > 0 {
        s.permits--
        return true
    }
    return false
}

func (s *Semaphore) Release() {
    s.mu.Lock()
    defer s.mu.Unlock()
    
    s.permits++
    s.cond.Signal()
}

func (s *Semaphore) AvailablePermits() int {
    s.mu.Lock()
    defer s.mu.Unlock()
    return s.permits
}
```

### 2. 屏障同步

#### CyclicBarrier实现
```go
type CyclicBarrier struct {
    mu       sync.Mutex
    cond     *sync.Cond
    parties  int
    count    int
    broken   bool
    barrierAction func()
}

func NewCyclicBarrier(parties int, barrierAction func()) *CyclicBarrier {
    cb := &CyclicBarrier{
        parties:       parties,
        count:         parties,
        barrierAction: barrierAction,
    }
    cb.cond = sync.NewCond(&cb.mu)
    return cb
}

func (cb *CyclicBarrier) Await() error {
    cb.mu.Lock()
    defer cb.mu.Unlock()
    
    if cb.broken {
        return errors.New("barrier is broken")
    }
    
    cb.count--
    
    if cb.count == 0 {
        // 最后一个到达的goroutine
        if cb.barrierAction != nil {
            cb.barrierAction()
        }
        
        // 重置屏障并唤醒所有等待者
        cb.count = cb.parties
        cb.cond.Broadcast()
        return nil
    }
    
    // 等待其他goroutine
    for cb.count > 0 && !cb.broken {
        cb.cond.Wait()
    }
    
    if cb.broken {
        return errors.New("barrier is broken")
    }
    
    return nil
}

func (cb *CyclicBarrier) Reset() {
    cb.mu.Lock()
    defer cb.mu.Unlock()
    
    cb.broken = true
    cb.cond.Broadcast()
    
    cb.broken = false
    cb.count = cb.parties
}
```

### 3. 读写锁变种

#### 公平读写锁
```go
type FairRWMutex struct {
    mu          sync.Mutex
    readerCond  *sync.Cond
    writerCond  *sync.Cond
    readers     int
    writers     int
    writeWaiting int
}

func NewFairRWMutex() *FairRWMutex {
    frw := &FairRWMutex{}
    frw.readerCond = sync.NewCond(&frw.mu)
    frw.writerCond = sync.NewCond(&frw.mu)
    return frw
}

func (frw *FairRWMutex) RLock() {
    frw.mu.Lock()
    defer frw.mu.Unlock()
    
    // 如果有写者等待，读者也要等待（公平性）
    for frw.writers > 0 || frw.writeWaiting > 0 {
        frw.readerCond.Wait()
    }
    
    frw.readers++
}

func (frw *FairRWMutex) RUnlock() {
    frw.mu.Lock()
    defer frw.mu.Unlock()
    
    frw.readers--
    if frw.readers == 0 {
        frw.writerCond.Signal()
    }
}

func (frw *FairRWMutex) Lock() {
    frw.mu.Lock()
    defer frw.mu.Unlock()
    
    frw.writeWaiting++
    for frw.readers > 0 || frw.writers > 0 {
        frw.writerCond.Wait()
    }
    frw.writeWaiting--
    frw.writers++
}

func (frw *FairRWMutex) Unlock() {
    frw.mu.Lock()
    defer frw.mu.Unlock()
    
    frw.writers--
    
    // 优先唤醒等待的写者
    if frw.writeWaiting > 0 {
        frw.writerCond.Signal()
    } else {
        frw.readerCond.Broadcast()
    }
}
```

## 性能优化技巧

### 1. 锁粒度优化

#### 细粒度锁
```go
type ShardedMap struct {
    shards []shard
    mask   uint32
}

type shard struct {
    mu   sync.RWMutex
    data map[string]interface{}
}

func NewShardedMap(shardCount int) *ShardedMap {
    if shardCount&(shardCount-1) != 0 {
        panic("shard count must be power of 2")
    }
    
    sm := &ShardedMap{
        shards: make([]shard, shardCount),
        mask:   uint32(shardCount - 1),
    }
    
    for i := range sm.shards {
        sm.shards[i].data = make(map[string]interface{})
    }
    
    return sm
}

func (sm *ShardedMap) getShard(key string) *shard {
    hash := fnv32(key)
    return &sm.shards[hash&sm.mask]
}

func (sm *ShardedMap) Get(key string) (interface{}, bool) {
    shard := sm.getShard(key)
    shard.mu.RLock()
    defer shard.mu.RUnlock()
    
    value, ok := shard.data[key]
    return value, ok
}

func (sm *ShardedMap) Set(key string, value interface{}) {
    shard := sm.getShard(key)
    shard.mu.Lock()
    defer shard.mu.Unlock()
    
    shard.data[key] = value
}

func fnv32(s string) uint32 {
    h := uint32(2166136261)
    for i := 0; i < len(s); i++ {
        h = (h * 16777619) ^ uint32(s[i])
    }
    return h
}
```

### 2. 无锁优化

#### 使用channel替代锁
```go
type ChannelBasedCounter struct {
    ch    chan int
    value int
    done  chan struct{}
}

func NewChannelBasedCounter() *ChannelBasedCounter {
    cbc := &ChannelBasedCounter{
        ch:   make(chan int, 100),
        done: make(chan struct{}),
    }
    
    go cbc.run()
    return cbc
}

func (cbc *ChannelBasedCounter) run() {
    for {
        select {
        case delta := <-cbc.ch:
            cbc.value += delta
        case <-cbc.done:
            return
        }
    }
}

func (cbc *ChannelBasedCounter) Add(delta int) {
    select {
    case cbc.ch <- delta:
    default:
        // 缓冲区满，可以选择阻塞或丢弃
    }
}

func (cbc *ChannelBasedCounter) Get() int {
    // 这里需要更复杂的实现来安全地读取值
    // 简化示例
    return cbc.value
}

func (cbc *ChannelBasedCounter) Close() {
    close(cbc.done)
}
```

## 面试要点总结

1. **锁的内部实现**：理解Mutex和RWMutex的内部机制
2. **同步原语应用**：掌握各种同步原语的使用场景
3. **性能优化**：了解锁粒度优化和无锁编程技巧
4. **自定义同步**：能够实现自定义的同步原语
5. **死锁避免**：理解死锁产生的条件和避免方法
6. **公平性问题**：了解锁的公平性和饥饿问题
7. **实际应用**：能够在实际项目中选择合适的同步机制
