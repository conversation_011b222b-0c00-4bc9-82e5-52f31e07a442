# Defer执行顺序和原理

## 1. 基本概念

### 执行顺序：LIFO（后进先出）
```go
func example() {
    defer fmt.Println("defer 1")
    defer fmt.Println("defer 2")
    defer fmt.Println("defer 3")
    fmt.Println("normal execution")
}
// 输出：normal execution → defer 3 → defer 2 → defer 1
```

### 执行时机
- **函数正常返回时**
- **函数发生panic时**
- **os.Exit()时不执行defer**

## 2. 参数求值时机

### 立即求值
```go
func deferArgs() {
    i := 0
    defer fmt.Println("defer value:", i) // i=0 立即求值
    i++
    fmt.Println("current value:", i)
}
// 输出：current value: 1 → defer value: 0
```

### 闭包陷阱
```go
// 错误：所有defer都打印3
for i := 0; i < 3; i++ {
    defer func() { fmt.Println(i) }()
}

// 正确：传参方式
for i := 0; i < 3; i++ {
    defer func(val int) { fmt.Println(val) }(i)
}
```

## 3. 底层实现

### 数据结构
```go
type _defer struct {
    siz     int32    // 参数大小
    started bool     // 是否开始执行
    heap    bool     // 是否堆分配
    sp      uintptr  // 栈指针
    pc      uintptr  // 程序计数器
    fn      *funcval // defer函数
    link    *_defer  // 链表指针
}
```

### 执行机制
- **注册**：deferproc()将defer加入链表头
- **执行**：deferreturn()从链表头开始执行
- **链表**：每个goroutine维护defer链表

## 4. 常见用法

### 资源管理
```go
func readFile(filename string) error {
    file, err := os.Open(filename)
    if err != nil {
        return err
    }
    defer file.Close() // 确保关闭
    // 处理文件...
    return nil
}
```

### 锁管理
```go
func criticalSection() {
    mu.Lock()
    defer mu.Unlock() // 确保解锁
    // 临界区代码...
}
```

### 错误恢复
```go
func safeOperation() (err error) {
    defer func() {
        if r := recover(); r != nil {
            err = fmt.Errorf("panic: %v", r)
        }
    }()
    // 可能panic的操作...
    return nil
}
```

## 5. defer与return

### 命名返回值
```go
func deferReturn() (result int) {
    defer func() {
        result++ // 可以修改返回值
    }()
    return 5 // 实际返回6
}
```

### 执行顺序
1. **计算返回值**
2. **执行defer函数**
3. **函数返回**

## 6. 面试要点

### 核心问题
1. **defer执行顺序？** - LIFO后进先出
2. **参数何时求值？** - defer声明时立即求值
3. **defer与panic？** - panic时仍会执行defer
4. **性能影响？** - 有开销，避免在循环中使用

### 常见陷阱
```go
// 陷阱：循环变量
for i := 0; i < 3; i++ {
    defer func() { fmt.Print(i) }() // 输出333
}

// 正确：传参
for i := 0; i < 3; i++ {
    defer func(v int) { fmt.Print(v) }(i) // 输出210
}
```

### 一句话总结
> Defer按LIFO顺序执行，参数立即求值，常用于资源清理和错误恢复
