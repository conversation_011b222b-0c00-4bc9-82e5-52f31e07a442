# Map底层实现和并发安全

## 1. 核心概念

### Map基本特性
- **引用类型**：类似哈希表、字典或关联数组
- **键值对存储**：实现高效的关联数据访问
- **动态扩容**：根据负载因子自动调整大小
- **非并发安全**：需要额外机制保证并发访问安全

### 创建方式
```go
var m1 map[string]int                    // 声明，值为nil
m2 := make(map[string]int)              // 创建空map
m3 := map[string]int{"a": 1, "b": 2}    // 字面量初始化
```

## 2. 底层数据结构

### hmap结构体
```go
type hmap struct {
    count     int    // map中的元素个数
    flags     uint8  // 状态标志
    B         uint8  // 桶数组的大小为2^B
    noverflow uint16 // 溢出桶的数量
    hash0     uint32 // 哈希种子

    buckets    unsafe.Pointer // 桶数组指针
    oldbuckets unsafe.Pointer // 扩容时的旧桶数组
    nevacuate  uintptr        // 扩容进度
}
```

### bucket结构体
```go
type bmap struct {
    tophash [bucketCnt]uint8 // 每个key的hash值的高8位
    // 接下来是8个key
    // 然后是8个value
    // 最后是overflow指针
}
```

### 内存布局优势
- **局部性优化**：key和value分别连续存储
- **减少内存对齐浪费**：紧凑的内存布局
- **快速比较**：tophash数组支持快速定位

## 3. 哈希算法和定位

### 定位过程
1. **哈希计算**：对key计算64位哈希值
2. **桶定位**：低B位确定桶位置
3. **快速比较**：高8位存储在tophash中
4. **线性探测**：在桶内顺序查找

### 桶内存布局
```
[tophash0...tophash7][key0...key7][val0...val7][overflow]
```

## 4. 扩容机制

### 扩容触发条件
1. **负载因子过高**：元素数量 > 桶数量 * 6.5
2. **溢出桶过多**：溢出桶数量 > 2^B

### 扩容类型
- **增量扩容**：桶数量翻倍，负载因子过高时触发
- **等量扩容**：桶数量不变，重新整理减少溢出桶

### 渐进式扩容特点
- **分摊开销**：扩容在后续访问中逐步完成
- **避免阻塞**：每次访问迁移1-2个旧桶
- **双桶并存**：扩容期间新旧桶同时存在

## 5. 并发安全问题

### 为什么不是并发安全的
- **数据竞争**：并发读写会导致数据不一致
- **内存安全**：可能访问到无效内存地址
- **运行时检测**：Go运行时会检测并发访问并panic

### 并发检测机制
```go
const (
    hashWriting  = 4 // 有goroutine在写map
)

// 读取时检查写标志
if h.flags&hashWriting != 0 {
    throw("concurrent map read and map write")
}
```

## 6. 并发安全解决方案

### 方案1：sync.RWMutex
```go
type SafeMap struct {
    mu sync.RWMutex
    m  map[string]int
}

func (sm *SafeMap) Get(key string) (int, bool) {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    return sm.m[key]
}

func (sm *SafeMap) Set(key string, val int) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    sm.m[key] = val
}
```

### 方案2：sync.Map
```go
var m sync.Map

// 基本操作
m.Store("key", "value")           // 存储
val, ok := m.Load("key")          // 读取
m.Delete("key")                   // 删除
val, loaded := m.LoadOrStore("key", "value") // 读取或存储

// 遍历
m.Range(func(key, value interface{}) bool {
    return true // 继续遍历
})
```

### 方案3：Channel实现
```go
type ChannelMap struct {
    ch chan func(map[string]int)
}

func (cm *ChannelMap) Get(key string) int {
    result := make(chan int)
    cm.ch <- func(m map[string]int) {
        result <- m[key]
    }
    return <-result
}
```

## 7. 性能优化

### 优化策略
1. **预分配容量**：`make(map[string]int, expectedSize)`
2. **选择合适key类型**：整数key通常比字符串key性能更好
3. **避免频繁删除**：大量删除时重新创建map更高效
4. **减少哈希冲突**：选择分布均匀的key

### 内存泄漏避免
```go
// 问题：删除元素后底层数组不会缩小
// 解决：重新创建map
if len(m) < cap(m)/4 {
    newM := make(map[string]int)
    for k, v := range m {
        newM[k] = v
    }
    m = newM
}
```

## 8. 面试要点

### 核心问题
1. **map的零值是什么？**
   - nil，读取安全但写入会panic

2. **map能否比较？**
   - 只能与nil比较，不能直接比较两个map

3. **map遍历顺序？**
   - 随机顺序，需要有序遍历时先排序key

4. **如何实现并发安全？**
   - sync.RWMutex、sync.Map、channel等方案

### 底层原理
- **哈希表实现**：桶数组 + 链表法处理冲突
- **渐进式扩容**：分摊扩容开销，避免长时间阻塞
- **内存布局优化**：key和value分别连续存储

### 一句话总结
> Map是Go的哈希表实现，采用桶数组+链表结构，支持渐进式扩容，非并发安全需要额外同步机制
