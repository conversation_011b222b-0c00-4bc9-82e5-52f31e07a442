# Interface内部实现详解

## 1. 核心概念

### 接口定义
接口定义一组方法签名，支持多态和依赖注入：

```go
type Animal interface {
    Speak() string
}

type Dog struct{}
func (d Dog) Speak() string { return "Woof" }
```

## 2. 底层数据结构

### 空接口（eface）
```go
type eface struct {
    _type *_type         // 类型信息
    data  unsafe.Pointer // 数据指针
}
```

### 非空接口（iface）
```go
type iface struct {
    tab  *itab          // 接口表
    data unsafe.Pointer // 数据指针
}
```

### 接口表（itab）
```go
type itab struct {
    inter *interfacetype // 接口类型
    _type *_type         // 实际类型
    hash  uint32         // 类型哈希
    fun   [1]uintptr     // 方法表
}
```

## 3. 工作原理

### 接口赋值过程
1. **类型检查**：编译时检查类型是否实现接口
2. **创建接口值**：运行时创建iface/eface结构
3. **填充数据**：设置类型信息和数据指针

### 方法调用过程
1. **查找方法表**：通过itab.fun找到方法地址
2. **动态调用**：通过函数指针调用具体实现
3. **传递接收者**：将data作为接收者传递

## 4. 类型断言

### 安全断言
```go
var i interface{} = "hello"
s, ok := i.(string) // 返回值和布尔标志
if ok {
    fmt.Println(s)
}
```

### 类型判断
```go
switch v := i.(type) {
case string:
    fmt.Println("string:", v)
case int:
    fmt.Println("int:", v)
default:
    fmt.Println("unknown type")
}
```

## 5. 性能考虑

### 接口调用开销
- **虚函数调用**：比直接调用慢约20%
- **内存分配**：大对象可能触发堆分配
- **类型断言**：有一定的运行时开销

### 优化建议
1. **避免频繁类型断言**
2. **使用具体类型而非接口**（性能敏感场景）
3. **接口设计要小而专一**

## 6. 面试要点

### 核心问题
1. **接口的零值是什么？**
   - nil，但nil接口值 ≠ nil指针

2. **接口如何实现多态？**
   - 通过方法表动态绑定

3. **空接口和非空接口的区别？**
   - 空接口用eface，非空接口用iface

4. **接口断言的原理？**
   - 比较接口中的类型信息

### 一句话总结
> Interface通过iface/eface结构体和方法表实现动态多态，支持类型断言和方法动态调用