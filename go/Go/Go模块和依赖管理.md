# Go模块和依赖管理

## 1. Go Modules基础

### 什么是Go Modules
Go Modules是Go 1.11引入的官方依赖管理系统，用于管理项目依赖和版本控制。

### 核心概念
- **Module**：相关包的集合，有版本控制
- **go.mod**：模块定义文件
- **go.sum**：依赖校验文件
- **Semantic Versioning**：语义化版本控制

## 2. 基本操作

### 初始化模块
```bash
# 创建新模块
go mod init example.com/myproject

# 生成的go.mod文件
module example.com/myproject

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
)
```

### 添加依赖
```bash
# 添加依赖
go get github.com/gin-gonic/gin

# 添加特定版本
go get github.com/gin-gonic/gin@v1.9.1

# 添加最新版本
go get github.com/gin-gonic/gin@latest

# 添加预发布版本
go get github.com/gin-gonic/gin@v1.10.0-beta.1
```

### 更新依赖
```bash
# 更新所有依赖
go get -u

# 更新特定依赖
go get -u github.com/gin-gonic/gin

# 更新到最新补丁版本
go get -u=patch
```

### 移除依赖
```bash
# 移除未使用的依赖
go mod tidy

# 手动移除依赖
go mod edit -droprequire github.com/unused/package
```

## 3. go.mod文件详解

### 基本结构
```go
module example.com/myproject

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
    github.com/stretchr/testify v1.8.4
)

require (
    // 间接依赖
    github.com/bytedance/sonic v1.9.1 // indirect
    github.com/chenzhuoyu/base64x v0.0.0-20221115062448-fe3a3abad311 // indirect
)

exclude (
    // 排除特定版本
    github.com/old/package v1.0.0
)

replace (
    // 替换依赖
    github.com/old/package => github.com/new/package v2.0.0
    github.com/local/package => ./local/package
)

retract (
    // 撤回版本
    v1.0.1 // 包含严重bug
)
```

### 版本选择规则
```bash
# 语义化版本
v1.2.3     # 精确版本
v1.2       # 最新的v1.2.x
v1         # 最新的v1.x.x
latest     # 最新版本

# 伪版本（用于未打tag的提交）
v0.0.0-20191109021931-daa7c04131f5

# 预发布版本
v1.2.3-beta.1
v1.2.3-rc.1
```

## 4. 依赖管理最佳实践

### 1. 版本固定策略
```go
// 生产环境：固定版本
require (
    github.com/gin-gonic/gin v1.9.1
    github.com/redis/go-redis/v9 v9.0.5
)

// 开发环境：可以使用latest
go get github.com/gin-gonic/gin@latest
```

### 2. 私有仓库配置
```bash
# 配置私有仓库
go env -w GOPRIVATE=github.com/mycompany/*
go env -w GOPROXY=direct

# 配置Git认证
git config --global url."**************:".insteadOf "https://github.com/"
```

### 3. 代理配置
```bash
# 设置代理
go env -w GOPROXY=https://goproxy.cn,direct
go env -w GOSUMDB=sum.golang.google.cn

# 禁用代理
go env -w GOPROXY=direct
go env -w GOSUMDB=off
```

## 5. 工作区模式（Go 1.18+）

### 创建工作区
```bash
# 初始化工作区
go work init ./module1 ./module2

# 生成的go.work文件
go 1.21

use (
    ./module1
    ./module2
)
```

### 工作区操作
```bash
# 添加模块到工作区
go work use ./module3

# 同步工作区
go work sync
```

## 6. 常见问题和解决方案

### 1. 依赖冲突
```bash
# 查看依赖图
go mod graph

# 查看特定包的依赖
go mod why github.com/gin-gonic/gin

# 解决冲突：使用replace
replace github.com/old/package => github.com/new/package v2.0.0
```

### 2. 缓存问题
```bash
# 清理模块缓存
go clean -modcache

# 查看缓存位置
go env GOMODCACHE
```

### 3. 校验失败
```bash
# 重新计算校验和
go mod tidy

# 验证依赖
go mod verify

# 下载依赖
go mod download
```

## 7. 发布模块

### 版本标记
```bash
# 创建版本标签
git tag v1.0.0
git push origin v1.0.0

# 主版本升级
git tag v2.0.0
git push origin v2.0.0
```

### 模块结构
```
mymodule/
├── go.mod
├── go.sum
├── README.md
├── LICENSE
├── main.go
└── internal/
    └── helper.go
```

### 向后兼容
```go
// v1版本
func OldFunction() string {
    return "old"
}

// v2版本 - 保持兼容
func OldFunction() string {
    return NewFunction()
}

func NewFunction() string {
    return "new"
}
```

## 8. 高级特性

### 1. 条件构建
```go
//go:build linux
// +build linux

package main

import "fmt"

func main() {
    fmt.Println("Linux specific code")
}
```

### 2. 内部包
```
mymodule/
├── go.mod
├── public.go          // 公开API
└── internal/
    └── private.go     // 内部实现
```

### 3. 供应商模式
```bash
# 创建vendor目录
go mod vendor

# 使用vendor构建
go build -mod=vendor
```

## 9. 性能优化

### 1. 模块代理
```bash
# 使用本地代理
go env -w GOPROXY=http://localhost:8080,direct

# 配置多个代理
go env -w GOPROXY=https://proxy1.com,https://proxy2.com,direct
```

### 2. 并行下载
```bash
# 设置并行下载数
go env -w GOMAXPROCS=8
```

## 10. 面试要点

### 核心问题
1. **Go Modules的优势？**
   - 版本管理、依赖隔离、可重现构建

2. **go.mod和go.sum的作用？**
   - go.mod定义依赖，go.sum校验完整性

3. **如何解决依赖冲突？**
   - 使用replace指令、升级依赖版本

4. **什么是最小版本选择？**
   - 选择满足所有约束的最小版本

### 最佳实践
- **定期运行go mod tidy**
- **提交go.sum到版本控制**
- **使用语义化版本**
- **避免使用replace在生产环境**

### 常见陷阱
- **忘记提交go.sum文件**
- **在库中使用replace指令**
- **不理解间接依赖**
- **版本约束过于严格**

### 一句话总结
> Go Modules通过语义化版本和最小版本选择算法，提供了可重现、可靠的依赖管理解决方案
