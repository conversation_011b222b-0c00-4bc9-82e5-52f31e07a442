# 性能分析工具pprof

## 1. pprof基础

### 什么是pprof
pprof是Go语言内置的性能分析工具，可以分析CPU使用、内存分配、阻塞情况等性能指标。

### 主要分析类型
- **CPU Profile**：CPU使用情况分析
- **Memory Profile**：内存分配分析
- **Block Profile**：阻塞分析
- **Mutex Profile**：互斥锁竞争分析
- **Goroutine Profile**：协程分析

## 2. 启用pprof

### HTTP服务集成
```go
package main

import (
    "log"
    "net/http"
    _ "net/http/pprof" // 导入pprof
)

func main() {
    // 启动pprof HTTP服务
    go func() {
        log.Println(http.ListenAndServe("localhost:6060", nil))
    }()
    
    // 你的应用代码
    select {}
}
```

### 手动Profile
```go
package main

import (
    "os"
    "runtime/pprof"
    "time"
)

func main() {
    // CPU Profile
    cpuFile, _ := os.Create("cpu.prof")
    defer cpuFile.Close()
    pprof.StartCPUProfile(cpuFile)
    defer pprof.StopCPUProfile()
    
    // 执行需要分析的代码
    doWork()
    
    // Memory Profile
    memFile, _ := os.Create("mem.prof")
    defer memFile.Close()
    pprof.WriteHeapProfile(memFile)
}

func doWork() {
    time.Sleep(2 * time.Second)
}
```

## 3. 数据收集

### HTTP端点访问
```bash
# 查看可用的profile类型
curl http://localhost:6060/debug/pprof/

# 获取CPU profile（30秒采样）
curl http://localhost:6060/debug/pprof/profile?seconds=30 > cpu.prof

# 获取内存profile
curl http://localhost:6060/debug/pprof/heap > heap.prof

# 获取goroutine信息
curl http://localhost:6060/debug/pprof/goroutine > goroutine.prof

# 获取阻塞信息
curl http://localhost:6060/debug/pprof/block > block.prof
```

### 命令行工具
```bash
# 直接分析在线服务
go tool pprof http://localhost:6060/debug/pprof/profile

# 分析本地文件
go tool pprof cpu.prof

# 对比两个profile
go tool pprof -base=old.prof new.prof
```

## 4. 分析方法

### CPU分析
```bash
# 进入交互模式
go tool pprof cpu.prof

# 常用命令
(pprof) top          # 显示CPU占用最高的函数
(pprof) top10        # 显示前10个函数
(pprof) list main    # 显示main函数的详细信息
(pprof) web          # 生成调用图（需要graphviz）
(pprof) png          # 生成PNG图片
(pprof) pdf          # 生成PDF文件
```

### 内存分析
```bash
go tool pprof heap.prof

# 内存分析命令
(pprof) top          # 按内存使用排序
(pprof) top -cum     # 按累计内存使用排序
(pprof) list func    # 查看函数内存分配详情
(pprof) peek regex   # 查看匹配正则的函数
```

### Web界面分析
```bash
# 启动Web界面
go tool pprof -http=:8080 cpu.prof

# 浏览器访问 http://localhost:8080
# 提供图形化界面，包括：
# - Top：函数排序列表
# - Graph：调用关系图
# - Flame Graph：火焰图
# - Source：源码视图
```

## 5. 实战案例

### 案例1：CPU热点分析
```go
package main

import (
    "fmt"
    "math"
    "time"
)

func main() {
    start := time.Now()
    
    // CPU密集型任务
    result := calculatePi(1000000)
    
    fmt.Printf("Pi: %f, Time: %v\n", result, time.Since(start))
}

func calculatePi(n int) float64 {
    var pi float64
    for i := 0; i < n; i++ {
        pi += math.Pow(-1, float64(i)) / (2*float64(i) + 1)
    }
    return pi * 4
}
```

分析结果：
```bash
$ go tool pprof cpu.prof
(pprof) top
Showing nodes accounting for 1.23s, 98.40% of 1.25s total
      flat  flat%   sum%        cum   cum%
     0.89s 71.20% 71.20%      0.89s 71.20%  math.Pow
     0.34s 27.20% 98.40%      1.23s 98.40%  main.calculatePi
```

### 案例2：内存泄漏检测
```go
package main

import (
    "time"
)

var globalSlice [][]byte

func main() {
    for i := 0; i < 1000; i++ {
        memoryLeak()
        time.Sleep(10 * time.Millisecond)
    }
}

func memoryLeak() {
    // 内存泄漏：持续向全局slice添加数据
    data := make([]byte, 1024*1024) // 1MB
    globalSlice = append(globalSlice, data)
}
```

分析结果：
```bash
$ go tool pprof heap.prof
(pprof) top
Showing nodes accounting for 1024MB, 100% of 1024MB total
      flat  flat%   sum%        cum   cum%
    1024MB   100%   100%     1024MB   100%  main.memoryLeak
```

### 案例3：Goroutine泄漏检测
```go
package main

import (
    "time"
)

func main() {
    for i := 0; i < 1000; i++ {
        go leakyGoroutine()
    }
    
    time.Sleep(10 * time.Second)
}

func leakyGoroutine() {
    // 永远不会退出的goroutine
    select {}
}
```

分析结果：
```bash
$ curl http://localhost:6060/debug/pprof/goroutine?debug=1
goroutine profile: total 1001
1000 @ 0x43a385 0x40b1f3 0x40b1c9 0x40b1c9 0x4001e1
#   0x40b1c9    main.leakyGoroutine+0x29    /path/to/main.go:15
```

## 6. 性能优化技巧

### 1. 减少内存分配
```go
// 优化前：频繁分配
func badConcat(strs []string) string {
    var result string
    for _, s := range strs {
        result += s // 每次都重新分配内存
    }
    return result
}

// 优化后：预分配
func goodConcat(strs []string) string {
    var builder strings.Builder
    builder.Grow(calculateTotalSize(strs))
    for _, s := range strs {
        builder.WriteString(s)
    }
    return builder.String()
}
```

### 2. 对象池复用
```go
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 0, 1024)
    },
}

func processData(data []byte) []byte {
    buf := bufferPool.Get().([]byte)
    defer bufferPool.Put(buf[:0])
    
    // 处理数据
    buf = append(buf, data...)
    return buf
}
```

### 3. 避免反射
```go
// 慢：使用反射
func slowMarshal(v interface{}) []byte {
    data, _ := json.Marshal(v)
    return data
}

// 快：直接序列化
func fastMarshal(u User) []byte {
    return []byte(fmt.Sprintf(`{"name":"%s","age":%d}`, u.Name, u.Age))
}
```

## 7. 监控集成

### Prometheus集成
```go
package main

import (
    "net/http"
    "github.com/prometheus/client_golang/prometheus/promhttp"
)

func main() {
    http.Handle("/metrics", promhttp.Handler())
    http.ListenAndServe(":8080", nil)
}
```

### 自定义指标
```go
var (
    requestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "HTTP request duration",
        },
        []string{"method", "endpoint"},
    )
)

func init() {
    prometheus.MustRegister(requestDuration)
}
```

## 8. 面试要点

### 核心问题
1. **pprof能分析什么？**
   - CPU、内存、阻塞、互斥锁、goroutine

2. **如何定位性能瓶颈？**
   - 使用top命令找热点函数，用list查看详情

3. **内存泄漏如何检测？**
   - 对比不同时间点的heap profile

4. **生产环境如何使用pprof？**
   - 通过HTTP端点，限制采样时间

### 最佳实践
- **定期进行性能分析**
- **关注内存分配热点**
- **监控goroutine数量**
- **使用基准测试验证优化效果**

### 常见陷阱
- **采样时间过短**
- **忽略内存分配频率**
- **只关注CPU不关注内存**
- **在生产环境长时间采样**

### 一句话总结
> pprof是Go性能优化的利器，通过可视化分析帮助定位瓶颈，指导性能优化方向
