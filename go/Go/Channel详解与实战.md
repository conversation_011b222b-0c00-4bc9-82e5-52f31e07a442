# Channel详解与实战

## 1. 核心概念

### Channel基本特性
- **类型安全**：只能传递指定类型的数据
- **同步机制**：提供goroutine间的同步和通信
- **阻塞特性**：发送和接收操作可能阻塞
- **方向性**：可以限制为只读或只写

### 创建方式
```go
// 无缓冲channel
ch := make(chan int)

// 有缓冲channel
ch := make(chan int, 10)

// 只读channel
var readOnly <-chan int = ch

// 只写channel
var writeOnly chan<- int = ch
```

## 2. Channel类型

### 无缓冲Channel（同步）
```go
func unbufferedExample() {
    ch := make(chan string)
    
    go func() {
        ch <- "hello" // 阻塞直到有接收者
    }()
    
    msg := <-ch // 阻塞直到有发送者
    fmt.Println(msg)
}
```

### 有缓冲Channel（异步）
```go
func bufferedExample() {
    ch := make(chan int, 3)
    
    // 不会阻塞，直到缓冲区满
    ch <- 1
    ch <- 2
    ch <- 3
    
    // 从缓冲区读取
    fmt.Println(<-ch) // 1
    fmt.Println(<-ch) // 2
}
```

## 3. Channel操作

### 基本操作
```go
// 发送
ch <- value

// 接收
value := <-ch

// 接收并检查是否关闭
value, ok := <-ch

// 关闭channel
close(ch)
```

### Select语句
```go
func selectExample() {
    ch1 := make(chan string)
    ch2 := make(chan string)
    
    go func() { ch1 <- "from ch1" }()
    go func() { ch2 <- "from ch2" }()
    
    select {
    case msg1 := <-ch1:
        fmt.Println(msg1)
    case msg2 := <-ch2:
        fmt.Println(msg2)
    case <-time.After(1 * time.Second):
        fmt.Println("timeout")
    default:
        fmt.Println("no channel ready")
    }
}
```

## 4. 常见模式

### 1. 工作池模式
```go
func workerPool() {
    jobs := make(chan int, 100)
    results := make(chan int, 100)
    
    // 启动3个worker
    for w := 1; w <= 3; w++ {
        go worker(w, jobs, results)
    }
    
    // 发送任务
    for j := 1; j <= 5; j++ {
        jobs <- j
    }
    close(jobs)
    
    // 收集结果
    for a := 1; a <= 5; a++ {
        <-results
    }
}

func worker(id int, jobs <-chan int, results chan<- int) {
    for j := range jobs {
        fmt.Printf("worker %d processing job %d\n", id, j)
        time.Sleep(time.Second)
        results <- j * 2
    }
}
```

### 2. 扇入模式（Fan-in）
```go
func fanIn(input1, input2 <-chan string) <-chan string {
    c := make(chan string)
    go func() {
        for {
            select {
            case s := <-input1:
                c <- s
            case s := <-input2:
                c <- s
            }
        }
    }()
    return c
}
```

### 3. 扇出模式（Fan-out）
```go
func fanOut(in <-chan int, out1, out2 chan<- int) {
    for val := range in {
        select {
        case out1 <- val:
        case out2 <- val:
        }
    }
}
```

### 4. 管道模式
```go
func pipeline() {
    // 阶段1：生成数字
    numbers := make(chan int)
    go func() {
        for i := 1; i <= 5; i++ {
            numbers <- i
        }
        close(numbers)
    }()
    
    // 阶段2：平方
    squares := make(chan int)
    go func() {
        for n := range numbers {
            squares <- n * n
        }
        close(squares)
    }()
    
    // 阶段3：输出
    for s := range squares {
        fmt.Println(s)
    }
}
```

## 5. Channel关闭

### 关闭规则
- **只有发送者关闭channel**
- **关闭后不能再发送数据**
- **可以从关闭的channel接收数据**
- **重复关闭会panic**

### 检查关闭状态
```go
func checkClosed() {
    ch := make(chan int, 2)
    ch <- 1
    ch <- 2
    close(ch)
    
    for {
        val, ok := <-ch
        if !ok {
            fmt.Println("channel closed")
            break
        }
        fmt.Println("received:", val)
    }
    
    // 或使用range
    ch2 := make(chan int, 2)
    ch2 <- 3
    ch2 <- 4
    close(ch2)
    
    for val := range ch2 {
        fmt.Println("range received:", val)
    }
}
```

## 6. 超时和取消

### 超时控制
```go
func timeoutExample() {
    ch := make(chan string, 1)
    
    go func() {
        time.Sleep(2 * time.Second)
        ch <- "result"
    }()
    
    select {
    case res := <-ch:
        fmt.Println(res)
    case <-time.After(1 * time.Second):
        fmt.Println("timeout")
    }
}
```

### Context取消
```go
func contextCancel(ctx context.Context) {
    ch := make(chan string)
    
    go func() {
        time.Sleep(2 * time.Second)
        ch <- "result"
    }()
    
    select {
    case res := <-ch:
        fmt.Println(res)
    case <-ctx.Done():
        fmt.Println("cancelled:", ctx.Err())
    }
}
```

## 7. 性能考虑

### 缓冲区大小选择
- **无缓冲**：强同步，性能较低
- **小缓冲**：减少阻塞，适合突发流量
- **大缓冲**：高吞吐，但占用更多内存

### 避免死锁
```go
// 错误：可能死锁
func deadlockExample() {
    ch := make(chan int)
    ch <- 1 // 阻塞，没有接收者
    <-ch
}

// 正确：使用goroutine
func correctExample() {
    ch := make(chan int)
    go func() {
        ch <- 1
    }()
    <-ch
}
```

## 8. 面试要点

### 核心问题
1. **Channel的底层实现？**
   - 环形队列 + 互斥锁 + 等待队列

2. **有缓冲和无缓冲的区别？**
   - 同步vs异步，阻塞行为不同

3. **如何优雅关闭Channel？**
   - 发送者关闭，接收者检查ok值

4. **Select的实现原理？**
   - 随机选择就绪的case，避免饥饿

### 常见陷阱
- **向关闭的channel发送数据会panic**
- **重复关闭channel会panic**
- **nil channel的读写都会阻塞**
- **select中的default会立即执行**

### 一句话总结
> Channel是Go的核心同步原语，通过"不要通过共享内存通信，而要通过通信共享内存"实现优雅的并发编程
