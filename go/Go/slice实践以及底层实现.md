# Slice 的实践与底层实现

## 1. 核心概念

### Slice 基本结构
`slice` 是对数组的动态窗口，包含三个核心属性：
- **长度（len）**：当前包含的元素数量
- **容量（cap）**：从起始位置到底层数组末尾的元素数量
- **指针（ptr）**：指向底层数组的指针

### 底层实现
```go
type slice struct {
    ptr *array  // 指向底层数组的指针
    len int     // 当前 slice 的长度
    cap int     // 当前 slice 的容量
}
```

## 2. 基本使用

### 创建方式
```go
// 1. 基于数组创建
arr := [5]int{1, 2, 3, 4, 5}
slice := arr[1:4] // [2, 3, 4]

// 2. 使用 make 函数
slice := make([]int, 5, 10) // 长度5，容量10

// 3. 字面量创建
slice := []int{1, 2, 3, 4, 5}
```

### 重要特性
- **共享底层数组**：多个切片可能共享同一底层数组
- **动态扩容**：容量不足时自动扩容
- **零拷贝切片**：切片操作不复制数据，只是调整指针和长度

## 3. 扩容机制

### 扩容规则
1. 如果新容量 > 2倍旧容量，则新容量 = 新申请容量
2. 否则：
   - 旧容量 < 1024：新容量 = 2倍旧容量
   - 旧容量 >= 1024：新容量 = 1.25倍旧容量
3. 最后根据元素大小进行内存对齐调整

### 扩容示例
```go
slice := make([]int, 0, 3)
slice = append(slice, 1, 2, 3) // [1 2 3] len=3 cap=3
slice = append(slice, 4)       // [1 2 3 4] len=4 cap=6 (扩容)
```

## 4. 常见陷阱

### 1. 共享底层数组问题
```go
arr := [5]int{1, 2, 3, 4, 5}
slice1 := arr[1:4] // [2, 3, 4]
slice2 := arr[2:5] // [3, 4, 5]

slice1[1] = 99 // 修改 slice1 影响 slice2
// slice1: [2, 99, 4]
// slice2: [99, 4, 5]
```

### 2. 内存泄漏
```go
// 问题：大slice的小切片会阻止大slice被回收
bigSlice := make([]byte, 1024*1024) // 1MB
smallSlice := bigSlice[:10]         // 只需要10字节

// 解决方案：复制需要的部分
safeCopy := make([]byte, 10)
copy(safeCopy, bigSlice[:10])
bigSlice = nil // 现在可以被回收
```

## 5. 最佳实践

### 性能优化
```go
// 1. 预分配容量
slice := make([]int, 0, expectedSize)

// 2. 安全复制
copied := append([]int(nil), original...)

// 3. 三索引切片控制容量
limited := slice[low:high:max] // 限制容量为 max-low
```

### 实用技巧
```go
// 栈操作
func (s *[]int) Push(v int) { *s = append(*s, v) }
func (s *[]int) Pop() int {
    old := *s
    n := len(old)
    *s = old[:n-1]
    return old[n-1]
}

// 队列操作
func (q *[]int) Enqueue(v int) { *q = append(*q, v) }
func (q *[]int) Dequeue() int {
    old := *q
    *q = old[1:]
    return old[0]
}
```

## 6. 面试要点

### 核心问题
1. **slice和array的区别？**
   - array大小固定，slice大小可变
   - array是值传递，slice是引用传递
   - slice包含指针、长度、容量三个字段

2. **slice扩容时发生了什么？**
   - 分配新的更大底层数组
   - 复制原数组数据到新数组
   - 更新slice指向新数组
   - 旧数组等待GC回收

3. **如何避免slice内存泄漏？**
   - 及时释放大slice的引用
   - 复制需要的小部分数据
   - 使用三索引切片限制容量

### 一句话总结
> Slice是Go的动态数组，底层由指针+长度+容量组成，支持自动扩容和零拷贝切片操作

