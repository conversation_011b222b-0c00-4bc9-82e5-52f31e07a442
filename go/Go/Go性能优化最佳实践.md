# Go性能优化最佳实践

## 内存优化

### 1. 减少内存分配
- **对象池**：复用对象减少GC压力
- **预分配**：提前分配足够容量的slice/map
- **避免装箱**：减少interface{}的使用

```go
// 使用sync.Pool
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 0, 1024)
    },
}

// 预分配slice
slice := make([]int, 0, expectedSize)
```

### 2. 字符串优化
- **strings.Builder**：高效字符串拼接
- **避免频繁转换**：减少[]byte和string转换
- **字符串常量**：使用常量池

```go
// 高效拼接
var builder strings.Builder
builder.Grow(expectedSize) // 预分配
builder.WriteString("hello")
result := builder.String()
```

### 3. 切片优化
- **容量管理**：避免频繁扩容
- **切片重用**：重置长度而非重新分配
- **内存泄漏**：及时释放大切片引用

```go
// 重用切片
slice = slice[:0] // 重置长度，保留容量

// 避免内存泄漏
func processLargeSlice(data []byte) []byte {
    result := make([]byte, len(smallPart))
    copy(result, data[start:end]) // 复制而非引用
    return result
}
```

## CPU优化

### 1. 算法优化
- **时间复杂度**：选择合适的算法和数据结构
- **空间换时间**：使用缓存减少重复计算
- **并行计算**：利用多核CPU

### 2. 循环优化
- **减少函数调用**：内联简单函数
- **循环展开**：减少循环开销
- **缓存友好**：顺序访问内存

```go
// 缓存友好的访问模式
for i := 0; i < rows; i++ {
    for j := 0; j < cols; j++ {
        matrix[i][j] = value // 按行访问
    }
}
```

### 3. 分支预测
- **减少分支**：使用位运算替代条件判断
- **热路径优化**：将常见情况放在前面
- **避免随机分支**：保持分支的可预测性

## 并发优化

### 1. Goroutine管理
- **控制数量**：避免创建过多goroutine
- **工作池**：复用goroutine减少创建开销
- **合理粒度**：平衡并发度和开销

```go
// 工作池模式
type WorkerPool struct {
    jobs    chan Job
    workers int
}

func (p *WorkerPool) Start() {
    for i := 0; i < p.workers; i++ {
        go p.worker()
    }
}
```

### 2. 锁优化
- **减少锁粒度**：细化锁的范围
- **读写锁**：读多写少场景使用RWMutex
- **无锁编程**：使用原子操作

```go
// 原子操作替代锁
var counter int64
atomic.AddInt64(&counter, 1)
```

### 3. Channel优化
- **缓冲大小**：合理设置channel缓冲
- **避免阻塞**：使用select的default分支
- **批量处理**：减少channel操作次数

## I/O优化

### 1. 网络I/O
- **连接池**：复用网络连接
- **批量操作**：减少网络往返次数
- **异步处理**：使用goroutine处理I/O

### 2. 文件I/O
- **缓冲I/O**：使用bufio包
- **批量读写**：减少系统调用次数
- **内存映射**：大文件使用mmap

```go
// 缓冲读取
file, _ := os.Open("large.txt")
reader := bufio.NewReader(file)
```

## 编译优化

### 1. 编译选项
- **优化级别**：使用-O2优化
- **内联**：-gcflags="-l=4"增加内联
- **逃逸分析**：-gcflags="-m"查看逃逸

### 2. 构建标签
- **条件编译**：使用build tags
- **平台优化**：针对特定平台优化
- **调试信息**：生产环境去除调试信息

## 性能分析

### 1. pprof工具
- **CPU分析**：找出热点函数
- **内存分析**：检查内存使用
- **阻塞分析**：发现并发瓶颈

```go
import _ "net/http/pprof"

// 启动pprof服务
go func() {
    log.Println(http.ListenAndServe("localhost:6060", nil))
}()
```

### 2. 基准测试
- **Benchmark**：测量函数性能
- **对比测试**：比较不同实现
- **内存分配**：检查分配次数

```go
func BenchmarkStringConcat(b *testing.B) {
    for i := 0; i < b.N; i++ {
        _ = "hello" + "world"
    }
}
```

### 3. 监控指标
- **GC指标**：垃圾回收频率和耗时
- **内存使用**：堆内存和栈内存
- **CPU使用率**：系统和用户态时间

## 常见陷阱

### 1. 内存泄漏
- **Goroutine泄漏**：确保goroutine能够退出
- **定时器泄漏**：及时停止Timer和Ticker
- **闭包引用**：避免闭包持有大对象

### 2. 性能陷阱
- **反射开销**：避免在热路径使用反射
- **接口转换**：减少不必要的类型断言
- **字符串操作**：避免频繁的字符串拼接

### 3. 并发陷阱
- **竞态条件**：使用-race检测
- **死锁**：避免循环等待
- **上下文切换**：控制goroutine数量

## 面试要点

### 1. 优化策略
- **如何定位性能瓶颈？** 使用pprof工具分析
- **内存优化方法？** 对象池、预分配、减少装箱
- **并发优化技巧？** 工作池、原子操作、合理锁粒度

### 2. 工具使用
- **pprof如何使用？** CPU、内存、阻塞分析
- **基准测试怎么写？** Benchmark函数和性能对比
- **GC调优参数？** GOGC、GOMEMLIMIT等环境变量

### 3. 实际案例
- **字符串拼接优化？** strings.Builder vs += 操作
- **map并发安全？** sync.Map vs 读写锁
- **slice扩容机制？** 容量翻倍策略和内存复制
