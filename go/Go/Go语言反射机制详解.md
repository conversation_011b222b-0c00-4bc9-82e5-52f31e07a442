Go语言的反射（reflection）是一种在运行时检查和操作类型信息的机制。通过反射，程序可以在运行时获取变量的类型信息、调用方法、修改字段值等。反射是Go语言中一个强大但需要谨慎使用的特性。

### 1. **反射的基本概念**

反射基于Go语言的接口机制，主要通过`reflect`包提供的`Type`和`Value`两个核心类型来实现。

```go
import (
    "fmt"
    "reflect"
)

func basicReflection() {
    var x float64 = 3.4
    
    // 获取类型信息
    t := reflect.TypeOf(x)
    fmt.Println("Type:", t) // Type: float64
    
    // 获取值信息
    v := reflect.ValueOf(x)
    fmt.Println("Value:", v) // Value: 3.4
    fmt.Println("Kind:", v.Kind()) // Kind: float64
}
```

### 2. **Type和Value的关系**

#### **Type接口**
```go
type Type interface {
    // 基本信息
    Name() string        // 类型名称
    Kind() Kind         // 类型种类
    Size() uintptr      // 类型大小
    
    // 结构体相关
    NumField() int                 // 字段数量
    Field(i int) StructField      // 获取字段
    FieldByName(name string) (StructField, bool)
    
    // 方法相关
    NumMethod() int               // 方法数量
    Method(i int) Method         // 获取方法
    MethodByName(name string) (Method, bool)
    
    // 其他类型相关
    Elem() Type                  // 指针、切片、数组、通道、映射的元素类型
    Key() Type                   // 映射的键类型
    // ...
}
```

#### **Value结构体**
```go
type Value struct {
    typ *rtype          // 类型信息
    ptr unsafe.Pointer  // 数据指针
    flag uintptr        // 标志位
}

// Value的主要方法
func (v Value) Type() Type
func (v Value) Kind() Kind
func (v Value) Interface() interface{}
func (v Value) CanSet() bool
func (v Value) Set(x Value)
// ...
```

### 3. **反射的三大定律**

#### **定律1：反射可以从接口值得到反射对象**
```go
func law1() {
    var x float64 = 3.4
    
    // interface{} -> reflect.Type
    t := reflect.TypeOf(x)
    fmt.Println("Type:", t)
    
    // interface{} -> reflect.Value
    v := reflect.ValueOf(x)
    fmt.Println("Value:", v)
}
```

#### **定律2：反射可以从反射对象得到接口值**
```go
func law2() {
    var x float64 = 3.4
    v := reflect.ValueOf(x)
    
    // reflect.Value -> interface{}
    y := v.Interface().(float64)
    fmt.Println("Recovered value:", y)
}
```

#### **定律3：要修改反射对象，其值必须可设置**
```go
func law3() {
    var x float64 = 3.4
    
    // 直接传值，不可修改
    v := reflect.ValueOf(x)
    fmt.Println("CanSet:", v.CanSet()) // false
    
    // 传指针，可以修改
    p := reflect.ValueOf(&x)
    fmt.Println("Pointer CanSet:", p.CanSet()) // false
    
    // 获取指针指向的元素
    elem := p.Elem()
    fmt.Println("Elem CanSet:", elem.CanSet()) // true
    
    // 修改值
    elem.SetFloat(7.1)
    fmt.Println("Modified x:", x) // 7.1
}
```

### 4. **结构体反射**

#### **获取结构体信息**
```go
type Person struct {
    Name string `json:"name" validate:"required"`
    Age  int    `json:"age" validate:"min=0,max=120"`
    Email string `json:"email,omitempty"`
}

func structReflection() {
    p := Person{Name: "Alice", Age: 30, Email: "<EMAIL>"}
    
    t := reflect.TypeOf(p)
    v := reflect.ValueOf(p)
    
    fmt.Printf("Type: %s, Kind: %s\n", t.Name(), t.Kind())
    
    // 遍历字段
    for i := 0; i < t.NumField(); i++ {
        field := t.Field(i)
        value := v.Field(i)
        
        fmt.Printf("Field %d: %s %s = %v\n", 
            i, field.Name, field.Type, value.Interface())
        
        // 获取标签
        jsonTag := field.Tag.Get("json")
        validateTag := field.Tag.Get("validate")
        fmt.Printf("  Tags: json=%s, validate=%s\n", jsonTag, validateTag)
    }
}
```

#### **修改结构体字段**
```go
func modifyStruct() {
    p := Person{Name: "Bob", Age: 25}
    
    v := reflect.ValueOf(&p).Elem() // 获取可修改的Value
    
    // 通过索引修改
    nameField := v.Field(0)
    if nameField.CanSet() {
        nameField.SetString("Charlie")
    }
    
    // 通过名称修改
    ageField := v.FieldByName("Age")
    if ageField.CanSet() {
        ageField.SetInt(35)
    }
    
    fmt.Printf("Modified: %+v\n", p)
}
```

### 5. **方法反射**

#### **调用方法**
```go
type Calculator struct{}

func (c Calculator) Add(a, b int) int {
    return a + b
}

func (c Calculator) Multiply(a, b int) int {
    return a * b
}

func methodReflection() {
    calc := Calculator{}
    v := reflect.ValueOf(calc)
    t := reflect.TypeOf(calc)
    
    // 遍历方法
    for i := 0; i < t.NumMethod(); i++ {
        method := t.Method(i)
        fmt.Printf("Method %d: %s\n", i, method.Name)
    }
    
    // 调用方法
    addMethod := v.MethodByName("Add")
    if addMethod.IsValid() {
        args := []reflect.Value{
            reflect.ValueOf(10),
            reflect.ValueOf(20),
        }
        result := addMethod.Call(args)
        fmt.Printf("Add result: %v\n", result[0].Int())
    }
}
```

#### **动态方法调用**
```go
func dynamicMethodCall(obj interface{}, methodName string, args ...interface{}) []reflect.Value {
    v := reflect.ValueOf(obj)
    method := v.MethodByName(methodName)
    
    if !method.IsValid() {
        panic(fmt.Sprintf("Method %s not found", methodName))
    }
    
    // 转换参数
    in := make([]reflect.Value, len(args))
    for i, arg := range args {
        in[i] = reflect.ValueOf(arg)
    }
    
    return method.Call(in)
}
```

### 6. **切片和映射反射**

#### **切片反射**
```go
func sliceReflection() {
    slice := []int{1, 2, 3, 4, 5}
    v := reflect.ValueOf(slice)
    
    fmt.Printf("Length: %d, Capacity: %d\n", v.Len(), v.Cap())
    
    // 遍历元素
    for i := 0; i < v.Len(); i++ {
        elem := v.Index(i)
        fmt.Printf("Element %d: %v\n", i, elem.Int())
    }
    
    // 动态创建切片
    sliceType := reflect.SliceOf(reflect.TypeOf(0))
    newSlice := reflect.MakeSlice(sliceType, 0, 10)
    
    // 添加元素
    newSlice = reflect.Append(newSlice, reflect.ValueOf(100))
    newSlice = reflect.Append(newSlice, reflect.ValueOf(200))
    
    fmt.Printf("New slice: %v\n", newSlice.Interface())
}
```

#### **映射反射**
```go
func mapReflection() {
    m := map[string]int{"a": 1, "b": 2, "c": 3}
    v := reflect.ValueOf(m)
    
    // 遍历映射
    for _, key := range v.MapKeys() {
        value := v.MapIndex(key)
        fmt.Printf("%s: %d\n", key.String(), value.Int())
    }
    
    // 动态创建映射
    mapType := reflect.MapOf(reflect.TypeOf(""), reflect.TypeOf(0))
    newMap := reflect.MakeMap(mapType)
    
    // 设置值
    newMap.SetMapIndex(reflect.ValueOf("x"), reflect.ValueOf(10))
    newMap.SetMapIndex(reflect.ValueOf("y"), reflect.ValueOf(20))
    
    fmt.Printf("New map: %v\n", newMap.Interface())
}
```

### 7. **反射的高级应用**

#### **JSON序列化实现**
```go
func simpleJSONMarshal(v interface{}) (string, error) {
    val := reflect.ValueOf(v)
    typ := reflect.TypeOf(v)
    
    if typ.Kind() != reflect.Struct {
        return "", fmt.Errorf("only struct supported")
    }
    
    var result []string
    for i := 0; i < val.NumField(); i++ {
        field := typ.Field(i)
        value := val.Field(i)
        
        if !value.CanInterface() {
            continue
        }
        
        jsonName := field.Tag.Get("json")
        if jsonName == "" {
            jsonName = field.Name
        }
        
        var valueStr string
        switch value.Kind() {
        case reflect.String:
            valueStr = fmt.Sprintf(`"%s"`, value.String())
        case reflect.Int, reflect.Int64:
            valueStr = fmt.Sprintf("%d", value.Int())
        case reflect.Float64:
            valueStr = fmt.Sprintf("%f", value.Float())
        default:
            valueStr = fmt.Sprintf(`"%v"`, value.Interface())
        }
        
        result = append(result, fmt.Sprintf(`"%s":%s`, jsonName, valueStr))
    }
    
    return "{" + strings.Join(result, ",") + "}", nil
}
```

#### **依赖注入容器**
```go
type Container struct {
    services map[reflect.Type]reflect.Value
}

func NewContainer() *Container {
    return &Container{
        services: make(map[reflect.Type]reflect.Value),
    }
}

func (c *Container) Register(service interface{}) {
    t := reflect.TypeOf(service)
    v := reflect.ValueOf(service)
    c.services[t] = v
}

func (c *Container) Get(serviceType reflect.Type) (interface{}, error) {
    if service, ok := c.services[serviceType]; ok {
        return service.Interface(), nil
    }
    return nil, fmt.Errorf("service not found: %v", serviceType)
}

func (c *Container) Inject(target interface{}) error {
    v := reflect.ValueOf(target).Elem()
    t := v.Type()
    
    for i := 0; i < v.NumField(); i++ {
        field := v.Field(i)
        fieldType := t.Field(i)
        
        if tag := fieldType.Tag.Get("inject"); tag == "true" {
            if service, err := c.Get(field.Type()); err == nil {
                field.Set(reflect.ValueOf(service))
            }
        }
    }
    
    return nil
}
```

### 8. **反射的性能考虑**

#### **性能对比**
```go
func BenchmarkDirectCall(b *testing.B) {
    calc := Calculator{}
    for i := 0; i < b.N; i++ {
        _ = calc.Add(10, 20)
    }
}

func BenchmarkReflectionCall(b *testing.B) {
    calc := Calculator{}
    v := reflect.ValueOf(calc)
    method := v.MethodByName("Add")
    args := []reflect.Value{reflect.ValueOf(10), reflect.ValueOf(20)}
    
    for i := 0; i < b.N; i++ {
        _ = method.Call(args)
    }
}
```

#### **优化策略**
```go
// 缓存反射信息
var methodCache = make(map[string]reflect.Value)

func cachedMethodCall(obj interface{}, methodName string) {
    key := fmt.Sprintf("%T.%s", obj, methodName)
    
    method, ok := methodCache[key]
    if !ok {
        v := reflect.ValueOf(obj)
        method = v.MethodByName(methodName)
        methodCache[key] = method
    }
    
    // 使用缓存的method
    method.Call(nil)
}
```

### 9. **反射的限制和注意事项**

#### **类型安全问题**
```go
func typeSafetyIssues() {
    var x int = 42
    v := reflect.ValueOf(&x).Elem()
    
    // 运行时错误，而不是编译时错误
    // v.SetString("hello") // panic: reflect: call of reflect.Value.SetString on int Value
    
    // 正确的类型检查
    if v.Kind() == reflect.Int {
        v.SetInt(100)
    }
}
```

#### **性能开销**
- 反射比直接调用慢10-100倍
- 涉及大量的运行时类型检查
- 无法进行编译时优化

#### **代码可读性**
- 反射代码通常比较复杂
- 难以理解和维护
- 错误只能在运行时发现

### 10. **面试常见问题**

#### **问题1：反射的零值**
```go
func reflectionZeroValue() {
    var v reflect.Value
    fmt.Println("Zero Value IsValid:", v.IsValid()) // false
    
    // v.Interface() // panic: reflect: call of reflect.Value.Interface on zero Value
    
    if v.IsValid() {
        fmt.Println(v.Interface())
    }
}
```

#### **问题2：反射与接口**
```go
func reflectionInterface() {
    var i interface{} = 42
    
    v := reflect.ValueOf(i)
    fmt.Println("Type:", v.Type())     // int
    fmt.Println("Kind:", v.Kind())     // int
    fmt.Println("Value:", v.Int())     // 42
    
    // 获取原始值
    original := v.Interface().(int)
    fmt.Println("Original:", original) // 42
}
```

### 总结

Go语言的反射机制提供了强大的运行时类型操作能力，主要应用场景包括：

1. **序列化/反序列化**：JSON、XML等格式转换
2. **ORM框架**：数据库映射
3. **依赖注入**：框架级别的对象管理
4. **测试框架**：动态测试用例生成
5. **配置解析**：动态配置映射

使用反射时需要注意：
- **性能开销**：谨慎在性能敏感的代码中使用
- **类型安全**：做好运行时类型检查
- **代码可读性**：平衡灵活性和可维护性
- **错误处理**：反射操作可能引发panic

反射是一把双刃剑，正确使用能够提供极大的灵活性，但滥用会导致性能问题和代码难以维护。
