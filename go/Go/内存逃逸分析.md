# 内存逃逸分析

## 1. 核心概念

### 什么是内存逃逸
内存逃逸是指本应分配在栈上的变量，由于某些原因被分配到堆上的现象。

### 栈 vs 堆
| 特性 | 栈 | 堆 |
|------|----|----|
| 分配速度 | 快 | 慢 |
| 回收方式 | 自动（函数返回） | GC回收 |
| 内存布局 | 连续 | 可能不连续 |
| 并发安全 | 天然安全 | 需要同步 |

## 2. 逃逸分析工具

### 编译时分析
```bash
# 查看逃逸分析结果
go build -gcflags="-m" main.go

# 更详细的分析
go build -gcflags="-m -m" main.go

# 禁用内联优化
go build -gcflags="-m -l" main.go
```

### 运行时分析
```bash
# 查看内存分配
go tool pprof http://localhost:6060/debug/pprof/heap

# 查看内存分配统计
GODEBUG=gctrace=1 go run main.go
```

## 3. 逃逸场景分析

### 1. 返回局部变量指针
```go
// 逃逸：返回局部变量地址
func escapeReturn() *int {
    x := 42
    return &x // x逃逸到堆
}

// 不逃逸：返回值拷贝
func noEscapeReturn() int {
    x := 42
    return x // x在栈上
}
```

### 2. 接口类型赋值
```go
// 逃逸：赋值给接口
func escapeInterface() {
    x := 42
    var i interface{} = x // x逃逸到堆
    fmt.Println(i)
}

// 不逃逸：直接使用
func noEscapeInterface() {
    x := 42
    fmt.Println(x) // x在栈上
}
```

### 3. 闭包引用
```go
// 逃逸：闭包引用外部变量
func escapeClosure() func() int {
    x := 42
    return func() int {
        return x // x逃逸到堆
    }
}

// 不逃逸：不被闭包引用
func noEscapeClosure() {
    x := 42
    func() {
        y := x // y在栈上，x也在栈上
        fmt.Println(y)
    }()
}
```

### 4. 切片动态扩容
```go
// 可能逃逸：切片扩容
func escapeSlice() {
    s := make([]int, 0, 10)
    for i := 0; i < 1000; i++ {
        s = append(s, i) // 扩容时可能逃逸
    }
}

// 不逃逸：预分配足够容量
func noEscapeSlice() {
    s := make([]int, 0, 1000)
    for i := 0; i < 1000; i++ {
        s = append(s, i) // 不会扩容，不逃逸
    }
}
```

### 5. 大对象分配
```go
// 逃逸：大对象
func escapeLargeObject() {
    // 大于32KB的对象直接分配到堆
    data := make([]byte, 64*1024)
    _ = data
}

// 不逃逸：小对象
func noEscapeLargeObject() {
    data := make([]byte, 1024)
    _ = data
}
```

## 4. 实际案例分析

### 案例1：字符串拼接
```go
// 逃逸版本
func concatEscape(strs []string) string {
    var result string
    for _, s := range strs {
        result += s // 每次拼接都可能逃逸
    }
    return result
}

// 优化版本
func concatNoEscape(strs []string) string {
    var builder strings.Builder
    builder.Grow(calculateSize(strs)) // 预分配
    for _, s := range strs {
        builder.WriteString(s)
    }
    return builder.String()
}
```

### 案例2：JSON序列化
```go
type User struct {
    Name string `json:"name"`
    Age  int    `json:"age"`
}

// 逃逸版本
func jsonEscape(u User) []byte {
    data, _ := json.Marshal(u) // u逃逸到堆
    return data
}

// 优化版本
func jsonNoEscape(u User) []byte {
    var buf bytes.Buffer
    encoder := json.NewEncoder(&buf)
    encoder.Encode(u)
    return buf.Bytes()
}
```

### 案例3：goroutine参数传递
```go
// 逃逸版本
func goroutineEscape() {
    for i := 0; i < 10; i++ {
        go func() {
            fmt.Println(i) // i逃逸，闭包引用
        }()
    }
}

// 优化版本
func goroutineNoEscape() {
    for i := 0; i < 10; i++ {
        go func(val int) {
            fmt.Println(val) // val不逃逸，参数传递
        }(i)
    }
}
```

## 5. 优化策略

### 1. 避免返回局部变量指针
```go
// 不好
func bad() *User {
    u := User{Name: "Alice"}
    return &u // 逃逸
}

// 好
func good() User {
    return User{Name: "Alice"} // 不逃逸
}
```

### 2. 减少接口使用
```go
// 不好
func bad(data interface{}) {
    fmt.Println(data) // data逃逸
}

// 好
func good(data string) {
    fmt.Println(data) // data不逃逸
}
```

### 3. 预分配内存
```go
// 不好
func bad() {
    s := []int{}
    for i := 0; i < 1000; i++ {
        s = append(s, i) // 多次扩容逃逸
    }
}

// 好
func good() {
    s := make([]int, 0, 1000)
    for i := 0; i < 1000; i++ {
        s = append(s, i) // 预分配，不逃逸
    }
}
```

### 4. 使用对象池
```go
var userPool = sync.Pool{
    New: func() interface{} {
        return &User{}
    },
}

func usePool() {
    u := userPool.Get().(*User)
    defer userPool.Put(u)
    
    // 使用u...
}
```

## 6. 性能影响

### 内存分配对比
```go
func BenchmarkStackAlloc(b *testing.B) {
    for i := 0; i < b.N; i++ {
        x := 42
        _ = x
    }
}

func BenchmarkHeapAlloc(b *testing.B) {
    for i := 0; i < b.N; i++ {
        x := 42
        _ = &x // 强制逃逸
    }
}
```

### GC压力
- **栈分配**：无GC压力，函数返回自动回收
- **堆分配**：增加GC压力，影响程序性能

## 7. 面试要点

### 核心问题
1. **什么是内存逃逸？**
   - 本应在栈上的变量被分配到堆上

2. **常见逃逸场景？**
   - 返回指针、接口赋值、闭包引用、大对象

3. **如何分析逃逸？**
   - 使用-gcflags="-m"编译选项

4. **如何优化逃逸？**
   - 避免返回指针、减少接口使用、预分配内存

### 优化原则
- **能用栈就不用堆**
- **能预分配就不动态扩容**
- **能传值就不传指针**
- **能用具体类型就不用接口**

### 一句话总结
> 内存逃逸分析帮助识别堆分配，通过优化减少GC压力，提升程序性能
