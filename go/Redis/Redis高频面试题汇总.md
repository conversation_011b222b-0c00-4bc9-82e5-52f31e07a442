# Redis高频面试题汇总

## 基础概念

### 1. Redis的数据类型及应用场景？
**String（字符串）**：
- 缓存、计数器、分布式锁
- 命令：SET、GET、INCR、DECR

**Hash（哈希）**：
- 存储对象、用户信息
- 命令：HSET、HGET、HMGET

**List（列表）**：
- 消息队列、最新消息列表
- 命令：LPUSH、RPOP、LRANGE

**Set（集合）**：
- 标签、好友关系、去重
- 命令：SADD、SMEMBERS、SINTER

**ZSet（有序集合）**：
- 排行榜、延时队列
- 命令：ZADD、ZRANGE、ZRANK

### 2. Redis为什么这么快？
1. **内存存储**：数据存储在内存中
2. **单线程模型**：避免线程切换和锁竞争
3. **IO多路复用**：epoll/kqueue高效处理网络IO
4. **高效数据结构**：针对不同场景优化的数据结构
5. **简单协议**：RESP协议简单高效

### 3. Redis单线程模型？
**6.0之前**：
- 网络IO和命令执行都是单线程
- 通过IO多路复用处理并发连接

**6.0之后**：
- 网络IO多线程化
- 命令执行仍然是单线程
- 提高网络IO处理能力

```go
// Go客户端示例
rdb := redis.NewClient(&redis.Options{
    Addr:     "localhost:6379",
    Password: "",
    DB:       0,
})

// 设置值
err := rdb.Set(ctx, "key", "value", 0).Err()
if err != nil {
    panic(err)
}

// 获取值
val, err := rdb.Get(ctx, "key").Result()
```

## 持久化机制

### 4. RDB和AOF的区别？
**RDB（Redis Database）**：
- **原理**：定期生成数据快照
- **优点**：文件小、恢复快、对性能影响小
- **缺点**：可能丢失最后一次快照后的数据
- **适用**：对数据完整性要求不高的场景

**AOF（Append Only File）**：
- **原理**：记录每个写操作命令
- **优点**：数据完整性好、可读性强
- **缺点**：文件大、恢复慢、对性能影响大
- **适用**：对数据完整性要求高的场景

### 5. AOF重写机制？
**触发条件**：
- AOF文件大小超过阈值
- AOF文件增长比例超过配置值

**重写过程**：
1. fork子进程进行重写
2. 子进程根据内存数据生成新AOF文件
3. 主进程继续处理命令，写入AOF缓冲区
4. 重写完成后替换旧AOF文件

## 内存管理

### 6. Redis内存淘汰策略？
**8种淘汰策略**：
1. **noeviction**：不淘汰，返回错误
2. **allkeys-lru**：所有key中淘汰最近最少使用
3. **allkeys-lfu**：所有key中淘汰最少使用频率
4. **allkeys-random**：所有key中随机淘汰
5. **volatile-lru**：过期key中淘汰最近最少使用
6. **volatile-lfu**：过期key中淘汰最少使用频率
7. **volatile-random**：过期key中随机淘汰
8. **volatile-ttl**：淘汰即将过期的key

### 7. Redis过期删除策略？
**三种策略**：
1. **定时删除**：设置定时器，到期立即删除
2. **惰性删除**：访问时检查是否过期
3. **定期删除**：定期随机检查并删除过期key

**Redis采用**：惰性删除 + 定期删除

## 高可用方案

### 8. Redis主从复制原理？
**复制过程**：
1. **全量复制**：
   - 从库发送PSYNC命令
   - 主库执行BGSAVE生成RDB文件
   - 主库发送RDB文件给从库
   - 从库加载RDB文件

2. **增量复制**：
   - 主库将写命令发送给从库
   - 从库执行命令保持数据同步

### 9. Redis Sentinel哨兵机制？
**主要功能**：
- **监控**：监控主从服务器状态
- **通知**：故障时通知管理员
- **故障转移**：自动进行主从切换
- **配置提供**：为客户端提供服务发现

**故障转移流程**：
1. 检测主库下线
2. 选举领导者Sentinel
3. 从从库中选择新主库
4. 更新配置并通知客户端

### 10. Redis Cluster集群？
**特点**：
- 数据分片存储（16384个槽位）
- 无中心架构
- 支持在线扩容
- 自动故障转移

**数据分布**：
```
CRC16(key) % 16384 = slot
```

**集群通信**：
- Gossip协议进行节点通信
- 每个节点都知道集群状态

## 缓存问题

### 11. 缓存穿透、击穿、雪崩？
**缓存穿透**：
- **问题**：查询不存在的数据，缓存和数据库都没有
- **解决**：布隆过滤器、缓存空值

**缓存击穿**：
- **问题**：热点key过期，大量请求直接访问数据库
- **解决**：互斥锁、永不过期

**缓存雪崩**：
- **问题**：大量key同时过期，数据库压力激增
- **解决**：过期时间随机化、多级缓存

### 12. 如何保证缓存和数据库一致性？
**策略选择**：
1. **Cache Aside**：应用程序管理缓存
2. **Read Through**：缓存代理读操作
3. **Write Through**：缓存代理写操作
4. **Write Behind**：异步写入数据库

**实现方案**：
```go
// 更新数据的正确姿势
func UpdateUser(userID int, user User) error {
    // 1. 先更新数据库
    if err := db.UpdateUser(userID, user); err != nil {
        return err
    }
    
    // 2. 删除缓存（而不是更新缓存）
    cacheKey := fmt.Sprintf("user:%d", userID)
    redis.Del(cacheKey)
    
    return nil
}
```

## 分布式锁

### 13. Redis分布式锁实现？
**基本实现**：
```go
func AcquireLock(key string, value string, expiration time.Duration) bool {
    result := rdb.SetNX(ctx, key, value, expiration).Val()
    return result
}

func ReleaseLock(key string, value string) bool {
    script := `
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
    `
    result := rdb.Eval(ctx, script, []string{key}, value).Val()
    return result.(int64) == 1
}
```

**Redlock算法**：
- 向多个Redis实例申请锁
- 超过半数成功才算获取锁成功
- 提高分布式锁的可靠性

### 14. 分布式锁的问题？
**常见问题**：
1. **锁超时**：业务执行时间超过锁过期时间
2. **死锁**：客户端崩溃导致锁无法释放
3. **误删锁**：删除了其他客户端的锁

**解决方案**：
- 设置合理的过期时间
- 使用唯一标识符
- 锁续期机制

## 性能优化

### 15. Redis性能优化方法？
**数据结构优化**：
- 选择合适的数据类型
- 控制key的长度
- 使用压缩列表等紧凑结构

**内存优化**：
- 设置合理的过期时间
- 使用内存淘汰策略
- 避免大key

**网络优化**：
- 使用pipeline批量操作
- 减少网络往返次数
- 使用连接池

**配置优化**：
- 调整maxmemory参数
- 优化持久化配置
- 合理设置超时参数

## 面试要点总结
1. **数据结构**：5种基本类型及其应用场景
2. **持久化**：RDB和AOF的原理和区别
3. **高可用**：主从复制、Sentinel、Cluster
4. **缓存问题**：穿透、击穿、雪崩的解决方案
5. **分布式锁**：实现原理和常见问题
6. **性能优化**：内存、网络、配置等方面的优化
