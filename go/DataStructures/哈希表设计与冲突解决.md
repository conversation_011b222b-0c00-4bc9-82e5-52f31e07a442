# 哈希表设计与冲突解决

## 哈希表基础

### 1. 核心概念
- **哈希函数**：将键映射到数组索引的函数
- **哈希冲突**：不同键映射到相同索引的情况
- **负载因子**：元素数量/表大小，影响性能

### 2. 设计要求
- **均匀分布**：减少冲突概率
- **快速计算**：O(1)时间复杂度
- **确定性**：相同输入产生相同输出

## 哈希函数设计

### 1. 常见方法
- **除留余数法**：`hash(key) = key % table_size`
- **乘法散列法**：`hash(key) = (key * A) % table_size`
- **位运算法**：利用位操作提高效率

### 2. 选择原则
- **表大小选择**：通常选择质数，减少冲突
- **分布均匀性**：避免聚集现象
- **计算效率**：简单快速的运算

## 冲突解决策略

### 1. 链地址法（Chaining）
**原理**：每个槽位维护一个链表存储冲突元素

**优点**：
- 实现简单
- 删除操作容易
- 负载因子可以超过1

**缺点**：
- 额外内存开销
- 缓存性能较差
- 链表过长影响性能

**时间复杂度**：
- 平均：O(1)
- 最坏：O(n)

### 2. 开放寻址法（Open Addressing）
**原理**：冲突时在表内寻找下一个空位

#### 线性探测
- **探测序列**：`(hash + i) % table_size`
- **优点**：实现简单，缓存友好
- **缺点**：容易产生聚集

#### 二次探测
- **探测序列**：`(hash + i²) % table_size`
- **优点**：减少聚集现象
- **缺点**：可能无法探测所有位置

#### 双重散列
- **探测序列**：`(hash1 + i * hash2) % table_size`
- **优点**：分布更均匀
- **缺点**：计算开销较大

### 3. 再哈希法
**原理**：使用多个哈希函数，冲突时尝试下一个

### 4. 建立公共溢出区
**原理**：冲突元素存储在单独的溢出区

## 动态调整

### 1. 扩容策略
- **触发条件**：负载因子超过阈值（通常0.75）
- **扩容倍数**：通常扩大2倍
- **重新哈希**：所有元素需要重新计算位置

### 2. 缩容策略
- **触发条件**：负载因子过低（通常0.25）
- **缩容倍数**：通常缩小一半
- **内存释放**：减少内存占用

## 性能分析

### 1. 时间复杂度
- **查找**：平均O(1)，最坏O(n)
- **插入**：平均O(1)，最坏O(n)
- **删除**：平均O(1)，最坏O(n)

### 2. 空间复杂度
- **链地址法**：O(n + m)，n为元素数，m为表大小
- **开放寻址法**：O(m)，m为表大小

### 3. 影响因素
- **哈希函数质量**：分布均匀性
- **负载因子**：过高导致冲突增加
- **冲突解决策略**：不同策略性能差异

## Go语言实现要点

### 1. map底层实现
- 使用开放寻址法的变种
- 桶（bucket）结构存储键值对
- 渐进式扩容避免阻塞

### 2. 并发安全
- 内置map不是并发安全的
- 使用sync.Map或加锁保护
- 读写分离优化性能

## 面试要点

### 1. 基础概念
- **什么是哈希冲突？** 不同键映射到相同位置
- **如何选择哈希函数？** 均匀分布、快速计算
- **负载因子的作用？** 平衡时间和空间效率

### 2. 冲突解决
- **链地址法vs开放寻址法？** 各自优缺点和适用场景
- **如何处理删除操作？** 开放寻址法需要标记删除
- **扩容时机和策略？** 负载因子阈值和重新哈希

### 3. 性能优化
- **如何减少冲突？** 好的哈希函数和合适的表大小
- **缓存友好性？** 开放寻址法局部性更好
- **并发优化？** 分段锁、无锁数据结构

### 4. 实际应用
- **数据库索引**：B+树vs哈希索引
- **缓存系统**：LRU缓存的哈希表实现
- **分布式系统**：一致性哈希算法
