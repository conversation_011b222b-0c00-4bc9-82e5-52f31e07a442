# 分布式系统设计模式

## 架构模式

### 1. 主从模式(Master-Slave)
**特点**：
- 一个主节点负责写操作
- 多个从节点负责读操作
- 主节点故障时需要选举新主节点

**优缺点**：
- ✅ 读写分离，提高性能
- ✅ 数据备份，提高可用性
- ❌ 主节点单点故障
- ❌ 数据一致性问题

**应用场景**：MySQL主从复制、Redis主从、Kafka

### 2. 对等模式(Peer-to-Peer)
**特点**：
- 所有节点地位相等
- 节点间直接通信
- 去中心化架构

**优缺点**：
- ✅ 无单点故障
- ✅ 扩展性好
- ❌ 数据一致性复杂
- ❌ 网络复杂度高

**应用场景**：BitTorrent、区块链、Cassandra

### 3. 分片模式(Sharding)
**特点**：
- 数据按规则分布到不同节点
- 每个节点负责部分数据
- 支持水平扩展

**分片策略**：
- **范围分片**：按数据范围分片
- **哈希分片**：按哈希值分片
- **目录分片**：通过路由表分片

**优缺点**：
- ✅ 水平扩展能力强
- ✅ 负载分散
- ❌ 跨分片查询复杂
- ❌ 数据重平衡困难

**应用场景**：MongoDB分片、Redis Cluster、HBase

### 4. 微服务模式
**特点**：
- 按业务功能拆分服务
- 服务独立部署和扩展
- 通过API通信

**优缺点**：
- ✅ 技术栈多样化
- ✅ 独立部署
- ❌ 分布式复杂性
- ❌ 数据一致性挑战

### 5. 事件驱动模式
**特点**：
- 通过事件进行服务间通信
- 异步处理
- 松耦合

**优缺点**：
- ✅ 解耦性好
- ✅ 扩展性强
- ❌ 调试困难
- ❌ 事件顺序问题

## 一致性模式

### 1. 强一致性
**特点**：所有节点同时看到相同数据
**实现方式**：
- 同步复制
- 分布式锁
- 两阶段提交

**优缺点**：
- ✅ 数据一致性强
- ❌ 性能较低
- ❌ 可用性受影响

### 2. 弱一致性
**特点**：不保证所有节点数据一致
**实现方式**：
- 异步复制
- 最终一致性
- 向量时钟

**优缺点**：
- ✅ 性能高
- ✅ 可用性好
- ❌ 数据可能不一致

### 3. 最终一致性
**特点**：系统最终会达到一致状态
**实现方式**：
- 异步复制
- 冲突解决机制
- 版本向量

## 容错模式

### 1. 故障检测
**检测方式**：
- **心跳检测**：定期发送心跳消息
- **超时检测**：响应超时判断故障
- **第三方检测**：通过监控系统检测

### 2. 故障恢复
**恢复策略**：
- **主备切换**：主节点故障时切换到备节点
- **重启恢复**：自动重启故障节点
- **数据修复**：修复不一致的数据

### 3. 降级策略
**降级方式**：
- **功能降级**：关闭非核心功能
- **服务降级**：使用缓存或默认值
- **容量降级**：限制请求量

## 负载均衡模式

### 1. 静态负载均衡
- **轮询**：依次分配请求
- **随机**：随机选择服务器
- **加权轮询**：根据权重分配

### 2. 动态负载均衡
- **最少连接**：选择连接数最少的服务器
- **响应时间**：选择响应时间最短的服务器
- **CPU使用率**：根据CPU负载选择

### 3. 一致性哈希
**特点**：
- 节点变化时影响最小
- 适合分布式缓存
- 支持虚拟节点

## 常见面试问题

### 1. 架构模式
- **Q: 如何选择合适的分布式架构模式？**
- A: 根据业务需求、数据特点、一致性要求、扩展性需求选择

- **Q: 主从模式和对等模式的区别？**
- A: 主从有中心节点，对等模式去中心化；主从实现简单但有单点故障

- **Q: 微服务架构的优缺点？**
- A: 优点是独立部署、技术多样化；缺点是分布式复杂性、数据一致性

### 2. 一致性模式
- **Q: 强一致性和最终一致性的选择？**
- A: 金融等场景选强一致性，社交等场景可选最终一致性

- **Q: 如何解决分布式系统的数据冲突？**
- A: 使用向量时钟、时间戳、业务规则等方式解决冲突

### 3. 容错设计
- **Q: 如何设计高可用的分布式系统？**
- A: 故障检测、自动恢复、降级策略、多副本、负载均衡

- **Q: 分布式系统如何处理脑裂问题？**
- A: 使用多数派机制、仲裁节点、共享存储等方式

### 4. 负载均衡
- **Q: 一致性哈希的优势是什么？**
- A: 节点变化时数据迁移量最小，适合分布式缓存

- **Q: 如何处理热点数据问题？**
- A: 数据分片、缓存、负载均衡、限流等方式

## 设计原则

### 1. 可扩展性
- 水平扩展优于垂直扩展
- 无状态设计
- 数据分片

### 2. 可用性
- 消除单点故障
- 故障隔离
- 快速恢复

### 3. 一致性
- 根据业务需求选择一致性级别
- 设计冲突解决机制
- 监控数据一致性

### 4. 性能
- 减少网络通信
- 异步处理
- 缓存策略
