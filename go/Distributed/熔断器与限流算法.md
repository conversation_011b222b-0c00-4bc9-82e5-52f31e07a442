# 熔断器与限流算法

## 熔断器（Circuit Breaker）

### 核心概念
熔断器是一种保护分布式系统的机制，当检测到故障时自动切断对故障服务的调用，防止故障扩散。

### 三种状态
- **关闭状态（Closed）**：正常调用，统计失败率
- **开启状态（Open）**：拒绝所有调用，直接返回错误
- **半开状态（Half-Open）**：允许少量请求测试服务恢复

### 状态转换
- **关闭→开启**：失败率超过阈值
- **开启→半开**：超时时间到达
- **半开→关闭**：测试请求成功
- **半开→开启**：测试请求失败

### 关键参数
- **失败阈值**：触发熔断的失败率
- **请求量阈值**：最小请求数量
- **超时时间**：熔断器开启持续时间
- **半开请求数**：半开状态允许的请求数

## 限流算法

### 1. 固定窗口算法
**原理**：在固定时间窗口内限制请求数量

**优缺点**：
- ✅ 实现简单，内存占用少
- ❌ 边界突刺问题，不够平滑

### 2. 滑动窗口算法
**原理**：将时间窗口分成多个小窗口，滑动统计

**优缺点**：
- ✅ 更平滑，避免边界问题
- ❌ 内存占用较大，实现复杂

### 3. 令牌桶算法
**原理**：以固定速率向桶中放入令牌，请求需要获取令牌

**特点**：
- 允许一定程度的突发流量
- 平均速率控制
- 桶容量决定最大突发量

### 4. 漏桶算法
**原理**：请求进入漏桶，以固定速率流出

**特点**：
- 强制限制输出速率
- 平滑处理突发流量
- 超出容量的请求被丢弃

## 算法对比

| 算法 | 突发处理 | 平滑性 | 实现复杂度 | 内存占用 |
|------|----------|--------|------------|----------|
| 固定窗口 | 差 | 差 | 简单 | 低 |
| 滑动窗口 | 中 | 好 | 中等 | 中 |
| 令牌桶 | 好 | 好 | 中等 | 低 |
| 漏桶 | 差 | 很好 | 简单 | 低 |

## 实际应用

### 熔断器应用场景
- **微服务调用**：防止级联故障
- **数据库访问**：保护数据库过载
- **外部API调用**：处理第三方服务不稳定
- **缓存穿透**：保护后端存储

### 限流应用场景
- **API网关**：保护后端服务
- **用户接口**：防止恶意刷量
- **资源保护**：数据库连接池限制
- **系统保护**：防止系统过载

## 常见面试问题

### 1. 熔断器相关
- **Q: 熔断器的作用是什么？**
- A: 防止故障扩散，保护系统稳定性，提供快速失败机制

- **Q: 熔断器的三种状态及转换条件？**
- A: 关闭、开启、半开三种状态，根据失败率和超时时间转换

- **Q: 如何设置熔断器参数？**
- A: 根据业务特点设置失败阈值、超时时间、请求量阈值

### 2. 限流算法相关
- **Q: 令牌桶和漏桶的区别？**
- A: 令牌桶允许突发流量，漏桶强制平滑输出

- **Q: 如何选择限流算法？**
- A: 根据业务需求：允许突发选令牌桶，要求平滑选漏桶

- **Q: 分布式环境下如何限流？**
- A: 使用Redis等共享存储，或者分片限流

### 3. 实际应用
- **Q: 微服务中如何实现熔断？**
- A: 使用Hystrix、Sentinel等框架，或自实现熔断逻辑

- **Q: 限流被触发后如何处理？**
- A: 返回错误码、降级处理、排队等待

### 4. 设计考虑
- **Q: 熔断器如何避免误判？**
- A: 设置合理的统计窗口、最小请求量、多维度判断

- **Q: 限流算法的性能考虑？**
- A: 选择合适算法、减少锁竞争、使用本地缓存

## 最佳实践

### 熔断器设计
- **多级熔断**：方法级、服务级、系统级
- **监控告警**：实时监控熔断状态
- **降级策略**：提供备用方案
- **参数调优**：根据实际情况调整参数

### 限流设计
- **分层限流**：网关、服务、资源多层保护
- **动态调整**：根据系统负载动态调整限流参数
- **用户区分**：VIP用户和普通用户不同限制
- **优雅降级**：超限时提供降级服务

### 监控指标
- **熔断器指标**：熔断次数、成功率、响应时间
- **限流指标**：请求量、拒绝率、通过率
- **系统指标**：CPU、内存、网络使用率
- **业务指标**：用户体验、业务成功率
