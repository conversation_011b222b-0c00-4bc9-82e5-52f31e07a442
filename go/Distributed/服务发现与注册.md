# 服务发现与注册

## 核心概念

服务发现是微服务架构中的关键组件，用于管理服务实例的注册、发现和健康检查，使服务能够动态地找到和调用其他服务。

## 基本原理

### 服务注册
- **服务启动**：服务实例启动时向注册中心注册
- **信息上报**：提供服务名、地址、端口、元数据等信息
- **心跳保持**：定期发送心跳维持注册状态
- **服务下线**：服务停止时主动注销

### 服务发现
- **查询服务**：客户端向注册中心查询服务列表
- **负载均衡**：从多个实例中选择一个进行调用
- **缓存更新**：本地缓存服务列表，定期更新
- **故障处理**：检测到故障实例时自动剔除

## 实现模式

### 1. 客户端发现模式
**特点**：
- 客户端直接查询注册中心
- 客户端负责负载均衡
- 注册中心只负责存储服务信息

**优点**：
- 简单直接，性能好
- 客户端可以实现智能负载均衡

**缺点**：
- 客户端逻辑复杂
- 与注册中心耦合

### 2. 服务端发现模式
**特点**：
- 客户端通过负载均衡器访问服务
- 负载均衡器查询注册中心
- 客户端无需感知注册中心

**优点**：
- 客户端简单
- 与注册中心解耦

**缺点**：
- 负载均衡器成为瓶颈
- 增加网络跳转

### 3. 服务网格模式
**特点**：
- 通过Sidecar代理处理服务通信
- 代理负责服务发现和负载均衡
- 应用无需关心服务发现逻辑

**优点**：
- 应用无侵入
- 功能丰富（监控、安全等）

**缺点**：
- 架构复杂
- 性能开销

## 主流方案

### 1. Consul
**特点**：
- HashiCorp开发
- 支持多数据中心
- 内置健康检查
- 提供KV存储

**核心功能**：
- 服务注册与发现
- 健康检查
- 配置管理
- 连接授权

### 2. Eureka
**特点**：
- Netflix开发
- Spring Cloud集成
- AP模型（可用性优先）
- 客户端缓存

**架构组件**：
- Eureka Server：注册中心
- Eureka Client：服务实例

### 3. etcd
**特点**：
- CoreOS开发
- 强一致性
- 支持Watch机制
- Kubernetes使用

**应用场景**：
- 配置管理
- 服务发现
- 分布式锁
- 选主

### 4. Nacos
**特点**：
- 阿里巴巴开发
- 配置管理+服务发现
- 支持多种注册模式
- 管理界面友好

**核心功能**：
- 动态配置服务
- 服务发现和管理
- 动态DNS服务

### 5. ZooKeeper
**特点**：
- Apache项目
- 强一致性
- 临时节点机制
- 成熟稳定

**服务发现实现**：
- 临时顺序节点
- Watch机制
- 路径结构

## 健康检查

### 检查方式
- **HTTP检查**：定期访问健康检查端点
- **TCP检查**：检查端口连通性
- **脚本检查**：执行自定义检查脚本
- **TTL检查**：服务主动上报健康状态

### 检查策略
- **检查间隔**：平衡及时性和性能
- **超时时间**：避免误判
- **失败阈值**：连续失败次数
- **恢复检查**：故障恢复后的检查

### 健康状态
- **Passing**：健康状态
- **Warning**：警告状态
- **Critical**：严重故障
- **Unknown**：未知状态

## 负载均衡集成

### 常用算法
- **轮询**：依次分配请求
- **随机**：随机选择实例
- **加权轮询**：根据权重分配
- **最少连接**：选择连接数最少的实例

### 实现方式
- **客户端负载均衡**：Ribbon、Spring Cloud LoadBalancer
- **服务端负载均衡**：Nginx、HAProxy
- **服务网格**：Istio、Linkerd

## 配置管理

### 配置类型
- **应用配置**：业务相关配置
- **环境配置**：不同环境的配置
- **基础设施配置**：数据库、缓存等配置

### 配置特性
- **动态更新**：配置变更实时生效
- **版本管理**：配置变更历史
- **环境隔离**：不同环境配置分离
- **权限控制**：配置访问权限

## 常见问题及解决方案

### 1. 注册中心故障
**问题**：注册中心不可用影响服务发现

**解决方案**：
- 客户端缓存服务列表
- 多注册中心部署
- 降级到静态配置

### 2. 网络分区
**问题**：网络分区导致服务误判

**解决方案**：
- 合理设置健康检查参数
- 多维度健康检查
- 人工干预机制

### 3. 服务雪崩
**问题**：大量服务同时下线

**解决方案**：
- 分批重启服务
- 熔断器保护
- 限流机制

### 4. 配置变更风险
**问题**：配置错误导致服务故障

**解决方案**：
- 配置校验机制
- 灰度发布
- 快速回滚

## 常见面试问题

### 1. 基础概念
- **Q: 服务发现的作用是什么？**
- A: 动态管理服务实例，实现服务间的自动发现和调用

- **Q: 服务注册的流程是什么？**
- A: 服务启动→注册信息→心跳保持→服务下线

### 2. 架构设计
- **Q: 客户端发现和服务端发现的区别？**
- A: 客户端发现由客户端查询注册中心，服务端发现通过负载均衡器

- **Q: 如何选择服务发现方案？**
- A: 根据一致性要求、性能需求、生态集成等因素选择

### 3. 实际应用
- **Q: 如何处理服务发现的故障？**
- A: 客户端缓存、多注册中心、降级机制

- **Q: 健康检查如何设计？**
- A: 选择合适的检查方式，设置合理的参数

### 4. 性能优化
- **Q: 如何提高服务发现性能？**
- A: 客户端缓存、批量查询、异步更新

- **Q: 大规模服务如何优化？**
- A: 分区部署、层次化架构、缓存优化

## 最佳实践

### 设计原则
- **高可用**：多实例部署，避免单点故障
- **一致性**：选择合适的一致性模型
- **性能**：客户端缓存，减少网络开销
- **安全**：访问控制，数据加密

### 实施建议
- **渐进式迁移**：逐步从静态配置迁移到动态发现
- **监控告警**：实时监控注册中心状态
- **容灾预案**：制定故障应急方案
- **文档规范**：服务注册和发现的规范文档

### 运维要点
- **容量规划**：根据服务数量规划注册中心容量
- **性能监控**：监控注册、发现、健康检查性能
- **数据备份**：定期备份注册中心数据
- **版本管理**：注册中心的版本升级策略
