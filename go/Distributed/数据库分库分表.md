# 数据库分库分表

## 核心概念

分库分表是解决数据库性能瓶颈和存储容量限制的重要技术，通过将数据分散到多个数据库和表中来提高系统的并发能力和存储容量。

## 基本概念

### 1. 分库(Sharding)
**定义**：将数据分散到多个数据库实例中
**目的**：
- 分散数据库连接压力
- 提高并发处理能力
- 突破单机存储限制
- 提高系统可用性

### 2. 分表(Table Partitioning)
**定义**：将单个大表拆分成多个小表
**类型**：
- **水平分表**：按行拆分，表结构相同
- **垂直分表**：按列拆分，表结构不同

### 3. 分库分表
**定义**：同时进行分库和分表
**优势**：最大化性能提升和扩展能力

## 分片策略

### 1. 水平分片
**按范围分片**：
- 根据数据范围分片（如按时间、ID范围）
- 优点：查询简单，扩展容易
- 缺点：数据分布可能不均匀

**按哈希分片**：
- 根据分片键的哈希值分片
- 优点：数据分布均匀
- 缺点：范围查询困难，扩容复杂

**按目录分片**：
- 通过查找表确定数据位置
- 优点：灵活性高，支持复杂路由
- 缺点：增加查找开销，目录成为瓶颈

### 2. 垂直分片
**按业务分片**：
- 根据业务模块分离数据库
- 优点：业务隔离，便于维护
- 缺点：跨库查询复杂

**按访问频率分片**：
- 热数据和冷数据分离
- 优点：提高热数据访问性能
- 缺点：数据迁移复杂

## 分片键选择

### 选择原则
- **数据分布均匀**：避免数据倾斜
- **查询友好**：减少跨分片查询
- **扩展性好**：支持动态扩容
- **业务相关**：符合业务访问模式

### 常见分片键
- **用户ID**：适合用户相关数据
- **时间**：适合时序数据
- **地理位置**：适合地理相关数据
- **业务ID**：适合业务相关数据

## 技术挑战

### 1. 跨分片查询
**问题**：需要查询多个分片的数据
**解决方案**：
- **分片路由**：根据查询条件确定分片
- **结果合并**：合并多个分片的查询结果
- **分页处理**：跨分片分页查询
- **聚合计算**：跨分片聚合操作

### 2. 分布式事务
**问题**：跨分片的事务一致性
**解决方案**：
- **两阶段提交**：保证强一致性
- **最终一致性**：通过补偿机制
- **避免跨分片事务**：业务设计避免
- **分布式事务框架**：使用Seata等框架

### 3. 数据迁移
**问题**：分片扩容时的数据迁移
**解决方案**：
- **在线迁移**：不停机迁移数据
- **双写方案**：新旧分片同时写入
- **一致性哈希**：减少迁移数据量
- **分批迁移**：分批次迁移数据

### 4. 全局唯一ID
**问题**：分布式环境下的ID生成
**解决方案**：
- **Snowflake算法**：基于时间戳的ID生成
- **数据库序列**：使用数据库自增序列
- **UUID**：全局唯一但无序
- **号段模式**：批量获取ID段

## 中间件方案

### 1. ShardingSphere
**特点**：
- Apache开源项目
- 支持多种数据库
- 提供分片、读写分离、分布式事务
- 支持JDBC和Proxy模式

**优势**：
- 功能完善，生态丰富
- 社区活跃，文档完善
- 支持多种部署模式
- 与Spring集成良好

### 2. MyCat
**特点**：
- 国产开源中间件
- 基于Proxy架构
- 支持多种数据库
- 提供分片和读写分离

**优势**：
- 对应用透明
- 支持SQL路由
- 配置相对简单
- 中文文档丰富

### 3. Vitess
**特点**：
- YouTube开源
- 专为MySQL设计
- 云原生架构
- 支持水平扩展

**优势**：
- 性能优秀
- 运维工具完善
- 支持在线Schema变更
- 适合大规模部署

## 读写分离

### 基本原理
- **主库**：处理写操作和实时读操作
- **从库**：处理读操作
- **数据同步**：主库数据异步同步到从库

### 实现方式
- **应用层**：应用代码控制读写路由
- **中间件**：通过代理中间件实现
- **数据库**：数据库自身支持

### 注意事项
- **主从延迟**：从库数据可能滞后
- **读写一致性**：写后立即读的一致性问题
- **故障切换**：主库故障时的切换机制

## 常见面试问题

### 1. 基础概念
- **Q: 什么是分库分表？为什么需要？**
- A: 将数据分散到多个数据库和表中，解决单库单表的性能和容量瓶颈

- **Q: 水平分片和垂直分片的区别？**
- A: 水平分片按行拆分，垂直分片按列拆分；水平分片解决数据量问题，垂直分片解决业务隔离

- **Q: 如何选择分片键？**
- A: 考虑数据分布均匀性、查询友好性、扩展性和业务相关性

### 2. 技术挑战
- **Q: 跨分片查询如何处理？**
- A: 分片路由、结果合并、分页处理、聚合计算

- **Q: 分布式事务如何保证？**
- A: 两阶段提交、最终一致性、避免跨分片事务、使用分布式事务框架

- **Q: 如何生成全局唯一ID？**
- A: Snowflake算法、数据库序列、UUID、号段模式

### 3. 数据迁移
- **Q: 分片扩容如何进行数据迁移？**
- A: 在线迁移、双写方案、一致性哈希、分批迁移

- **Q: 如何保证迁移过程中的数据一致性？**
- A: 双写验证、数据校验、回滚机制、监控告警

### 4. 读写分离
- **Q: 读写分离的主从延迟如何处理？**
- A: 强制主库读、延迟检测、业务容忍、缓存补偿

- **Q: 主库故障如何处理？**
- A: 自动故障转移、手动切换、数据恢复、业务降级

### 5. 方案选择
- **Q: 如何选择分库分表中间件？**
- A: 根据功能需求、性能要求、运维复杂度、团队技术栈选择

- **Q: 什么时候考虑分库分表？**
- A: 单表数据量过大、并发压力大、存储容量不足、性能瓶颈明显

## 最佳实践

### 设计原则
- **业务优先**：根据业务特点设计分片策略
- **渐进式**：从简单到复杂，逐步演进
- **可扩展**：考虑未来的扩展需求
- **可维护**：保持系统的可维护性

### 实施建议
- **充分测试**：在生产环境前充分测试
- **监控完善**：建立完善的监控体系
- **文档齐全**：维护详细的设计文档
- **团队培训**：确保团队掌握相关技术

### 避免陷阱
- **过度设计**：避免过早优化
- **数据倾斜**：注意数据分布均匀性
- **跨分片依赖**：减少跨分片的业务依赖
- **运维复杂度**：控制系统复杂度
