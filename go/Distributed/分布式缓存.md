# 分布式缓存

## 核心概念

分布式缓存是将缓存数据分布在多个节点上的缓存系统，提供高性能、高可用的数据访问服务。

## 主要特点

### 优势
- **高性能**：内存访问，毫秒级响应
- **高可用**：多节点部署，故障容错
- **可扩展**：水平扩展，支持大容量
- **减轻负载**：减少数据库访问压力

### 挑战
- **数据一致性**：缓存与数据库同步
- **缓存穿透**：大量请求绕过缓存
- **缓存雪崩**：缓存同时失效
- **热点数据**：某些数据访问频繁

## 主流方案

### 1. Redis Cluster
**特点**：
- 官方集群方案
- 数据自动分片
- 主从复制
- 故障自动转移

**架构**：
- 16384个哈希槽
- 每个节点负责一部分槽
- 客户端直连数据节点

### 2. Redis Sentinel
**特点**：
- 高可用方案
- 主从切换
- 监控和通知
- 配置管理

**组件**：
- Master节点：主节点
- Slave节点：从节点
- Sentinel节点：监控节点

### 3. Codis
**特点**：
- 豌豆荚开源
- 代理架构
- 数据迁移
- 管理界面

**架构**：
- Codis Proxy：代理层
- Codis Server：存储层
- ZooKeeper：协调服务

### 4. Twemproxy
**特点**：
- Twitter开源
- 轻量级代理
- 多种哈希算法
- 连接池

## 数据分片策略

### 1. 哈希分片
**原理**：根据key的哈希值分配到不同节点

**优点**：分布均匀，实现简单
**缺点**：扩容困难，数据迁移复杂

### 2. 一致性哈希
**原理**：将节点和数据映射到哈希环上

**优点**：扩容影响小，数据迁移少
**缺点**：可能数据倾斜，实现复杂

### 3. 范围分片
**原理**：按key的范围分配到不同节点

**优点**：范围查询友好
**缺点**：可能热点问题，负载不均

### 4. 目录分片
**原理**：维护key到节点的映射表

**优点**：灵活性高，支持复杂路由
**缺点**：映射表成为瓶颈

## 常见问题及解决方案

### 1. 缓存穿透
**问题**：查询不存在的数据，绕过缓存直接访问数据库

**解决方案**：
- **布隆过滤器**：快速判断数据是否存在
- **空值缓存**：缓存空结果，设置较短过期时间
- **参数校验**：在接口层校验参数合法性

### 2. 缓存击穿
**问题**：热点数据过期，大量请求同时访问数据库

**解决方案**：
- **互斥锁**：只允许一个线程重建缓存
- **永不过期**：热点数据设置永不过期
- **提前刷新**：在过期前异步刷新缓存

### 3. 缓存雪崩
**问题**：大量缓存同时失效，数据库压力激增

**解决方案**：
- **过期时间随机化**：避免同时过期
- **多级缓存**：本地缓存+分布式缓存
- **限流降级**：数据库访问限流
- **预热缓存**：系统启动时预加载热点数据

### 4. 数据一致性
**问题**：缓存与数据库数据不一致

**解决方案**：
- **Cache Aside**：应用程序管理缓存
- **Write Through**：写入时同时更新缓存
- **Write Behind**：异步写入数据库
- **双写一致性**：先更新数据库再删除缓存

## 缓存模式

### 1. Cache Aside（旁路缓存）
**读流程**：
1. 查询缓存
2. 缓存命中返回数据
3. 缓存未命中查询数据库
4. 将数据写入缓存

**写流程**：
1. 更新数据库
2. 删除缓存

### 2. Read Through（读穿透）
**特点**：缓存层负责数据加载
**流程**：应用只与缓存交互，缓存负责从数据库加载数据

### 3. Write Through（写穿透）
**特点**：写入时同时更新缓存和数据库
**流程**：数据同时写入缓存和数据库

### 4. Write Behind（写回）
**特点**：异步写入数据库
**流程**：先写缓存，异步批量写入数据库

## 性能优化

### 1. 数据结构选择
- **String**：简单键值对
- **Hash**：对象存储，节省内存
- **List**：队列、栈操作
- **Set**：去重、交并差运算
- **ZSet**：排行榜、范围查询

### 2. 内存优化
- **数据压缩**：使用压缩算法
- **过期策略**：合理设置TTL
- **内存回收**：定期清理无用数据
- **数据结构优化**：选择合适的数据结构

### 3. 网络优化
- **连接池**：复用连接，减少开销
- **批量操作**：Pipeline、事务
- **数据序列化**：选择高效序列化方式
- **压缩传输**：网络数据压缩

## 常见面试问题

### 1. 基础概念
- **Q: 分布式缓存的作用？**
- A: 提高性能、减轻数据库压力、提升用户体验

- **Q: Redis和Memcached的区别？**
- A: Redis支持多种数据结构、持久化、集群；Memcached简单、性能高

### 2. 架构设计
- **Q: 如何设计分布式缓存架构？**
- A: 考虑数据分片、高可用、一致性、监控等方面

- **Q: 缓存数据如何分片？**
- A: 哈希分片、一致性哈希、范围分片等方式

### 3. 问题解决
- **Q: 如何解决缓存穿透？**
- A: 布隆过滤器、空值缓存、参数校验

- **Q: 缓存雪崩如何预防？**
- A: 过期时间随机化、多级缓存、限流降级

### 4. 一致性保证
- **Q: 如何保证缓存一致性？**
- A: 选择合适的缓存模式，如Cache Aside、Write Through等

- **Q: 双写一致性问题如何解决？**
- A: 先更新数据库再删除缓存，使用分布式锁

## 最佳实践

### 设计原则
- **合理的过期时间**：根据业务特点设置TTL
- **热点数据预热**：系统启动时预加载
- **监控告警**：实时监控缓存命中率、性能指标
- **降级策略**：缓存故障时的降级方案

### 运维建议
- **容量规划**：根据业务增长预估容量
- **性能监控**：监控QPS、延迟、命中率
- **故障处理**：制定故障应急预案
- **数据备份**：重要数据定期备份

### 开发规范
- **Key命名规范**：统一的命名规则
- **数据序列化**：选择合适的序列化方式
- **异常处理**：缓存异常时的处理逻辑
- **代码复用**：封装通用的缓存操作
