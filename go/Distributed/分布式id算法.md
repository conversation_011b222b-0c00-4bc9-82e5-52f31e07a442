# 分布式ID生成算法

## 核心需求
- **全局唯一性**：分布式环境下ID不重复
- **高性能**：支持高并发生成
- **有序性**：趋势递增，便于数据库索引
- **高可用**：避免单点故障

## 主流方案对比

### 1. UUID
- **原理**：基于时间戳、MAC地址、随机数生成
- **优点**：本地生成，无依赖，全局唯一
- **缺点**：无序，长度大(36字符)，不适合主键
- **适用场景**：对有序性无要求的场景

### 2. Snowflake算法
**结构组成**：
```
| 1位符号 | 41位时间戳 | 10位机器ID | 12位序列号 |
```

**核心特点**：
- **时间戳**：毫秒级时间戳，可用69年
- **机器ID**：支持1024台机器
- **序列号**：每毫秒可生成4096个ID
- **有序性**：趋势递增，便于数据库索引

**关键问题**：
- **时钟回拨**：检测并拒绝生成，或使用偏移量
- **机器ID分配**：通过配置中心或数据库分配
- **并发安全**：使用锁或无锁算法保证线程安全

### 3. 数据库自增
**实现方式**：
- **单机自增**：使用AUTO_INCREMENT
- **多机自增**：设置不同起始值和步长

**优缺点**：
- ✅ 简单可靠，强一致性
- ❌ 性能瓶颈，单点故障

### 4. 号段模式
**工作原理**：
- 从数据库批量获取ID段（如1-1000）
- 应用内存中分配ID，用完再获取下一段
- 减少数据库访问，提高性能

**优缺点**：
- ✅ 性能好，减少数据库压力
- ❌ 服务重启可能浪费ID

### 5. Redis方案
**实现方式**：
- **INCR命令**：原子性递增
- **带过期时间**：按日期生成ID，自动清理

**优缺点**：
- ✅ 性能高，实现简单
- ❌ 依赖Redis，可能丢失数据

## 方案选择指南

| 方案 | 性能 | 复杂度 | 有序性 | 依赖 | 适用场景 |
|------|------|--------|--------|------|----------|
| UUID | 高 | 低 | 无 | 无 | 对有序性无要求 |
| Snowflake | 高 | 中 | 有 | 时钟 | 高并发，需要有序 |
| 数据库自增 | 低 | 低 | 有 | 数据库 | 小规模系统 |
| 号段模式 | 高 | 中 | 有 | 数据库 | 中等规模系统 |
| Redis | 高 | 低 | 有 | Redis | 简单快速实现 |

## 常见面试问题

### 1. 基础概念
- **Q: 分布式ID需要满足什么要求？**
- A: 全局唯一、高性能、有序性（可选）、高可用

- **Q: UUID为什么不适合做数据库主键？**
- A: 无序性导致B+树频繁分裂，影响插入性能

### 2. Snowflake算法
- **Q: Snowflake算法的组成部分？**
- A: 1位符号位 + 41位时间戳 + 10位机器ID + 12位序列号

- **Q: 如何解决时钟回拨问题？**
- A: 检测回拨并拒绝生成、使用偏移量、预分配时间段

- **Q: 机器ID如何分配？**
- A: 配置中心分配、数据库分配、MAC地址hash

### 3. 方案选择
- **Q: 如何选择合适的分布式ID方案？**
- A: 根据性能要求、有序性需求、依赖复杂度选择

- **Q: 高并发场景下推荐哪种方案？**
- A: Snowflake或号段模式，性能高且有序

### 4. 实际应用
- **Q: 分布式ID在微服务中的应用？**
- A: 订单号生成、用户ID、消息ID等需要全局唯一的场景

- **Q: 如何保证分布式ID生成的高可用？**
- A: 多机部署、故障转移、备用方案