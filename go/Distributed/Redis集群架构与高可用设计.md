# Redis集群架构与高可用设计

## 核心概念

Redis集群是构建高可用、高性能缓存系统的关键技术，通过数据分片和主从复制实现高可用性。

## Redis集群架构

### 1. Redis Cluster（官方集群）

**核心特性**：
- **数据分片**：16384个哈希槽，自动分布数据
- **高可用性**：主从复制 + 自动故障转移
- **水平扩展**：动态添加/删除节点
- **无中心化**：去中心化架构，无单点故障

**分片机制**：
- **哈希槽**：CRC16(key) % 16384
- **槽位分配**：每个主节点负责一部分槽位
- **数据路由**：客户端直接访问对应节点

### 2. Redis Sentinel（哨兵模式）

**核心特性**：
- **监控**：监控主从节点健康状态
- **通知**：故障时通知管理员或应用
- **自动故障转移**：主节点故障时自动选举新主节点
- **配置提供**：为客户端提供当前主节点信息

**工作原理**：
- 多个Sentinel监控同一组Redis实例
- 通过投票机制判断主节点是否下线
- 自动从从节点中选举新的主节点

### 3. Redis主从复制

**复制机制**：
- **全量复制**：从节点首次连接时进行
- **增量复制**：主从断线重连后的数据同步
- **异步复制**：主节点写入后异步同步到从节点

**复制流程**：
1. 从节点发送SYNC命令
2. 主节点执行BGSAVE生成RDB
3. 主节点发送RDB文件给从节点
4. 从节点加载RDB文件
5. 主节点发送缓冲区命令

## 高可用设计

### 故障检测机制
- **心跳检测**：节点间定期发送PING消息
- **主观下线**：单个节点认为目标节点不可达
- **客观下线**：多数节点认为目标节点不可达
- **故障转移**：自动选举新的主节点

### 数据一致性保证
- **最终一致性**：异步复制保证最终一致
- **读写分离**：主节点写，从节点读
- **数据持久化**：RDB + AOF双重保障

### 脑裂问题解决
- **最小主节点数**：min-slaves-to-write配置
- **最大延迟时间**：min-slaves-max-lag配置
- **Sentinel投票**：多数派机制防止脑裂

## 性能优化策略

### 客户端优化
- **连接池**：复用连接，减少连接开销
- **Pipeline**：批量发送命令，减少网络往返
- **智能路由**：客户端缓存槽位信息，直接路由
- **读写分离**：读操作路由到从节点

### 服务端优化
- **内存优化**：合理配置内存淘汰策略
- **持久化优化**：RDB和AOF参数调优
- **网络优化**：TCP_NODELAY、缓冲区大小
- **CPU优化**：多线程IO、合理的工作线程数

### 集群优化
- **槽位均衡**：确保数据均匀分布
- **节点规划**：合理的主从比例和分布
- **监控告警**：实时监控集群状态
- **容量规划**：预估数据增长和性能需求

## 常见面试问题

### 1. 基础概念
- **Q: Redis集群有哪些部署方式？**
- A: 主从复制、Sentinel哨兵模式、Redis Cluster集群模式

- **Q: Redis Cluster如何实现数据分片？**
- A: 使用16384个哈希槽，通过CRC16(key) % 16384计算槽位

- **Q: Redis主从复制的原理？**
- A: 全量复制(RDB) + 增量复制(命令传播)，异步复制机制

### 2. 高可用机制
- **Q: Redis Sentinel的作用是什么？**
- A: 监控、通知、自动故障转移、配置提供

- **Q: Redis如何实现故障转移？**
- A: 心跳检测 + 主观下线 + 客观下线 + 自动选举新主节点

- **Q: 如何解决Redis脑裂问题？**
- A: 配置min-slaves-to-write和min-slaves-max-lag参数

### 3. 性能优化
- **Q: Redis集群的性能优化策略？**
- A: 连接池、Pipeline、读写分离、槽位均衡、内存优化

- **Q: Redis Cluster的数据迁移如何进行？**
- A: 通过MIGRATE命令逐个迁移槽位中的key，支持在线迁移

- **Q: 如何监控Redis集群状态？**
- A: 监控节点状态、内存使用、连接数、命令执行时间等指标

### 4. 实际应用
- **Q: 什么场景下选择Redis Cluster？**
- A: 数据量大、需要水平扩展、高可用要求的场景

- **Q: Redis集群的限制有哪些？**
- A: 不支持多key操作、事务限制、Lua脚本限制

- **Q: 如何设计Redis集群的容量规划？**
- A: 评估数据量、QPS、内存需求，合理规划节点数量和配置

### 5. 故障处理
- **Q: Redis节点宕机如何处理？**
- A: 主节点宕机自动故障转移，从节点宕机影响读性能但不影响写

- **Q: 如何处理Redis集群的网络分区？**
- A: 多数派机制，少数派停止服务，避免数据不一致

- **Q: Redis数据丢失的原因和预防？**
- A: 异步复制、持久化配置、网络分区等，通过合理配置和监控预防
    }
    
    return status
}

func (cm *ClusterMonitor) evaluateNodeHealth(status NodeStatus) {
    // 检查延迟
    if status.Latency > time.Millisecond*100 {
        cm.sendAlert("HIGH_LATENCY", status.Address, 
            fmt.Sprintf("Node latency: %v", status.Latency), "MEDIUM")
    }
    
    // 检查内存使用
    if status.Memory > 1024*1024*1024*8 { // 8GB
        cm.sendAlert("HIGH_MEMORY", status.Address,
            fmt.Sprintf("Memory usage: %d bytes", status.Memory), "MEDIUM")
    }
    
    // 检查连接数
    if status.Connections > 10000 {
        cm.sendAlert("HIGH_CONNECTIONS", status.Address,
            fmt.Sprintf("Connection count: %d", status.Connections), "LOW")
    }
    
    // 检查节点状态
    if status.Status == "DOWN" {
        cm.sendAlert("NODE_DOWN", status.Address, "Node is unreachable", "HIGH")
    }
}

func (cm *ClusterMonitor) sendAlert(alertType, node, message, severity string) {
    alert := Alert{
        Type:      alertType,
        Node:      node,
        Message:   message,
        Timestamp: time.Now(),
        Severity:  severity,
    }
    
    select {
    case cm.alertChannel <- alert:
    default:
        // 告警通道满了，丢弃告警
    }
}
```

### 5. **集群扩容和缩容**

#### **动态扩容实现**
```go
type ClusterScaler struct {
    client  *redis.ClusterClient
    monitor *ClusterMonitor
}

func NewClusterScaler(client *redis.ClusterClient, monitor *ClusterMonitor) *ClusterScaler {
    return &ClusterScaler{
        client:  client,
        monitor: monitor,
    }
}

func (cs *ClusterScaler) ScaleOut(newNodes []string) error {
    for _, node := range newNodes {
        if err := cs.addNode(node); err != nil {
            return fmt.Errorf("failed to add node %s: %w", node, err)
        }
    }
    
    // 重新分配槽位
    return cs.rebalanceSlots()
}

func (cs *ClusterScaler) addNode(nodeAddr string) error {
    ctx := context.Background()
    
    // 连接到新节点
    newNodeClient := redis.NewClient(&redis.Options{
        Addr: nodeAddr,
    })
    defer newNodeClient.Close()
    
    // 重置节点状态
    if err := newNodeClient.ClusterReset(ctx, "hard").Err(); err != nil {
        return err
    }
    
    // 获取现有集群中的一个节点
    existingNode, err := cs.getExistingNode()
    if err != nil {
        return err
    }
    
    // 将新节点加入集群
    return existingNode.ClusterMeet(ctx, parseHost(nodeAddr), parsePort(nodeAddr)).Err()
}

func (cs *ClusterScaler) rebalanceSlots() error {
    ctx := context.Background()
    
    // 获取当前槽位分配
    slots, err := cs.getCurrentSlotAllocation()
    if err != nil {
        return err
    }
    
    // 计算新的槽位分配
    newAllocation := cs.calculateNewAllocation(slots)
    
    // 执行槽位迁移
    return cs.migrateSlots(ctx, newAllocation)
}

func (cs *ClusterScaler) calculateNewAllocation(currentSlots map[string][]int) map[string][]int {
    // 获取所有主节点
    masters := cs.getMasterNodes()
    totalSlots := 16384
    slotsPerNode := totalSlots / len(masters)
    
    newAllocation := make(map[string][]int)
    slotIndex := 0
    
    for i, master := range masters {
        startSlot := i * slotsPerNode
        endSlot := startSlot + slotsPerNode
        
        if i == len(masters)-1 {
            endSlot = totalSlots // 最后一个节点分配剩余所有槽位
        }
        
        var slots []int
        for j := startSlot; j < endSlot; j++ {
            slots = append(slots, j)
        }
        
        newAllocation[master] = slots
    }
    
    return newAllocation
}
```

### 6. **面试常见问题**

#### **问题1：Redis集群的数据分片原理**
- **哈希槽**：16384个槽位，使用CRC16算法计算key的槽位
- **槽位分配**：每个主节点负责一部分槽位
- **数据路由**：客户端根据key计算槽位，直接访问对应节点

#### **问题2：Redis集群的故障转移机制**
- **故障检测**：节点间互相ping检测
- **主观下线**：单个节点认为某节点故障
- **客观下线**：大多数节点认为某节点故障
- **故障转移**：从节点自动提升为主节点

#### **问题3：Redis集群vs Redis Sentinel的区别**
- **Sentinel**：主从架构，单点写入，适合读多写少
- **Cluster**：分片架构，多点写入，适合大数据量
- **复杂度**：Cluster更复杂，Sentinel更简单
- **扩展性**：Cluster支持水平扩展，Sentinel垂直扩展

### 总结

Redis集群架构设计需要考虑：

1. **数据分片策略**：合理的槽位分配和数据分布
2. **高可用保证**：主从复制和自动故障转移
3. **性能优化**：智能路由和负载均衡
4. **运维管理**：监控告警和自动化运维
5. **扩展能力**：支持动态扩容和缩容

在实际应用中，需要根据业务特点选择合适的集群方案，并建立完善的监控和运维体系，确保集群的稳定性和高可用性。
