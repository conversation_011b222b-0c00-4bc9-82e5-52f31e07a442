# 限流系统设计

## 需求分析
- **保护系统**：防止系统过载崩溃
- **公平性**：保证服务的公平使用
- **灵活性**：支持不同维度的限流策略
- **高性能**：限流本身不能成为瓶颈

## 限流算法

### 1. 固定窗口计数器
- **原理**：固定时间窗口内限制请求数量
- **优点**：简单易实现
- **缺点**：边界突刺问题

### 2. 滑动窗口计数器
- **原理**：滑动时间窗口内统计请求数
- **优点**：平滑限流，避免突刺
- **缺点**：内存占用较大

### 3. 令牌桶算法
- **原理**：固定速率产生令牌，请求消耗令牌
- **优点**：允许突发流量，平滑限流
- **缺点**：实现复杂度中等

### 4. 漏桶算法
- **原理**：请求进入漏桶，固定速率流出
- **优点**：流量整形，输出平滑
- **缺点**：不允许突发流量

## 限流维度

### 1. 用户维度
- 按用户ID限流
- 防止单用户占用过多资源
- 实现用户级别的公平性

### 2. IP维度
- 按客户端IP限流
- 防止恶意攻击
- 简单有效的防护手段

### 3. 接口维度
- 按API接口限流
- 保护核心业务接口
- 差异化服务策略

### 4. 全局维度
- 系统整体流量控制
- 保护系统稳定性
- 最后一道防线

## 分布式限流

### 1. 集中式限流
- **实现**：Redis + Lua脚本
- **优点**：精确控制、一致性好
- **缺点**：依赖外部存储、延迟较高

### 2. 分布式限流
- **实现**：本地限流 + 全局协调
- **优点**：性能好、可用性高
- **缺点**：精确度稍差

## 系统架构

### 1. 限流网关
- 统一入口限流
- 支持多种限流策略
- 配置热更新

### 2. 限流中间件
- 应用层限流
- 业务逻辑集成
- 细粒度控制

### 3. 监控告警
- 限流触发监控
- 流量趋势分析
- 自动扩容建议

## 面试要点
1. **算法选择**：不同场景下选择合适的限流算法
2. **分布式实现**：如何在分布式环境下实现精确限流
3. **性能考虑**：限流系统的性能优化
4. **降级策略**：限流触发后的处理策略
