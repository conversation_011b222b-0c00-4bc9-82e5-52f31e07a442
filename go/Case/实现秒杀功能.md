# 秒杀系统设计与实现

## 业务特点
1. **瞬时高并发**：短时间内大量用户涌入
2. **库存稀缺**：商品数量远少于用户数量
3. **时间敏感**：活动有明确的开始和结束时间
4. **用户体验要求高**：响应快速，操作简单

## 系统架构设计

### 1. 整体架构
```
用户 -> CDN -> 负载均衡 -> API网关 -> 秒杀服务 -> 缓存/数据库
                                    -> 订单服务 -> 消息队列
                                    -> 支付服务
```

### 2. 核心服务
- **秒杀服务**：处理秒杀请求，库存扣减
- **订单服务**：生成订单，状态管理
- **支付服务**：处理支付，超时取消
- **通知服务**：结果通知，状态推送

## 流量控制策略

### 1. 多层限流
- **CDN层**：静态资源缓存，减少源站压力
- **网关层**：IP限流、用户限流、接口限流
- **应用层**：业务限流、熔断降级
- **数据层**：连接池限制、慢查询优化

### 2. 用户分流
- **页面静态化**：商品详情页预生成
- **按钮控制**：未开始时按钮置灰
- **验证码**：防止脚本刷单
- **排队机制**：超出处理能力时引导排队

### 3. 缓存策略
- **商品信息缓存**：提前预热到Redis
- **库存缓存**：Redis存储可售库存
- **用户状态缓存**：防止重复购买
- **页面缓存**：减少动态渲染

## 库存管理

### 1. 分层库存设计
```
总库存(DB) -> 可售库存(Redis) -> 预扣库存(内存)
```

### 2. 库存扣减流程
1. **预检查**：Redis检查库存是否充足
2. **原子扣减**：使用Lua脚本保证原子性（详见"超卖问题.md"）
3. **异步同步**：定期同步到数据库
4. **兜底机制**：Redis故障时降级到数据库

### 3. 库存回补
- **支付超时**：自动释放预扣库存
- **订单取消**：手动释放库存
- **系统异常**：补偿机制恢复库存

## 订单处理

### 1. 异步下单
- **流程**：秒杀成功 → 发送消息队列 → 异步创建订单
- **优点**：快速响应，削峰填谷
- **实现**：使用Kafka/RabbitMQ等消息队列

### 2. 订单状态机
- **待支付** → **已支付** → **已完成**
- **待支付** → **已取消**（超时或主动取消）

### 3. 超时处理
- **支付超时**：15分钟内未支付自动取消
- **实现方式**：延时队列(Redis/RabbitMQ)
- **补偿机制**：释放库存，更新订单状态

## 性能优化

### 1. 数据库优化
- **读写分离**：读请求分发到从库
- **分库分表**：按用户ID或商品ID分片
- **索引优化**：合理设计索引，避免全表扫描
- **连接池**：控制数据库连接数

### 2. 缓存优化
- **多级缓存**：本地缓存 + 分布式缓存
- **缓存预热**：活动开始前预加载数据
- **缓存更新**：双写一致性，延时双删
- **缓存穿透**：布隆过滤器防护

### 3. 系统优化
- **应用调优**：JVM参数、连接池、线程池配置
- **监控告警**：实时监控QPS、响应时间、错误率

## 安全防护

### 1. 防刷机制
- **验证码**：图形验证码、滑动验证
- **设备指纹**：识别异常设备
- **行为分析**：检测异常操作模式
- **黑名单**：封禁恶意用户和IP

### 2. 接口安全
- **签名验证**：防止接口被恶意调用
- **时间戳校验**：防止重放攻击
- **参数校验**：严格校验请求参数
- **HTTPS**：保证数据传输安全

## 监控与运维

### 1. 关键指标
- **QPS/TPS**：每秒请求数和事务数
- **响应时间**：接口平均响应时间
- **成功率**：秒杀成功率
- **库存消耗**：实时库存变化

### 2. 告警机制
- **阈值告警**：指标超过预设阈值
- **异常告警**：系统异常和错误
- **容量告警**：资源使用率过高
- **业务告警**：库存不足、订单异常

## 面试要点
1. **系统设计**：如何设计一个完整的秒杀系统
2. **性能优化**：如何应对瞬时高并发
3. **数据一致性**：如何保证库存和订单的一致性
4. **容错处理**：系统异常时的降级和恢复策略
5. **安全防护**：如何防止恶意刷单和攻击