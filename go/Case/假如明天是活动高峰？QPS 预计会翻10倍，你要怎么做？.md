# QPS翻10倍的应对策略

## 紧急扩容方案

### 1. 系统容量扩展
- **压力测试**：模拟10倍流量，找出瓶颈
- **水平扩容**：增加应用服务器实例
- **弹性伸缩**：配置云服务自动扩容

### 2. 缓存优化
- **缓存预热**：提前加载热点数据到Redis
- **多级缓存**：本地缓存+分布式缓存
- **热点数据隔离**：单独缓存高频数据

### 3. 限流降级
- **接口限流**：令牌桶/漏桶算法
- **用户分级**：VIP用户优先处理
- **熔断机制**：过载时快速失败

### 4. 数据库优化
- **读写分离**：读请求分发到从库
- **连接池调优**：增加数据库连接数
- **SQL优化**：检查慢查询，优化索引

### 5. 异步处理
- **消息队列**：削峰填谷(Kafka/RabbitMQ)
- **异步下单**：订单异步生成
- **批量处理**：合并数据库操作

### 6. CDN加速
- **静态资源**：图片、CSS、JS走CDN
- **动态内容缓存**：页面片段缓存
- **边缘计算**：就近处理请求

### 7. 监控告警
- **实时监控**：CPU、内存、QPS、响应时间
- **预警机制**：阈值告警，提前干预
- **应急预案**：故障快速恢复流程

### 8. 用户体验
- **排队系统**：超载时引导用户排队
- **页面优化**：减少HTTP请求，资源压缩
- **友好提示**：实时显示系统状态

## 面试要点
1. **优先级排序**：先解决最大瓶颈
2. **成本考虑**：权衡扩容成本和收益
3. **风险控制**：确保核心功能可用