# 分布式ID生成系统

## 需求分析
- **全局唯一**：在分布式环境下保证ID唯一性
- **高性能**：支持高并发ID生成
- **有序性**：ID趋势递增，便于数据库索引
- **高可用**：服务高可用，避免单点故障

## 主流方案对比

### 1. UUID
- **优点**：简单、本地生成、性能高
- **缺点**：无序、占用空间大、不适合做主键

### 2. 数据库自增ID
- **优点**：简单、有序、唯一
- **缺点**：性能瓶颈、单点故障

### 3. 雪花算法(Snowflake)
**结构**：1位符号位 + 41位时间戳 + 10位机器ID + 12位序列号
- **优点**：高性能、趋势递增、无依赖
- **缺点**：依赖机器时钟、机器ID管理复杂

### 4. 号段模式
从数据库批量获取ID段，本地分配
- **优点**：高性能、数据库压力小
- **缺点**：可能不连续、重启丢失部分ID

### 5. Redis生成ID
使用Redis的INCR命令生成
- **优点**：简单、性能好
- **缺点**：依赖Redis、可能丢失数据

## 系统架构设计

### 1. 多机房部署
- 不同机房分配不同的机器ID范围
- 避免机器ID冲突
- 提高系统可用性

### 2. 服务注册与发现
- 使用Consul/Etcd管理机器ID分配
- 自动故障检测和恢复
- 动态扩容支持

### 3. 监控告警
- ID生成QPS监控
- 服务可用性监控
- 机器ID冲突检测

## 关键问题解决

### 时钟回拨问题
1. **检测回拨**：记录上次生成时间，检测时钟回拨
2. **等待策略**：等待时钟追上上次时间
3. **异常处理**：回拨时间过长则抛异常

### 机器ID分配
1. **静态配置**：配置文件指定机器ID
2. **动态分配**：通过注册中心自动分配
3. **IP取模**：根据IP地址计算机器ID

## 面试要点
1. **方案选择**：根据业务场景选择合适的方案
2. **时钟回拨**：雪花算法如何处理时钟回拨问题
3. **机器ID分配**：如何保证机器ID不冲突
4. **性能优化**：如何提高ID生成性能
