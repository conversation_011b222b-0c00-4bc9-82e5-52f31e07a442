# 缓存系统设计

## 需求分析
- **高性能**：毫秒级响应时间
- **高可用**：99.9%以上可用性
- **可扩展**：支持水平扩展
- **数据一致性**：缓存与数据库一致

## 缓存架构

### 1. 单机缓存
- **本地缓存**：进程内缓存，速度最快
- **适用场景**：数据量小、更新频率低
- **优缺点**：速度快但容量有限、无法共享

### 2. 分布式缓存
- **Redis集群**：主从复制、哨兵模式、集群模式
- **适用场景**：大数据量、高并发访问
- **优缺点**：容量大、可共享但网络延迟

### 3. 多级缓存
- **L1缓存**：本地缓存
- **L2缓存**：分布式缓存
- **L3缓存**：CDN缓存

## 缓存策略

### 1. Cache-Aside
- **读**：先查缓存，miss则查数据库并更新缓存
- **写**：先更新数据库，再删除缓存
- **优点**：简单可控
- **缺点**：可能出现不一致

### 2. Write-Through
- **写**：同时更新缓存和数据库
- **优点**：数据一致性好
- **缺点**：写性能较差

### 3. Write-Behind
- **写**：先更新缓存，异步更新数据库
- **优点**：写性能好
- **缺点**：可能丢失数据

## 缓存问题及解决

### 1. 缓存穿透
- **问题**：查询不存在的数据，缓存无法命中
- **解决**：布隆过滤器、空值缓存

### 2. 缓存击穿
- **问题**：热点数据过期，大量请求直达数据库
- **解决**：互斥锁、永不过期

### 3. 缓存雪崩
- **问题**：大量缓存同时过期
- **解决**：过期时间随机化、多级缓存

### 4. 数据一致性
- **问题**：缓存与数据库数据不一致
- **解决**：延时双删、消息队列

## 缓存淘汰策略

### 1. LRU (Least Recently Used)
- 淘汰最近最少使用的数据
- 适合大部分场景

### 2. LFU (Least Frequently Used)
- 淘汰使用频率最低的数据
- 适合热点数据明显的场景

### 3. FIFO (First In First Out)
- 先进先出
- 实现简单但效果一般

## 监控指标

### 1. 性能指标
- **命中率**：缓存命中次数/总请求次数
- **响应时间**：平均响应时间、P99响应时间
- **QPS**：每秒查询数

### 2. 资源指标
- **内存使用率**：已用内存/总内存
- **CPU使用率**：缓存服务CPU占用
- **网络带宽**：网络IO使用情况

## 面试要点
1. **缓存策略选择**：根据业务场景选择合适的缓存策略
2. **一致性保证**：如何保证缓存与数据库的一致性
3. **性能优化**：如何提高缓存命中率和响应速度
4. **故障处理**：缓存故障时的降级策略
