# API网关设计

## 核心功能

### 1. 路由转发
- **动态路由**：根据请求路径、方法、头部信息路由
- **负载均衡**：轮询、随机、加权、最少连接
- **健康检查**：自动剔除不健康的后端服务

### 2. 认证授权
- **身份认证**：JWT、OAuth2.0、API Key
- **权限控制**：RBAC、ABAC权限模型
- **单点登录**：统一身份认证入口

### 3. 限流熔断
- **限流算法**：令牌桶、漏桶、滑动窗口
- **熔断机制**：快速失败，防止级联故障
- **降级策略**：返回默认值或缓存数据

### 4. 协议转换
- **HTTP/HTTPS**：标准Web协议
- **gRPC**：高性能RPC协议
- **WebSocket**：实时双向通信
- **GraphQL**：灵活的查询语言

### 5. 监控日志
- **请求追踪**：分布式链路追踪
- **性能监控**：QPS、延迟、错误率
- **访问日志**：结构化日志记录
- **告警通知**：异常情况及时通知

## 架构设计

### 1. 部署模式
- **集中式网关**：统一入口，便于管理
- **分布式网关**：按业务域拆分，减少单点故障
- **边缘网关**：CDN边缘节点部署

### 2. 高可用设计
- **多实例部署**：避免单点故障
- **故障转移**：自动切换到健康实例
- **数据同步**：配置信息实时同步

### 3. 性能优化
- **连接池**：复用HTTP连接
- **缓存策略**：响应缓存、路由缓存
- **异步处理**：非阻塞IO模型

## 技术选型

### 开源方案
- **Kong**：基于Nginx，插件丰富
- **Zuul**：Netflix开源，Spring生态
- **Envoy**：高性能，云原生
- **Traefik**：自动服务发现

### 云服务
- **AWS API Gateway**：托管服务，弹性扩展
- **Azure API Management**：企业级功能
- **阿里云API网关**：国内云服务

## 关键问题

### 1. 性能瓶颈
- **问题**：网关成为性能瓶颈
- **解决**：水平扩展、缓存优化、异步处理

### 2. 单点故障
- **问题**：网关故障影响所有服务
- **解决**：多实例部署、健康检查、故障转移

### 3. 配置管理
- **问题**：路由配置复杂，变更风险高
- **解决**：配置中心、版本控制、灰度发布

### 4. 安全防护
- **问题**：网关暴露在公网，安全风险高
- **解决**：WAF防护、DDoS防护、安全审计

## 面试要点
1. **功能设计**：网关应该包含哪些核心功能
2. **性能优化**：如何保证网关的高性能
3. **高可用**：如何避免网关成为单点故障
4. **安全考虑**：网关层面的安全防护措施
5. **技术选型**：不同场景下的网关选择
