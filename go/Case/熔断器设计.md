# 熔断器设计

## 核心概念

### 熔断器的作用
- **快速失败**：服务异常时快速返回错误，避免长时间等待
- **防止级联故障**：阻止故障在分布式系统中传播
- **系统保护**：保护下游服务不被过载
- **自动恢复**：故障恢复后自动恢复正常调用

### 解决的问题
- **服务雪崩**：一个服务故障导致整个系统崩溃
- **资源浪费**：大量线程阻塞在故障服务调用上
- **用户体验**：长时间等待导致用户体验差
- **系统稳定性**：提高系统整体稳定性

## 熔断器状态

### 1. 关闭状态(Closed)
- **正常工作**：所有请求正常通过
- **失败统计**：统计失败次数和失败率
- **状态转换**：失败率超过阈值时转为开启状态

### 2. 开启状态(Open)
- **快速失败**：直接返回错误，不调用下游服务
- **超时等待**：等待一定时间后转为半开状态
- **保护下游**：避免对故障服务的继续调用

### 3. 半开状态(Half-Open)
- **试探性调用**：允许少量请求通过
- **状态判断**：根据试探结果决定下一步状态
- **成功恢复**：试探成功则转为关闭状态
- **继续保护**：试探失败则转为开启状态

## 熔断策略

### 1. 失败率熔断
- **统计窗口**：在时间窗口内统计失败率
- **阈值判断**：失败率超过阈值触发熔断
- **适用场景**：服务调用失败率较高的情况

### 2. 异常数熔断
- **异常统计**：统计连续异常次数
- **阈值触发**：连续异常次数超过阈值
- **适用场景**：对异常敏感的场景

### 3. 响应时间熔断
- **延迟统计**：统计响应时间分布
- **慢调用判断**：响应时间超过阈值的比例
- **适用场景**：对响应时间敏感的场景

### 4. 并发数熔断
- **并发统计**：统计当前并发调用数
- **限流保护**：并发数超过阈值时熔断
- **适用场景**：保护下游服务不被过载

## 技术实现

### 1. 状态机实现
```go
type CircuitBreaker struct {
    state         State
    failureCount  int
    successCount  int
    lastFailTime  time.Time
    timeout       time.Duration
    threshold     int
}

func (cb *CircuitBreaker) Call(fn func() error) error {
    switch cb.state {
    case Closed:
        return cb.callInClosed(fn)
    case Open:
        return cb.callInOpen(fn)
    case HalfOpen:
        return cb.callInHalfOpen(fn)
    }
}
```

### 2. 滑动窗口统计
- **时间窗口**：固定时间窗口或滑动时间窗口
- **计数器**：统计成功、失败、超时次数
- **环形缓冲区**：高效的数据结构实现

### 3. 配置管理
- **动态配置**：支持运行时修改熔断参数
- **多级配置**：全局配置、服务级配置、方法级配置
- **配置热更新**：配置变更实时生效

## 降级策略

### 1. 返回默认值
- **静态默认值**：预设的固定返回值
- **动态默认值**：根据上下文计算的默认值
- **适用场景**：对数据准确性要求不高的场景

### 2. 返回缓存数据
- **本地缓存**：使用本地缓存的历史数据
- **分布式缓存**：从Redis等缓存中获取数据
- **适用场景**：数据变化不频繁的场景

### 3. 调用备用服务
- **备用接口**：调用功能相似的备用接口
- **简化服务**：调用功能简化的服务版本
- **适用场景**：有备用方案的场景

### 4. 异步处理
- **消息队列**：将请求放入队列异步处理
- **延迟处理**：延迟到服务恢复后处理
- **适用场景**：可以异步处理的业务

## 监控指标

### 1. 核心指标
- **熔断状态**：当前熔断器状态
- **失败率**：请求失败率统计
- **响应时间**：平均响应时间和P99
- **熔断次数**：熔断触发次数统计

### 2. 业务指标
- **降级次数**：降级策略执行次数
- **恢复时间**：从熔断到恢复的时间
- **影响范围**：熔断影响的请求数量
- **用户体验**：用户感知的服务质量

## 最佳实践

### 1. 参数调优
- **失败阈值**：根据服务特性设置合理阈值
- **时间窗口**：平衡响应速度和准确性
- **恢复时间**：给服务足够的恢复时间
- **试探请求**：半开状态下的试探请求数量

### 2. 组合使用
- **限流+熔断**：先限流再熔断，多层保护
- **重试+熔断**：合理的重试策略配合熔断
- **超时+熔断**：设置合理的超时时间

### 3. 测试验证
- **故障注入**：主动注入故障测试熔断效果
- **压力测试**：验证熔断器在高负载下的表现
- **恢复测试**：验证服务恢复后的自动恢复能力

## 技术选型

### 开源框架
- **Hystrix**：Netflix开源，功能完善（已停止维护）
- **Resilience4j**：轻量级，Java生态
- **Sentinel**：阿里开源，功能丰富
- **go-kit**：Go语言微服务工具包

### 云服务
- **AWS App Mesh**：服务网格中的熔断功能
- **Istio**：服务网格熔断器
- **Linkerd**：轻量级服务网格

## 面试要点
1. **状态转换**：熔断器三种状态的转换条件
2. **熔断策略**：不同场景下的熔断策略选择
3. **降级方案**：熔断后的降级处理策略
4. **参数调优**：如何设置合理的熔断参数
5. **监控告警**：熔断器的监控指标和告警策略
