# 短链接系统设计

## 需求分析
- **功能需求**：长链接转短链接、短链接跳转、统计分析
- **非功能需求**：高并发、低延迟、高可用、数据一致性

## 系统架构

### 1. 核心组件
- **短链生成服务**：负责生成唯一短链
- **跳转服务**：处理短链访问和重定向
- **统计服务**：记录访问数据和分析
- **缓存层**：Redis缓存热点数据

### 2. 短链生成算法

**方案一：自增ID + Base62编码**
- 优点：简单、唯一、有序
- 缺点：可预测、依赖数据库

**方案二：哈希算法**
- 优点：分布式友好、无依赖
- 缺点：可能冲突、需要冲突处理

**方案三：随机字符串**
- 优点：不可预测、高性能
- 缺点：需要唯一性检查

### 3. 数据存储设计

**核心表结构**
- short_code: 短链标识(主键)
- long_url: 原始长链接
- created_at: 创建时间
- expires_at: 过期时间
- click_count: 点击统计

### 4. 缓存策略
- **热点数据**：访问频繁的短链缓存到Redis
- **缓存更新**：写入时更新缓存，设置合理过期时间
- **缓存穿透**：布隆过滤器防止无效查询

### 5. 高并发优化
- **读写分离**：读请求走从库，写请求走主库
- **分库分表**：按短链hash值分片
- **异步统计**：点击统计异步更新，避免影响跳转性能

## 面试要点
1. **唯一性保证**：如何确保短链不重复
2. **性能优化**：缓存策略、数据库优化
3. **扩展性**：如何支持更大规模的访问量
4. **数据一致性**：缓存和数据库的一致性问题
