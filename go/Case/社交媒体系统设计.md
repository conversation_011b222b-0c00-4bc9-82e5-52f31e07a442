# 社交媒体系统设计(类Twitter)

## 需求分析
- **发布动态**：用户可以发布文字、图片、视频
- **关注机制**：用户可以关注其他用户
- **时间线**：显示关注用户的动态时间线
- **互动功能**：点赞、评论、转发、私信
- **搜索功能**：搜索用户、话题、内容

## 系统架构

### 1. 整体架构
```
客户端 -> 负载均衡 -> API网关 -> 微服务集群
                              -> 用户服务
                              -> 动态服务  
                              -> 时间线服务
                              -> 通知服务
                              -> 搜索服务
```

### 2. 核心服务
- **用户服务**：用户注册、登录、资料管理
- **动态服务**：发布、删除、编辑动态
- **关注服务**：关注、取消关注、好友关系
- **时间线服务**：生成用户时间线
- **互动服务**：点赞、评论、转发
- **通知服务**：系统通知、消息推送

## 数据模型设计

### 1. 用户表(Users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY,
    username VARCHAR(50) UNIQUE,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255),
    display_name VARCHAR(100),
    bio TEXT,
    avatar_url VARCHAR(255),
    followers_count INT DEFAULT 0,
    following_count INT DEFAULT 0,
    tweets_count INT DEFAULT 0,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 2. 动态表(Tweets)
```sql
CREATE TABLE tweets (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    content TEXT,
    media_urls JSON,
    reply_to_tweet_id BIGINT,
    retweet_of_tweet_id BIGINT,
    likes_count INT DEFAULT 0,
    retweets_count INT DEFAULT 0,
    replies_count INT DEFAULT 0,
    created_at TIMESTAMP,
    INDEX idx_user_time (user_id, created_at),
    INDEX idx_created_at (created_at)
);
```

### 3. 关注关系表(Follows)
```sql
CREATE TABLE follows (
    id BIGINT PRIMARY KEY,
    follower_id BIGINT,
    following_id BIGINT,
    created_at TIMESTAMP,
    UNIQUE KEY uk_follow (follower_id, following_id),
    INDEX idx_follower (follower_id),
    INDEX idx_following (following_id)
);
```

### 4. 互动表(Interactions)
```sql
CREATE TABLE likes (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    tweet_id BIGINT,
    created_at TIMESTAMP,
    UNIQUE KEY uk_user_tweet (user_id, tweet_id)
);
```

## 时间线生成

### 1. Pull模式(拉模式)
- **实现**：用户请求时实时生成时间线
- **流程**：
  1. 获取用户关注列表
  2. 查询关注用户的最新动态
  3. 按时间排序返回结果
- **优点**：存储空间小，数据实时性好
- **缺点**：查询延迟高，数据库压力大

### 2. Push模式(推模式)
- **实现**：用户发布动态时推送给所有粉丝
- **流程**：
  1. 用户发布动态
  2. 获取用户粉丝列表
  3. 将动态推送到粉丝的时间线
- **优点**：查询速度快，用户体验好
- **缺点**：存储空间大，写入压力大

### 3. 混合模式(Push+Pull)
- **策略**：根据用户类型采用不同策略
- **普通用户**：使用Push模式，预生成时间线
- **大V用户**：使用Pull模式，实时生成
- **优点**：平衡存储和性能
- **缺点**：系统复杂度高

## 缓存策略

### 1. 多级缓存
- **CDN缓存**：静态资源(头像、图片)
- **Redis缓存**：热点数据(时间线、用户信息)
- **本地缓存**：应用内存缓存

### 2. 缓存设计
```go
// 用户时间线缓存
type TimelineCache struct {
    UserID    int64     `json:"user_id"`
    TweetIDs  []int64   `json:"tweet_ids"`
    UpdatedAt time.Time `json:"updated_at"`
}

// 缓存键设计
timeline_key := fmt.Sprintf("timeline:user:%d", userID)
tweet_key := fmt.Sprintf("tweet:%d", tweetID)
user_key := fmt.Sprintf("user:%d", userID)
```

### 3. 缓存更新策略
- **写入时更新**：发布动态时更新相关缓存
- **定时刷新**：定期刷新热点数据缓存
- **LRU淘汰**：内存不足时淘汰最少使用数据

## 分库分表

### 1. 分片策略
- **用户表**：按user_id分片
- **动态表**：按user_id分片(保证用户数据在同一分片)
- **关注表**：按follower_id分片
- **时间线表**：按user_id分片

### 2. 分片算法
```go
// 一致性哈希分片
func getShardID(userID int64, shardCount int) int {
    return int(userID % int64(shardCount))
}

// 数据库路由
func getDBConnection(userID int64) *sql.DB {
    shardID := getShardID(userID, dbShardCount)
    return dbConnections[shardID]
}
```

### 3. 跨分片查询
- **时间线查询**：需要查询多个分片的数据
- **搜索功能**：使用专门的搜索引擎
- **统计功能**：使用数据仓库离线计算

## 消息队列

### 1. 异步处理
- **动态发布**：异步更新粉丝时间线
- **计数更新**：异步更新点赞、转发数
- **通知推送**：异步发送通知消息
- **数据同步**：异步同步到搜索引擎

### 2. 队列设计
```go
// 动态发布事件
type TweetPublishedEvent struct {
    TweetID   int64     `json:"tweet_id"`
    UserID    int64     `json:"user_id"`
    Content   string    `json:"content"`
    Timestamp time.Time `json:"timestamp"`
}

// 时间线更新事件
type TimelineUpdateEvent struct {
    UserID  int64   `json:"user_id"`
    TweetID int64   `json:"tweet_id"`
    Action  string  `json:"action"` // add, remove
}
```

## 搜索功能

### 1. 搜索引擎
- **Elasticsearch**：全文搜索、实时索引
- **Solr**：企业级搜索平台
- **自建搜索**：基于倒排索引的搜索引擎

### 2. 索引设计
```json
{
  "tweet_index": {
    "mappings": {
      "properties": {
        "id": {"type": "long"},
        "user_id": {"type": "long"},
        "username": {"type": "keyword"},
        "content": {"type": "text", "analyzer": "ik_max_word"},
        "hashtags": {"type": "keyword"},
        "mentions": {"type": "keyword"},
        "created_at": {"type": "date"}
      }
    }
  }
}
```

### 3. 搜索优化
- **实时索引**：新动态实时建立索引
- **热词缓存**：缓存热门搜索词结果
- **搜索建议**：提供搜索自动补全
- **个性化搜索**：基于用户历史优化结果

## 推荐算法

### 1. 内容推荐
- **协同过滤**：基于用户行为推荐
- **内容推荐**：基于动态内容推荐
- **热门推荐**：推荐热门动态和话题
- **个性化推荐**：基于用户兴趣推荐

### 2. 用户推荐
- **共同好友**：推荐共同好友
- **兴趣相似**：推荐兴趣相似用户
- **地理位置**：推荐附近用户
- **热门用户**：推荐热门博主

## 性能优化

### 1. 读优化
- **读写分离**：读请求分发到从库
- **索引优化**：合理设计数据库索引
- **查询优化**：优化SQL查询语句
- **分页优化**：使用游标分页

### 2. 写优化
- **批量写入**：批量更新数据库
- **异步写入**：非关键数据异步写入
- **写入缓冲**：使用缓冲区减少写入次数
- **分片写入**：分散写入压力

### 3. 系统优化
- **连接池**：数据库连接池管理
- **线程池**：异步任务线程池
- **内存管理**：合理管理内存使用
- **GC优化**：垃圾回收参数调优

## 安全设计

### 1. 身份认证
- **JWT Token**：无状态身份认证
- **OAuth2**：第三方登录授权
- **多因子认证**：提高账号安全性
- **设备管理**：管理登录设备

### 2. 内容安全
- **敏感词过滤**：过滤违规内容
- **图片审核**：AI识别违规图片
- **举报机制**：用户举报违规内容
- **人工审核**：人工复审违规内容

### 3. 系统安全
- **SQL注入防护**：参数化查询
- **XSS防护**：输入输出过滤
- **CSRF防护**：跨站请求伪造防护
- **DDoS防护**：分布式拒绝服务防护

## 监控告警

### 1. 业务监控
- **用户活跃度**：DAU、MAU统计
- **内容指标**：发布量、互动量
- **性能指标**：响应时间、错误率
- **资源使用**：CPU、内存、磁盘使用率

### 2. 告警机制
- **阈值告警**：指标超过预设阈值
- **异常检测**：基于机器学习的异常检测
- **业务告警**：关键业务指标异常
- **自动恢复**：自动故障恢复机制

## 面试要点
1. **系统架构**：如何设计大规模社交媒体系统架构
2. **时间线生成**：Push vs Pull模式的选择和优化
3. **数据存储**：分库分表策略和跨分片查询
4. **性能优化**：如何优化读写性能和用户体验
5. **扩展性**：如何设计可扩展的系统架构
