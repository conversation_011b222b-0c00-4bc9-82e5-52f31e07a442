# 服务发现设计

## 核心概念

### 服务发现的作用
- **服务注册**：服务启动时向注册中心注册自己
- **服务发现**：客户端从注册中心获取服务列表
- **健康检查**：监控服务实例的健康状态
- **负载均衡**：在多个服务实例间分发请求

### 解决的问题
- **动态服务地址**：服务实例IP和端口动态变化
- **服务扩缩容**：自动感知服务实例的增减
- **故障隔离**：快速发现和剔除故障实例
- **跨环境调用**：不同环境间的服务调用

## 服务发现模式

### 1. 客户端发现模式
- **流程**：客户端查询注册中心 → 获取服务列表 → 选择实例调用
- **优点**：客户端控制负载均衡策略
- **缺点**：客户端逻辑复杂，与注册中心耦合

### 2. 服务端发现模式
- **流程**：客户端调用负载均衡器 → 负载均衡器查询注册中心 → 转发请求
- **优点**：客户端逻辑简单，解耦
- **缺点**：负载均衡器成为单点，增加网络跳数

### 3. 服务网格模式
- **流程**：通过Sidecar代理实现服务发现
- **优点**：对应用透明，功能丰富
- **缺点**：架构复杂，资源开销大

## 注册中心设计

### 1. 数据模型
```
服务(Service)
├── 服务名称(ServiceName)
├── 服务实例(Instance)
│   ├── 实例ID(InstanceId)
│   ├── IP地址(IP)
│   ├── 端口(Port)
│   ├── 健康状态(Health)
│   ├── 元数据(Metadata)
│   └── 权重(Weight)
```

### 2. 存储选型
- **内存存储**：Redis、Hazelcast（高性能）
- **分布式存储**：Etcd、Consul、Zookeeper（强一致性）
- **关系数据库**：MySQL、PostgreSQL（持久化）

### 3. 一致性模型
- **强一致性**：CP模型，如Etcd、Consul
- **最终一致性**：AP模型，如Eureka
- **选择考虑**：业务对一致性的要求

## 健康检查机制

### 1. 检查方式
- **心跳检查**：服务定期发送心跳
- **主动探测**：注册中心主动检查服务
- **混合模式**：结合心跳和探测

### 2. 检查类型
- **HTTP检查**：调用健康检查接口
- **TCP检查**：检查端口连通性
- **脚本检查**：执行自定义检查脚本
- **gRPC检查**：gRPC健康检查协议

### 3. 故障处理
- **故障检测**：连续失败次数达到阈值
- **实例隔离**：将故障实例从服务列表移除
- **自动恢复**：故障恢复后自动加入服务列表

## 负载均衡策略

### 1. 基本策略
- **轮询(Round Robin)**：依次选择实例
- **随机(Random)**：随机选择实例
- **加权轮询**：根据权重分配请求
- **最少连接**：选择连接数最少的实例

### 2. 高级策略
- **一致性哈希**：保证相同请求到同一实例
- **地理位置**：选择地理位置最近的实例
- **响应时间**：选择响应时间最短的实例
- **自适应**：根据实时性能动态调整

## 技术实现

### 1. 服务注册
```go
// 服务注册示例
type ServiceRegistry interface {
    Register(service *ServiceInstance) error
    Deregister(serviceId string) error
    Heartbeat(serviceId string) error
}
```

### 2. 服务发现
```go
// 服务发现示例
type ServiceDiscovery interface {
    Discover(serviceName string) ([]*ServiceInstance, error)
    Watch(serviceName string) (<-chan []*ServiceInstance, error)
}
```

### 3. 客户端缓存
- **本地缓存**：缓存服务列表，减少注册中心压力
- **缓存更新**：定期刷新或事件驱动更新
- **缓存失效**：网络故障时使用本地缓存

## 技术选型

### 开源方案
- **Consul**：HashiCorp开源，功能完善
- **Etcd**：CoreOS开源，强一致性
- **Eureka**：Netflix开源，AP模型
- **Nacos**：阿里开源，配置+服务发现
- **Zookeeper**：Apache开源，CP模型

### 云服务
- **AWS Service Discovery**：AWS托管服务
- **Azure Service Fabric**：微软服务网格
- **Google Service Directory**：GCP服务目录

## 性能优化

### 1. 缓存优化
- **多级缓存**：客户端缓存+注册中心缓存
- **缓存预热**：系统启动时预加载热点服务
- **缓存更新**：增量更新减少网络传输

### 2. 网络优化
- **连接复用**：HTTP/2、gRPC连接复用
- **压缩传输**：gzip压缩减少传输量
- **就近访问**：选择最近的注册中心节点

### 3. 扩展性优化
- **分片存储**：按服务名分片存储
- **读写分离**：读写请求分离处理
- **异步处理**：异步处理非关键操作

## 面试要点
1. **发现模式**：客户端发现vs服务端发现的优缺点
2. **一致性选择**：CP vs AP模型的选择考虑
3. **健康检查**：如何设计有效的健康检查机制
4. **性能优化**：如何提高服务发现的性能
5. **故障处理**：服务实例故障时的处理策略
