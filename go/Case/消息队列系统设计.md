# 消息队列系统设计

## 核心功能
- **异步处理**：解耦生产者和消费者
- **削峰填谷**：平滑处理流量峰值
- **可靠传输**：保证消息不丢失
- **高吞吐量**：支持大规模消息处理

## 基本组件
- **Producer**：消息生产者
- **Consumer**：消息消费者
- **Broker**：消息代理服务器
- **Topic/Queue**：消息主题/队列

## 消息模型

### 1. 点对点模型
- **特点**：一对一消息传递
- **场景**：任务分发、负载均衡
- **保证**：消息只被一个消费者处理

### 2. 发布订阅模型
- **特点**：一对多消息传递
- **场景**：事件通知、数据同步
- **保证**：消息被所有订阅者接收

## 系统架构

### 1. 单机架构
- **优点**：简单、延迟低
- **缺点**：容量有限、单点故障
- **适用**：小规模、低并发场景

### 2. 集群架构
- **主从模式**：主节点写入，从节点读取
- **分片模式**：按主题或分区分布数据
- **优点**：高可用、可扩展

## 可靠性保证

### 1. 消息持久化
- **内存存储**：高性能但易丢失
- **磁盘存储**：可靠但性能较低
- **混合存储**：内存+磁盘组合

### 2. 消息确认机制
- **生产者确认**：确保消息成功发送
- **消费者确认**：确保消息成功处理
- **重试机制**：失败自动重试

### 3. 消息去重
- **幂等性**：重复消息不影响结果
- **去重策略**：消息ID、业务键去重

## 性能优化

### 1. 批量处理
- **批量发送**：减少网络开销
- **批量消费**：提高处理效率
- **批量确认**：减少确认次数

### 2. 分区策略
- **轮询分区**：均匀分布消息
- **哈希分区**：相同key到同一分区
- **自定义分区**：业务逻辑分区

## 消息顺序

### 1. 全局顺序
- **实现**：单分区处理
- **优点**：严格有序
- **缺点**：性能受限

### 2. 分区顺序
- **实现**：分区内有序
- **优点**：性能与顺序平衡
- **适用**：大部分业务场景

## 监控指标
- **吞吐量**：每秒处理消息数
- **延迟**：消息处理延迟
- **积压量**：未处理消息数量
- **可用性**：服务正常运行时间

## 面试要点
1. **可靠性保证**：如何确保消息不丢失、不重复
2. **性能优化**：如何提高消息处理性能
3. **顺序保证**：如何保证消息处理顺序
4. **架构选择**：根据业务场景选择合适架构
