# 超卖问题解决方案

## 问题定义
高并发场景下，多个用户同时购买导致实际销售数量超过库存数量的问题。

## 产生原因
1. **并发读取**：多个请求同时读取到相同库存数
2. **非原子操作**：读取-判断-扣减不是原子操作
3. **时间窗口**：操作间隙内其他请求介入

## 解决方案

### 1. 数据库锁机制

#### 悲观锁
- **原理**：SELECT...FOR UPDATE锁定行
- **优点**：强一致性，简单可靠
- **缺点**：性能差，容易死锁
- **适用场景**：并发量不高，一致性要求严格

#### 乐观锁
- **原理**：使用版本号或时间戳控制
- **优点**：性能好，无锁等待
- **缺点**：高并发下重试频繁
- **适用场景**：读多写少，可接受重试

### 2. Redis原子操作
- **原理**：使用Lua脚本保证原子性
- **优点**：高性能，原子操作
- **缺点**：数据一致性依赖Redis
- **适用场景**：高并发，可接受最终一致性

### 3. 分布式锁
- **原理**：Redis SETNX或Zookeeper实现
- **优点**：分布式环境下保证互斥
- **缺点**：性能开销，锁超时处理复杂
- **适用场景**：分布式系统，强一致性要求

### 4. 消息队列串行化
- **思路**：将并发请求转为串行处理
- **实现**：请求入队，单消费者顺序处理
- **优点**：彻底避免并发问题，削峰填谷
- **缺点**：响应延迟，系统复杂度增加

### 5. 库存分层设计
- **总库存**：数据库中的实际库存
- **可售库存**：Redis中的可售数量
- **预扣库存**：用户下单后预扣，支付后确认

## 方案对比

| 方案 | 性能 | 一致性 | 复杂度 | 适用场景 |
|------|------|--------|--------|----------|
| 悲观锁 | 低 | 强 | 低 | 低并发 |
| 乐观锁 | 中 | 强 | 中 | 中等并发 |
| Redis原子 | 高 | 最终 | 中 | 高并发 |
| 分布式锁 | 中 | 强 | 高 | 分布式 |
| 消息队列 | 中 | 强 | 高 | 异步场景 |

## 面试要点
1. **根本原因**：理解超卖问题的本质是并发控制问题
2. **方案选择**：根据业务场景选择合适的解决方案
3. **权衡考虑**：性能、一致性、复杂度的平衡
4. **实际应用**：结合具体业务场景分析优缺点