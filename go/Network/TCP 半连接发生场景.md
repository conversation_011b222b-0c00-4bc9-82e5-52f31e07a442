TCP 半连接（**half-open connection**）通常发生在 TCP 三次握手过程中。具体来说，在三次握手的第二步和第三步之间，连接处于半连接状态。这意味着服务器已经收到了客户端的 SYN 包，并发送了 SYN-ACK 包，但还未收到客户端的 ACK 包，从而未完全建立连接。

### **TCP 半连接的发生场景**

1. **正常三次握手过程**：
   - 客户端向服务器发送 SYN 包，表示请求建立连接。
   - 服务器接收到 SYN 包后，返回一个 SYN-ACK 包，表示同意建立连接。这时，连接进入半连接状态（SYN_RCVD 状态）。
   - 客户端收到 SYN-ACK 包后，发送一个 ACK 包，服务器收到 ACK 后，连接正式建立。

2. **半连接的典型场景**：
   - **客户端未发送 ACK 包**：客户端在接收到 SYN-ACK 包后，可能由于网络问题或恶意攻击等原因，未发送 ACK 包。这时服务器会一直保持在半连接状态，等待 ACK 包的到来，直到超时。
   - **SYN flood 攻击**：攻击者伪造大量的源 IP 地址发送 SYN 包，但不发送最终的 ACK 包，导致服务器保持大量半连接状态，耗尽资源。
   - **网络故障**：客户端与服务器之间的网络出现故障，导致客户端的 ACK 包无法到达服务器，服务器因此停留在半连接状态。

### **TCP 半连接的处理**

- **服务器处理半连接**：服务器会维护一个半连接队列，用于存储处于半连接状态的请求。TCP 协议允许服务器在等待 ACK 包时进入 SYN_RCVD 状态。如果 ACK 包未在一定时间内收到，服务器会认为连接失败，并释放资源。

- **SYN Cookies**：为了防止半连接攻击（如 SYN flood），服务器可以使用 SYN cookies 技术。当收到 SYN 包时，服务器不立即分配资源，而是生成一个特殊的 SYN cookie 作为序列号，并发送给客户端。只有在收到合法的 ACK 包后，服务器才会正式建立连接，从而避免了资源浪费。

### **总结**

TCP 半连接是 TCP 三次握手过程中连接未完全建立的状态，通常在客户端未发送 ACK 包时发生。半连接状态是网络攻击（如 SYN flood）的常见目标，因此需要采取措施如使用 SYN Cookies 来防止服务器资源被耗尽。