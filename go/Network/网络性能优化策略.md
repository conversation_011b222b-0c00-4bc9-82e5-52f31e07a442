# 网络性能优化策略

## 性能优化概述

**Q: 网络性能优化的目标是什么？**
A: 
- **减少延迟**: 降低请求响应时间
- **提高吞吐量**: 增加数据传输效率
- **优化用户体验**: 提升页面加载速度
- **降低成本**: 减少带宽和服务器资源消耗

**Q: 影响网络性能的主要因素？**
A:
- **网络延迟**: 物理距离、网络拥塞
- **带宽限制**: 网络容量瓶颈
- **服务器性能**: 处理能力和响应速度
- **协议开销**: HTTP头部、连接建立成本

## 前端优化策略

### 1. 资源优化

**文件压缩**:
```javascript
// Gzip压缩配置
app.use(compression({
    level: 6,
    threshold: 1024,
    filter: (req, res) => {
        return compression.filter(req, res);
    }
}));
```

**图片优化**:
- **格式选择**: WebP > JPEG > PNG
- **尺寸适配**: 响应式图片
- **懒加载**: 延迟加载非关键图片
- **压缩**: 有损/无损压缩

**代码优化**:
```javascript
// 代码分割
import('./module').then(module => {
    // 动态导入
});

// Tree Shaking
import { specificFunction } from 'library';
```

### 2. 缓存策略

**浏览器缓存**:
```http
# 强缓存
Cache-Control: max-age=31536000
Expires: Wed, 21 Oct 2024 07:28:00 GMT

# 协商缓存
ETag: "33a64df551425fcc55e4d42a148795d9f25f89d4"
Last-Modified: Wed, 21 Oct 2024 07:28:00 GMT
```

**缓存层次**:
- **浏览器缓存**: 本地存储
- **CDN缓存**: 边缘节点
- **代理缓存**: 中间代理服务器
- **服务器缓存**: 应用层缓存

### 3. 请求优化

**减少请求数量**:
- **资源合并**: CSS/JS文件合并
- **雪碧图**: 图片合并
- **内联资源**: 小文件内联到HTML

**并行请求**:
```javascript
// 并行请求
Promise.all([
    fetch('/api/user'),
    fetch('/api/posts'),
    fetch('/api/comments')
]).then(responses => {
    // 处理响应
});
```

**请求优先级**:
```html
<!-- 关键资源预加载 -->
<link rel="preload" href="critical.css" as="style">
<link rel="preload" href="hero-image.jpg" as="image">

<!-- DNS预解析 -->
<link rel="dns-prefetch" href="//api.example.com">
```

## 协议层优化

### 1. HTTP/2优化

**多路复用**:
- 单连接并发请求
- 消除队头阻塞
- 减少连接开销

**服务器推送**:
```javascript
// Node.js HTTP/2服务器推送
const http2 = require('http2');
const server = http2.createSecureServer(options);

server.on('stream', (stream, headers) => {
    if (headers[':path'] === '/') {
        // 推送关键资源
        stream.pushStream({ ':path': '/style.css' }, (err, pushStream) => {
            pushStream.respondWithFile('style.css');
        });
    }
});
```

**头部压缩**:
- HPACK算法
- 减少重复头部传输
- 动态表维护

### 2. HTTP/3 (QUIC)

**优势**:
- 基于UDP，减少握手延迟
- 内置加密
- 连接迁移支持
- 改进的拥塞控制

**适用场景**:
- 移动网络
- 高延迟环境
- 频繁网络切换

### 3. 连接优化

**Keep-Alive**:
```http
Connection: keep-alive
Keep-Alive: timeout=5, max=1000
```

**连接池**:
```go
// Go HTTP客户端连接池
client := &http.Client{
    Transport: &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
    },
}
```

## 服务端优化

### 1. 负载均衡

**算法选择**:
- **轮询**: 简单均匀分配
- **最少连接**: 动态负载感知
- **IP哈希**: 会话保持
- **加权**: 考虑服务器性能差异

**健康检查**:
```nginx
upstream backend {
    server ************:8080 weight=3;
    server ************:8080 weight=2;
    server ************:8080 backup;
}

location / {
    proxy_pass http://backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

### 2. 数据库优化

**查询优化**:
- 索引优化
- 查询语句优化
- 分页查询
- 连接池管理

**缓存策略**:
```go
// Redis缓存示例
func GetUserFromCache(userID string) (*User, error) {
    key := fmt.Sprintf("user:%s", userID)
    
    // 尝试从缓存获取
    cached, err := redisClient.Get(key).Result()
    if err == nil {
        var user User
        json.Unmarshal([]byte(cached), &user)
        return &user, nil
    }
    
    // 缓存未命中，从数据库获取
    user, err := getUserFromDB(userID)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    userJSON, _ := json.Marshal(user)
    redisClient.Set(key, userJSON, time.Hour).Err()
    
    return user, nil
}
```

### 3. 异步处理

**消息队列**:
```go
// 异步任务处理
func HandleRequest(w http.ResponseWriter, r *http.Request) {
    // 快速响应
    w.WriteHeader(http.StatusAccepted)
    w.Write([]byte("Request accepted"))
    
    // 异步处理
    go func() {
        processHeavyTask(r)
    }()
}
```

## 网络层优化

### 1. CDN部署

**全球分布**:
- 边缘节点部署
- 智能路由
- 就近访问

**缓存策略**:
```javascript
// CDN缓存配置
const cdnConfig = {
    staticAssets: {
        maxAge: '1y',
        immutable: true
    },
    dynamicContent: {
        maxAge: '5m',
        staleWhileRevalidate: '1h'
    }
};
```

### 2. DNS优化

**DNS解析优化**:
- 减少DNS查询次数
- 使用DNS预解析
- 选择快速DNS服务器

**DNS负载均衡**:
```
; DNS记录示例
www.example.com.  300  IN  A  ************
www.example.com.  300  IN  A  ************
www.example.com.  300  IN  A  ************
```

### 3. 网络拓扑优化

**多线路接入**:
- 电信、联通、移动
- 智能路由选择
- 故障自动切换

**专线连接**:
- 减少公网延迟
- 提高稳定性
- 保证带宽质量

## 监控与分析

### 1. 性能指标

**用户体验指标**:
- **FCP**: 首次内容绘制
- **LCP**: 最大内容绘制
- **FID**: 首次输入延迟
- **CLS**: 累积布局偏移

**网络指标**:
- **TTFB**: 首字节时间
- **DNS解析时间**: 域名解析耗时
- **连接时间**: TCP连接建立时间
- **传输时间**: 数据传输时间

### 2. 监控工具

**前端监控**:
```javascript
// Performance API
const navigation = performance.getEntriesByType('navigation')[0];
console.log('DNS查询时间:', navigation.domainLookupEnd - navigation.domainLookupStart);
console.log('TCP连接时间:', navigation.connectEnd - navigation.connectStart);
console.log('请求响应时间:', navigation.responseEnd - navigation.requestStart);
```

**服务端监控**:
- 响应时间监控
- 错误率统计
- 吞吐量分析
- 资源使用率

### 3. 性能分析

**瓶颈识别**:
- 网络延迟分析
- 服务器性能分析
- 数据库查询分析
- 第三方服务依赖分析

**优化效果评估**:
- A/B测试
- 性能对比
- 用户体验评分
- 业务指标影响

## 面试要点

**Q: 如何系统性地进行网络性能优化？**
A:
1. **前端优化**: 资源压缩、缓存、请求优化
2. **协议优化**: HTTP/2、连接复用
3. **服务端优化**: 负载均衡、缓存、异步处理
4. **网络优化**: CDN、DNS、网络拓扑

**Q: 如何选择合适的缓存策略？**
A:
- **静态资源**: 长期缓存 + 版本控制
- **动态内容**: 短期缓存 + 协商缓存
- **个性化内容**: 私有缓存或不缓存
- **API数据**: 根据更新频率设置TTL

**Q: HTTP/2相比HTTP/1.1有哪些性能优势？**
A:
- **多路复用**: 消除队头阻塞
- **服务器推送**: 主动推送资源
- **头部压缩**: 减少传输开销
- **二进制分帧**: 提高解析效率

**Q: 如何监控和评估网络性能？**
A:
- 使用Performance API收集指标
- 部署APM工具监控服务端
- 分析用户体验指标(Core Web Vitals)
- 建立性能基线和告警机制
