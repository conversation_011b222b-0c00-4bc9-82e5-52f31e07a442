## 网络模型：OSI七层与TCP/IP五层

### OSI七层模型

| 层次 | 名称 | 功能 | 主要协议 |
|------|------|------|----------|
| 7 | **应用层** | 为用户提供网络服务 | HTTP、FTP、SMTP、DNS |
| 6 | **表示层** | 数据格式化、加密解密 | MIME、SSL/TLS |
| 5 | **会话层** | 会话管理和同步 | NetBIOS、RPC |
| 4 | **传输层** | 端到端通信、流量控制 | TCP、UDP |
| 3 | **网络层** | 路由选择、数据包转发 | IP、ICMP、IPsec |
| 2 | **数据链路层** | 帧传输、错误检测 | Ethernet、PPP |
| 1 | **物理层** | 物理信号传输 | IEEE 802.3、RS-232 |

### TCP/IP五层模型

| 层次 | 名称 | 对应OSI层 | 功能 |
|------|------|-----------|------|
| 5 | **应用层** | 应用层+表示层+会话层 | 应用程序接口 |
| 4 | **传输层** | 传输层 | 端到端通信 |
| 3 | **网络层** | 网络层 | 路由和转发 |
| 2 | **数据链路层** | 数据链路层 | 帧传输 |
| 1 | **物理层** | 物理层 | 物理传输 |

### 数据封装过程

**发送过程（自上而下）**：
1. **应用层**：应用数据
2. **传输层**：添加TCP/UDP头部 → 段(Segment)
3. **网络层**：添加IP头部 → 包(Packet)
4. **数据链路层**：添加帧头和帧尾 → 帧(Frame)
5. **物理层**：转换为比特流传输

**接收过程（自下而上）**：
1. **物理层**：接收比特流
2. **数据链路层**：去除帧头尾，错误检测
3. **网络层**：去除IP头部，路由判断
4. **传输层**：去除TCP/UDP头部，端口识别
5. **应用层**：处理应用数据

### 关键协议详解

#### 传输层协议
- **TCP**：面向连接、可靠传输、流量控制
- **UDP**：无连接、快速传输、无可靠性保证

#### 网络层协议
- **IP**：提供逻辑地址和路由功能
- **ICMP**：网络诊断和错误报告

#### 应用层协议
- **HTTP/HTTPS**：Web服务
- **FTP**：文件传输
- **SMTP/POP3**：邮件服务
- **DNS**：域名解析

### 面试要点

**Q: OSI和TCP/IP模型的区别？**
A: OSI是理论模型(7层)，TCP/IP是实际应用模型(5层)，TCP/IP将OSI上三层合并为应用层

**Q: 数据在各层如何封装？**
A: 每层添加自己的头部信息，形成段→包→帧→比特流的封装过程

**Q: 为什么TCP/IP模型更实用？**
A: 简化了OSI模型，更贴近实际网络协议栈的实现