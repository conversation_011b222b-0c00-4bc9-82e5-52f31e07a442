## HTTP版本演进：1.0、1.1、2.0、3.0

### 版本对比

| 特性 | HTTP/1.0 | HTTP/1.1 | HTTP/2.0 | HTTP/3.0 |
|------|----------|----------|----------|----------|
| **连接方式** | 短连接 | 长连接 | 多路复用 | QUIC连接 |
| **传输格式** | 文本 | 文本 | 二进制帧 | 二进制帧 |
| **头部压缩** | 无 | 无 | HPACK | QPACK |
| **服务器推送** | 无 | 无 | 支持 | 支持 |
| **传输层协议** | TCP | TCP | TCP | UDP(QUIC) |
| **队头阻塞** | 有 | 有 | 无 | 无 |

### HTTP/1.0
**特点**：
- 每个请求建立新的TCP连接
- 请求完成后立即关闭连接
- 不支持Host头，无法虚拟主机
- 简单的缓存控制（Expires）

**问题**：
- 连接开销大
- 无法复用连接
- 性能较差

### HTTP/1.1
**改进**：
- **持久连接**：`Connection: keep-alive`
- **管道化**：可发送多个请求（但有队头阻塞）
- **Host头**：支持虚拟主机
- **分块传输**：`Transfer-Encoding: chunked`
- **缓存增强**：`Cache-Control`头

**问题**：
- 队头阻塞问题
- 头部冗余
- 单连接性能限制

### HTTP/2.0
**核心特性**：
- **二进制分帧**：提高解析效率
- **多路复用**：单连接并发处理多请求
- **头部压缩**：HPACK算法减少冗余
- **服务器推送**：主动推送资源
- **流优先级**：请求优先级控制

**优势**：
- 解决队头阻塞
- 减少连接数
- 提高传输效率

### HTTP/3.0
**革命性改进**：
- **基于QUIC**：使用UDP替代TCP
- **内置加密**：默认TLS加密
- **连接迁移**：网络切换时保持连接
- **0-RTT**：快速连接建立
- **改进拥塞控制**：更好的网络适应

**优势**：
- 消除TCP队头阻塞
- 减少握手延迟
- 更好的移动网络支持

### 面试要点

**Q: HTTP/1.1的队头阻塞问题？**
A: 管道化时，前面的请求阻塞会影响后续请求，HTTP/2通过多路复用解决

**Q: HTTP/2如何实现多路复用？**
A: 通过二进制分帧，将请求分解为帧，在单个连接上交错发送

**Q: HTTP/3为什么使用UDP？**
A: TCP的队头阻塞无法在应用层解决，QUIC基于UDP实现可靠传输

**Q: 服务器推送的作用？**
A: 服务器主动推送客户端需要的资源，减少往返时间