# RESTful API设计最佳实践

## REST架构原则

### 1. 资源导向设计

#### 资源命名规范
```go
// 好的API设计
GET    /api/v1/users           // 获取用户列表
GET    /api/v1/users/123       // 获取特定用户
POST   /api/v1/users           // 创建用户
PUT    /api/v1/users/123       // 更新用户
DELETE /api/v1/users/123       // 删除用户

// 嵌套资源
GET    /api/v1/users/123/orders     // 获取用户的订单
POST   /api/v1/users/123/orders     // 为用户创建订单
GET    /api/v1/users/123/orders/456 // 获取用户的特定订单

// 避免的设计
GET    /api/v1/getUsers        // 动词形式
POST   /api/v1/createUser      // 动词形式
GET    /api/v1/user_list       // 下划线命名
```

#### 资源结构设计
```go
package main

import (
    "encoding/json"
    "net/http"
    "time"
)

// 用户资源
type User struct {
    ID        int       `json:"id"`
    Username  string    `json:"username"`
    Email     string    `json:"email"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
    Profile   *Profile  `json:"profile,omitempty"`
}

type Profile struct {
    FirstName string `json:"first_name"`
    LastName  string `json:"last_name"`
    Avatar    string `json:"avatar"`
    Bio       string `json:"bio"`
}

// 订单资源
type Order struct {
    ID          int         `json:"id"`
    UserID      int         `json:"user_id"`
    Status      string      `json:"status"`
    TotalAmount float64     `json:"total_amount"`
    Items       []OrderItem `json:"items"`
    CreatedAt   time.Time   `json:"created_at"`
    UpdatedAt   time.Time   `json:"updated_at"`
}

type OrderItem struct {
    ID        int     `json:"id"`
    ProductID int     `json:"product_id"`
    Quantity  int     `json:"quantity"`
    Price     float64 `json:"price"`
}
```

### 2. HTTP方法的正确使用

#### CRUD操作映射
```go
package main

import (
    "encoding/json"
    "net/http"
    "strconv"
    "github.com/gorilla/mux"
)

type UserHandler struct {
    service UserService
}

// GET /users - 获取用户列表
func (h *UserHandler) GetUsers(w http.ResponseWriter, r *http.Request) {
    // 解析查询参数
    page, _ := strconv.Atoi(r.URL.Query().Get("page"))
    limit, _ := strconv.Atoi(r.URL.Query().Get("limit"))
    search := r.URL.Query().Get("search")
    
    if page <= 0 {
        page = 1
    }
    if limit <= 0 || limit > 100 {
        limit = 20
    }
    
    users, total, err := h.service.GetUsers(page, limit, search)
    if err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    response := map[string]interface{}{
        "data": users,
        "pagination": map[string]interface{}{
            "page":  page,
            "limit": limit,
            "total": total,
        },
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}

// GET /users/{id} - 获取特定用户
func (h *UserHandler) GetUser(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    id, err := strconv.Atoi(vars["id"])
    if err != nil {
        http.Error(w, "Invalid user ID", http.StatusBadRequest)
        return
    }
    
    user, err := h.service.GetUser(id)
    if err != nil {
        if err == ErrUserNotFound {
            http.Error(w, "User not found", http.StatusNotFound)
            return
        }
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(user)
}

// POST /users - 创建用户
func (h *UserHandler) CreateUser(w http.ResponseWriter, r *http.Request) {
    var req CreateUserRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }
    
    // 验证请求
    if err := req.Validate(); err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    
    user, err := h.service.CreateUser(&req)
    if err != nil {
        if err == ErrUserExists {
            http.Error(w, "User already exists", http.StatusConflict)
            return
        }
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusCreated)
    json.NewEncoder(w).Encode(user)
}

// PUT /users/{id} - 更新用户
func (h *UserHandler) UpdateUser(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    id, err := strconv.Atoi(vars["id"])
    if err != nil {
        http.Error(w, "Invalid user ID", http.StatusBadRequest)
        return
    }
    
    var req UpdateUserRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, "Invalid JSON", http.StatusBadRequest)
        return
    }
    
    user, err := h.service.UpdateUser(id, &req)
    if err != nil {
        if err == ErrUserNotFound {
            http.Error(w, "User not found", http.StatusNotFound)
            return
        }
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(user)
}

// DELETE /users/{id} - 删除用户
func (h *UserHandler) DeleteUser(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    id, err := strconv.Atoi(vars["id"])
    if err != nil {
        http.Error(w, "Invalid user ID", http.StatusBadRequest)
        return
    }
    
    err = h.service.DeleteUser(id)
    if err != nil {
        if err == ErrUserNotFound {
            http.Error(w, "User not found", http.StatusNotFound)
            return
        }
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    w.WriteHeader(http.StatusNoContent)
}
```

### 3. 状态码的正确使用

#### 常用状态码指南
```go
package main

import (
    "encoding/json"
    "net/http"
)

// 成功响应
func successResponse(w http.ResponseWriter, data interface{}, statusCode int) {
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(statusCode)
    
    if data != nil {
        json.NewEncoder(w).Encode(data)
    }
}

// 错误响应
type ErrorResponse struct {
    Error   string `json:"error"`
    Message string `json:"message"`
    Code    int    `json:"code"`
}

func errorResponse(w http.ResponseWriter, message string, statusCode int) {
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(statusCode)
    
    response := ErrorResponse{
        Error:   http.StatusText(statusCode),
        Message: message,
        Code:    statusCode,
    }
    
    json.NewEncoder(w).Encode(response)
}

// 验证错误响应
type ValidationError struct {
    Field   string `json:"field"`
    Message string `json:"message"`
}

type ValidationErrorResponse struct {
    Error   string            `json:"error"`
    Message string            `json:"message"`
    Code    int               `json:"code"`
    Details []ValidationError `json:"details"`
}

func validationErrorResponse(w http.ResponseWriter, errors []ValidationError) {
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusBadRequest)
    
    response := ValidationErrorResponse{
        Error:   "Validation Failed",
        Message: "The request contains invalid data",
        Code:    http.StatusBadRequest,
        Details: errors,
    }
    
    json.NewEncoder(w).Encode(response)
}

// 状态码使用示例
func handleAPIRequest(w http.ResponseWriter, r *http.Request) {
    switch r.Method {
    case http.MethodGet:
        // 200 OK - 成功获取资源
        successResponse(w, data, http.StatusOK)
        
    case http.MethodPost:
        // 201 Created - 成功创建资源
        successResponse(w, newResource, http.StatusCreated)
        
    case http.MethodPut:
        // 200 OK - 成功更新资源
        successResponse(w, updatedResource, http.StatusOK)
        
    case http.MethodDelete:
        // 204 No Content - 成功删除资源
        successResponse(w, nil, http.StatusNoContent)
        
    default:
        // 405 Method Not Allowed - 不支持的方法
        errorResponse(w, "Method not allowed", http.StatusMethodNotAllowed)
    }
}
```

## API版本控制

### 1. URL版本控制
```go
package main

import (
    "github.com/gorilla/mux"
    "net/http"
)

func setupVersionedRoutes() *mux.Router {
    r := mux.NewRouter()
    
    // API v1
    v1 := r.PathPrefix("/api/v1").Subrouter()
    v1.HandleFunc("/users", handleUsersV1).Methods("GET", "POST")
    v1.HandleFunc("/users/{id}", handleUserV1).Methods("GET", "PUT", "DELETE")
    
    // API v2
    v2 := r.PathPrefix("/api/v2").Subrouter()
    v2.HandleFunc("/users", handleUsersV2).Methods("GET", "POST")
    v2.HandleFunc("/users/{id}", handleUserV2).Methods("GET", "PUT", "DELETE")
    
    return r
}

// v1用户结构
type UserV1 struct {
    ID       int    `json:"id"`
    Name     string `json:"name"`
    Email    string `json:"email"`
}

// v2用户结构（增加了字段）
type UserV2 struct {
    ID        int    `json:"id"`
    FirstName string `json:"first_name"`
    LastName  string `json:"last_name"`
    Email     string `json:"email"`
    Phone     string `json:"phone"`
}

func handleUsersV1(w http.ResponseWriter, r *http.Request) {
    // v1 API逻辑
}

func handleUsersV2(w http.ResponseWriter, r *http.Request) {
    // v2 API逻辑
}
```

### 2. 请求头版本控制
```go
package main

import (
    "net/http"
    "strings"
)

func versionMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        version := r.Header.Get("API-Version")
        if version == "" {
            // 从Accept头获取版本
            accept := r.Header.Get("Accept")
            if strings.Contains(accept, "application/vnd.api.v2+json") {
                version = "v2"
            } else {
                version = "v1" // 默认版本
            }
        }
        
        // 将版本信息添加到context
        ctx := context.WithValue(r.Context(), "api-version", version)
        r = r.WithContext(ctx)
        
        next.ServeHTTP(w, r)
    })
}

func handleVersionedRequest(w http.ResponseWriter, r *http.Request) {
    version := r.Context().Value("api-version").(string)
    
    switch version {
    case "v1":
        handleV1Request(w, r)
    case "v2":
        handleV2Request(w, r)
    default:
        http.Error(w, "Unsupported API version", http.StatusBadRequest)
    }
}
```

## 请求验证和错误处理

### 1. 输入验证
```go
package main

import (
    "errors"
    "regexp"
    "strings"
)

type CreateUserRequest struct {
    Username string `json:"username"`
    Email    string `json:"email"`
    Password string `json:"password"`
    Age      int    `json:"age"`
}

func (r *CreateUserRequest) Validate() error {
    var errors []ValidationError
    
    // 用户名验证
    if r.Username == "" {
        errors = append(errors, ValidationError{
            Field:   "username",
            Message: "Username is required",
        })
    } else if len(r.Username) < 3 {
        errors = append(errors, ValidationError{
            Field:   "username",
            Message: "Username must be at least 3 characters",
        })
    }
    
    // 邮箱验证
    if r.Email == "" {
        errors = append(errors, ValidationError{
            Field:   "email",
            Message: "Email is required",
        })
    } else if !isValidEmail(r.Email) {
        errors = append(errors, ValidationError{
            Field:   "email",
            Message: "Invalid email format",
        })
    }
    
    // 密码验证
    if r.Password == "" {
        errors = append(errors, ValidationError{
            Field:   "password",
            Message: "Password is required",
        })
    } else if len(r.Password) < 8 {
        errors = append(errors, ValidationError{
            Field:   "password",
            Message: "Password must be at least 8 characters",
        })
    }
    
    // 年龄验证
    if r.Age < 0 || r.Age > 150 {
        errors = append(errors, ValidationError{
            Field:   "age",
            Message: "Age must be between 0 and 150",
        })
    }
    
    if len(errors) > 0 {
        return ValidationErrors(errors)
    }
    
    return nil
}

func isValidEmail(email string) bool {
    emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
    return emailRegex.MatchString(email)
}

type ValidationErrors []ValidationError

func (ve ValidationErrors) Error() string {
    var messages []string
    for _, err := range ve {
        messages = append(messages, err.Field+": "+err.Message)
    }
    return strings.Join(messages, ", ")
}
```

### 2. 统一错误处理
```go
package main

import (
    "log"
    "net/http"
    "runtime/debug"
)

// 错误处理中间件
func errorHandlingMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        defer func() {
            if err := recover(); err != nil {
                log.Printf("Panic: %v\n%s", err, debug.Stack())
                errorResponse(w, "Internal server error", http.StatusInternalServerError)
            }
        }()
        
        next.ServeHTTP(w, r)
    })
}

// 自定义错误类型
type APIError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

func (e *APIError) Error() string {
    return e.Message
}

func NewAPIError(code int, message string) *APIError {
    return &APIError{
        Code:    code,
        Message: message,
    }
}

// 错误处理函数
func handleError(w http.ResponseWriter, err error) {
    switch e := err.(type) {
    case *APIError:
        errorResponse(w, e.Message, e.Code)
    case ValidationErrors:
        validationErrorResponse(w, []ValidationError(e))
    default:
        log.Printf("Unexpected error: %v", err)
        errorResponse(w, "Internal server error", http.StatusInternalServerError)
    }
}
```

## 面试常见问题

### Q1: RESTful API的设计原则是什么？

**答案**：
1. **资源导向**：URL表示资源，不是动作
2. **HTTP方法**：使用标准HTTP方法表示操作
3. **无状态**：每个请求包含所有必要信息
4. **统一接口**：一致的API设计风格
5. **分层系统**：支持缓存、负载均衡等中间层

### Q2: 如何设计API的版本控制？

**答案**：
1. **URL版本控制**：/api/v1/users
2. **请求头版本控制**：API-Version: v1
3. **参数版本控制**：?version=v1
4. **媒体类型版本控制**：Accept: application/vnd.api.v1+json

### Q3: API的幂等性如何保证？

**答案**：
1. **GET、PUT、DELETE**：天然幂等
2. **POST**：通过幂等键保证
3. **状态检查**：操作前检查资源状态
4. **事务处理**：使用数据库事务

### Q4: 如何处理API的分页？

**答案**：
1. **基于偏移量**：page和limit参数
2. **基于游标**：cursor分页
3. **响应格式**：包含分页元数据
4. **链接头**：提供导航链接

## 最佳实践总结

1. **资源命名**：使用名词，复数形式，小写字母
2. **HTTP方法**：正确使用GET、POST、PUT、DELETE
3. **状态码**：返回合适的HTTP状态码
4. **错误处理**：统一的错误响应格式
5. **版本控制**：向后兼容的版本策略
6. **文档完善**：详细的API文档
7. **安全考虑**：认证、授权、输入验证
8. **性能优化**：缓存、分页、压缩
