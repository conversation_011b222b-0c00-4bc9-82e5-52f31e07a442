# Cookie和Session机制

## 基本概念

**Q: 什么是Cookie？**
A: Cookie是服务器发送到用户浏览器并保存在本地的小型数据文件，用于记录用户信息和状态

**Q: 什么是Session？**
A: Session是服务器端保存用户会话信息的机制，通过Session ID与客户端建立关联

**Q: 为什么需要Cookie和Session？**
A: HTTP协议是无状态的，Cookie和Session用于在多次请求间保持用户状态和信息

## Cookie详解

### 1. Cookie属性

**基本属性**:
```http
Set-Cookie: name=value; Domain=.example.com; Path=/; Expires=Wed, 09 Jun 2024 10:18:14 GMT; HttpOnly; Secure; SameSite=Strict
```

**属性说明**:
- **name=value**: Cookie的名称和值
- **Domain**: 指定Cookie的域名范围
- **Path**: 指定Cookie的路径范围
- **Expires/Max-Age**: 过期时间
- **HttpOnly**: 禁止JavaScript访问
- **Secure**: 仅在HTTPS下传输
- **SameSite**: 跨站请求策略

### 2. Cookie类型

**会话Cookie**:
- 浏览器关闭时删除
- 不设置Expires或Max-Age
- 用于临时状态保存

**持久Cookie**:
- 设置明确的过期时间
- 浏览器关闭后仍然存在
- 用于长期状态保存

**第三方Cookie**:
- 由非当前域名设置
- 用于跨站跟踪
- 隐私问题较多

### 3. Cookie安全

**安全风险**:
- **XSS攻击**: 恶意脚本窃取Cookie
- **CSRF攻击**: 跨站请求伪造
- **中间人攻击**: 网络传输被截获

**防护措施**:
- 设置HttpOnly防止XSS
- 设置Secure确保HTTPS传输
- 设置SameSite防止CSRF
- 敏感信息加密存储

## Session详解

### 1. Session工作原理

**创建流程**:
1. 用户首次访问，服务器创建Session
2. 生成唯一Session ID
3. 将Session ID发送给客户端(通常通过Cookie)
4. 客户端后续请求携带Session ID
5. 服务器根据Session ID查找对应Session

### 2. Session存储方式

**内存存储**:
- 访问速度快
- 服务器重启丢失
- 不支持集群共享

**文件存储**:
- 持久化保存
- 访问速度较慢
- 支持单机持久化

**数据库存储**:
- 持久化且可靠
- 支持集群共享
- 访问速度中等

**Redis存储**:
- 高性能访问
- 支持集群共享
- 支持过期自动清理

### 3. Session管理

**Session生命周期**:
- **创建**: 用户首次访问或登录
- **使用**: 每次请求更新访问时间
- **销毁**: 超时、用户登出或服务器重启

**Session配置**:
```go
// Go示例
session := sessions.Default(c)
session.Set("user_id", userID)
session.Options(sessions.Options{
    MaxAge:   3600,    // 1小时过期
    HttpOnly: true,    // 防止XSS
    Secure:   true,    // HTTPS only
    SameSite: http.SameSiteStrictMode,
})
session.Save()
```

## Cookie vs Session

### 对比分析

| 特性 | Cookie | Session |
|------|--------|---------|
| **存储位置** | 客户端浏览器 | 服务器端 |
| **安全性** | 相对较低 | 相对较高 |
| **存储容量** | 4KB限制 | 服务器内存限制 |
| **网络传输** | 每次请求都传输 | 仅传输Session ID |
| **服务器压力** | 无 | 需要存储和管理 |
| **跨域支持** | 受同源策略限制 | 通过Session ID支持 |

### 使用场景

**Cookie适用场景**:
- 用户偏好设置
- 购物车信息
- 自动登录令牌
- 访问统计

**Session适用场景**:
- 用户登录状态
- 敏感信息存储
- 多步骤表单
- 权限控制

## 分布式Session解决方案

### 1. Session复制

**原理**: 在集群中的所有服务器间同步Session数据

**优点**:
- 实现简单
- 无单点故障

**缺点**:
- 网络开销大
- 内存消耗多
- 扩展性差

### 2. Session粘滞

**原理**: 通过负载均衡器将用户请求固定到特定服务器

**优点**:
- 实现简单
- 性能较好

**缺点**:
- 负载不均衡
- 服务器故障影响用户

### 3. Session集中存储

**原理**: 将Session存储在独立的存储系统中

**优点**:
- 支持集群
- 数据一致性好
- 扩展性强

**缺点**:
- 增加网络延迟
- 存储系统成为瓶颈

**实现方案**:
- Redis Cluster
- MongoDB
- 数据库

### 4. 无状态设计

**JWT Token**:
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "user_id": 123,
    "exp": 1640995200
  },
  "signature": "..."
}
```

**优点**:
- 无需服务器存储
- 天然支持分布式
- 减少数据库查询

**缺点**:
- Token较大
- 无法主动失效
- 安全性要求高

## 安全最佳实践

### 1. Cookie安全

**设置安全属性**:
```http
Set-Cookie: sessionid=abc123; HttpOnly; Secure; SameSite=Strict; Max-Age=3600
```

**敏感信息处理**:
- 不在Cookie中存储敏感信息
- 对Cookie值进行加密
- 定期更换Cookie值

### 2. Session安全

**Session ID安全**:
- 使用强随机数生成Session ID
- Session ID长度足够(至少128位)
- 登录后重新生成Session ID

**Session管理**:
- 设置合理的超时时间
- 用户登出时销毁Session
- 定期清理过期Session

### 3. 传输安全

**HTTPS传输**:
- 强制使用HTTPS
- 设置HSTS头
- 使用安全的TLS版本

**防护措施**:
- 实施CSRF保护
- 添加XSS防护
- 使用内容安全策略(CSP)

## 面试要点

**Q: Cookie和Session的区别？**
A:
- **存储位置**: Cookie在客户端，Session在服务端
- **安全性**: Session更安全，Cookie容易被篡改
- **容量限制**: Cookie有4KB限制，Session受服务器内存限制
- **网络开销**: Cookie每次都传输，Session只传输ID

**Q: 如何实现分布式Session？**
A:
1. **Session复制**: 集群间同步Session
2. **Session粘滞**: 固定用户到特定服务器
3. **集中存储**: 使用Redis等外部存储
4. **无状态设计**: 使用JWT等Token机制

**Q: 如何防止Session劫持？**
A:
- 使用HTTPS传输
- 设置HttpOnly和Secure属性
- 绑定IP地址验证
- 定期更换Session ID
- 设置合理的超时时间

**Q: 什么时候选择Cookie，什么时候选择Session？**
A:
- **Cookie**: 非敏感信息、客户端偏好、减少服务器压力
- **Session**: 敏感信息、用户状态、需要服务器控制的场景
