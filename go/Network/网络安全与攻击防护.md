## 网络安全与攻击防护

### 常见网络攻击

#### 1. DDoS攻击
**定义**：分布式拒绝服务攻击，通过大量请求使服务器过载

**类型**：
- **SYN Flood**：发送大量SYN请求，耗尽服务器连接资源
- **UDP Flood**：发送大量UDP包，消耗带宽和处理能力
- **HTTP Flood**：发送大量HTTP请求，消耗应用层资源

**防护措施**：
- 限流和熔断
- CDN分发
- 防火墙过滤
- 负载均衡

#### 2. SQL注入
**原理**：通过输入恶意SQL代码，操控数据库

**示例**：
```sql
-- 正常查询
SELECT * FROM users WHERE username = 'admin' AND password = 'password'

-- 注入攻击
SELECT * FROM users WHERE username = 'admin' OR '1'='1' --' AND password = 'password'
```

**防护措施**：
- 参数化查询
- 输入验证和过滤
- 最小权限原则
- WAF（Web应用防火墙）

#### 3. XSS攻击
**类型**：
- **反射型XSS**：恶意脚本通过URL参数传入
- **存储型XSS**：恶意脚本存储在服务器
- **DOM型XSS**：通过修改DOM结构执行脚本

**防护措施**：
- 输出编码
- 内容安全策略（CSP）
- 输入验证
- HttpOnly Cookie

#### 4. CSRF攻击
**原理**：跨站请求伪造，利用用户已登录状态执行恶意操作

**防护措施**：
- CSRF Token
- 验证Referer头
- 双重Cookie验证
- SameSite Cookie属性

### HTTPS安全机制

#### SSL/TLS握手过程
```
客户端                    服务端
   |                        |
   |------ Client Hello --->|  1. 支持的加密套件
   |<----- Server Hello ----|  2. 选择加密套件+证书
   |<----- Certificate -----|  3. 服务器证书
   |<-- Server Hello Done --|  4. 握手完成
   |                        |
   |-- Client Key Exchange->|  5. 预主密钥
   |-- Change Cipher Spec ->|  6. 切换加密
   |------ Finished ------->|  7. 握手完成
   |<- Change Cipher Spec --|  8. 服务端切换
   |<------ Finished -------|  9. 握手完成
```

#### 加密机制
- **对称加密**：AES，加密数据传输
- **非对称加密**：RSA/ECDSA，交换密钥
- **哈希算法**：SHA-256，数据完整性
- **数字证书**：CA签发，身份验证

### 网络防护策略

#### 1. 防火墙
**类型**：
- **包过滤防火墙**：基于IP、端口过滤
- **状态检测防火墙**：跟踪连接状态
- **应用层防火墙**：深度包检测

**配置示例**：
```bash
# iptables规则
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT
iptables -A INPUT -p tcp --dport 22 -s ***********/24 -j ACCEPT
iptables -A INPUT -j DROP
```

#### 2. 入侵检测系统（IDS）
**功能**：
- 实时监控网络流量
- 检测异常行为模式
- 生成安全告警

**类型**：
- **网络IDS**：监控网络流量
- **主机IDS**：监控主机活动

#### 3. Web应用防火墙（WAF）
**功能**：
- 过滤HTTP/HTTPS流量
- 防护Web应用攻击
- 规则引擎匹配

### 安全协议

#### 1. IPSec
**组件**：
- **AH**：认证头，提供数据完整性
- **ESP**：封装安全载荷，提供加密
- **IKE**：密钥交换协议

#### 2. VPN
**类型**：
- **Site-to-Site VPN**：站点间连接
- **Remote Access VPN**：远程访问
- **SSL VPN**：基于SSL的VPN

### 安全最佳实践

#### 1. 网络层面
- 网络分段和隔离
- 最小权限原则
- 定期安全审计
- 入侵检测和防护

#### 2. 应用层面
- 安全编码规范
- 输入验证和输出编码
- 会话管理
- 错误处理

#### 3. 运维层面
- 定期更新补丁
- 安全配置基线
- 日志监控和分析
- 应急响应计划

### 面试要点

**Q: 如何防护DDoS攻击？**
A: 多层防护：网络层限流、CDN分发、应用层防护、流量清洗

**Q: HTTPS如何保证安全？**
A: 通过SSL/TLS提供加密传输、身份认证、数据完整性验证

**Q: SQL注入的原理和防护？**
A: 原理是拼接恶意SQL代码，防护通过参数化查询、输入验证

**Q: XSS和CSRF的区别？**
A: XSS是注入恶意脚本，CSRF是伪造用户请求，防护方法不同

**Q: 如何设计安全的网络架构？**
A: 分层防护、网络隔离、最小权限、监控告警、应急响应
