# 跨域问题与解决方案

## 跨域基本概念

**Q: 什么是跨域？**
A: 跨域是指浏览器的同源策略限制，当协议、域名、端口任一不同时，就会产生跨域问题

**Q: 什么是同源策略？**
A: 同源策略是浏览器的安全机制，限制一个源的文档或脚本与另一个源的资源进行交互

**同源条件**:
- 协议相同 (http/https)
- 域名相同 (example.com)
- 端口相同 (80/443/8080)

## 跨域场景示例

### 1. 常见跨域情况

```javascript
// 当前页面: https://www.example.com:8080

// 跨域情况
https://api.example.com:8080    // 子域名不同
http://www.example.com:8080     // 协议不同
https://www.example.com:3000    // 端口不同
https://www.other.com:8080      // 域名不同

// 同源情况
https://www.example.com:8080/api    // 同协议、域名、端口
```

### 2. 跨域限制内容

**受限制的操作**:
- XMLHttpRequest和Fetch API
- DOM访问
- Cookie、LocalStorage访问
- Canvas图像数据读取

**不受限制的操作**:
- 图片、CSS、JS文件加载
- 表单提交
- iframe嵌入(部分限制)

## 跨域解决方案

### 1. CORS (Cross-Origin Resource Sharing)

**简单请求**:
```http
GET /api/data HTTP/1.1
Host: api.example.com
Origin: https://www.example.com

HTTP/1.1 200 OK
Access-Control-Allow-Origin: https://www.example.com
Access-Control-Allow-Credentials: true
```

**预检请求**:
```http
OPTIONS /api/data HTTP/1.1
Host: api.example.com
Origin: https://www.example.com
Access-Control-Request-Method: POST
Access-Control-Request-Headers: Content-Type

HTTP/1.1 200 OK
Access-Control-Allow-Origin: https://www.example.com
Access-Control-Allow-Methods: GET, POST, PUT, DELETE
Access-Control-Allow-Headers: Content-Type, Authorization
Access-Control-Max-Age: 86400
```

**CORS头部详解**:
- **Access-Control-Allow-Origin**: 允许的源
- **Access-Control-Allow-Methods**: 允许的HTTP方法
- **Access-Control-Allow-Headers**: 允许的请求头
- **Access-Control-Allow-Credentials**: 是否允许携带凭证
- **Access-Control-Max-Age**: 预检请求缓存时间

### 2. JSONP (JSON with Padding)

**原理**: 利用script标签不受同源策略限制的特性

**实现示例**:
```javascript
// 客户端
function handleResponse(data) {
    console.log(data);
}

const script = document.createElement('script');
script.src = 'https://api.example.com/data?callback=handleResponse';
document.head.appendChild(script);

// 服务端返回
handleResponse({"name": "John", "age": 30});
```

**优缺点**:
- 优点: 兼容性好，支持老浏览器
- 缺点: 只支持GET请求，安全性较低

### 3. 代理服务器

**开发环境代理**:
```javascript
// webpack.config.js
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'https://api.example.com',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  }
};
```

**生产环境代理**:
```nginx
# Nginx配置
location /api/ {
    proxy_pass https://api.example.com/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

### 4. PostMessage

**用于iframe通信**:
```javascript
// 父页面
const iframe = document.getElementById('myIframe');
iframe.contentWindow.postMessage('Hello', 'https://other.example.com');

// 子页面
window.addEventListener('message', function(event) {
    if (event.origin !== 'https://www.example.com') return;
    console.log('Received:', event.data);
    event.source.postMessage('Reply', event.origin);
});
```

### 5. WebSocket

**WebSocket不受同源策略限制**:
```javascript
const ws = new WebSocket('wss://api.example.com/ws');
ws.onopen = function() {
    ws.send('Hello Server');
};
ws.onmessage = function(event) {
    console.log('Received:', event.data);
};
```

### 6. 服务端设置

**Node.js Express示例**:
```javascript
const express = require('express');
const cors = require('cors');
const app = express();

// 允许所有源
app.use(cors());

// 自定义CORS配置
app.use(cors({
    origin: ['https://www.example.com', 'https://app.example.com'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
```

**Go Gin示例**:
```go
func CORSMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Header("Access-Control-Allow-Origin", "*")
        c.Header("Access-Control-Allow-Credentials", "true")
        c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
        c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

        if c.Request.Method == "OPTIONS" {
            c.AbortWithStatus(204)
            return
        }
        c.Next()
    }
}
```

## 安全考虑

### 1. CORS安全配置

**避免使用通配符**:
```javascript
// 不安全
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true

// 安全
Access-Control-Allow-Origin: https://trusted.example.com
Access-Control-Allow-Credentials: true
```

**动态设置Origin**:
```javascript
const allowedOrigins = ['https://www.example.com', 'https://app.example.com'];
const origin = req.headers.origin;
if (allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
}
```

### 2. 预检请求优化

**缓存预检请求**:
```http
Access-Control-Max-Age: 86400  // 24小时
```

**减少预检请求**:
- 使用简单请求方法(GET, POST, HEAD)
- 避免自定义请求头
- 使用简单的Content-Type

### 3. 凭证处理

**Cookie跨域**:
```javascript
// 客户端
fetch('https://api.example.com/data', {
    credentials: 'include'  // 携带Cookie
});

// 服务端
Access-Control-Allow-Credentials: true
Access-Control-Allow-Origin: https://www.example.com  // 不能使用*
```

## 常见问题与解决

### 1. 预检请求失败

**问题**: OPTIONS请求返回错误
**解决**: 
- 确保服务器支持OPTIONS方法
- 正确设置CORS头部
- 检查请求头是否在允许列表中

### 2. Cookie无法携带

**问题**: 跨域请求无法携带Cookie
**解决**:
- 设置`credentials: 'include'`
- 服务端设置`Access-Control-Allow-Credentials: true`
- 不能使用`Access-Control-Allow-Origin: *`

### 3. 自定义头部被拒绝

**问题**: 自定义请求头导致预检失败
**解决**:
- 在`Access-Control-Allow-Headers`中添加自定义头部
- 使用标准头部名称
- 检查头部名称拼写

## 面试要点

**Q: 什么情况下会发生跨域？**
A: 当协议、域名、端口任一不同时就会跨域，这是浏览器同源策略的限制

**Q: 有哪些解决跨域的方法？**
A:
1. **CORS**: 服务端设置响应头
2. **JSONP**: 利用script标签
3. **代理**: 服务器代理转发
4. **PostMessage**: iframe通信
5. **WebSocket**: 不受同源策略限制

**Q: CORS简单请求和预检请求的区别？**
A:
- **简单请求**: 满足特定条件，直接发送请求
- **预检请求**: 不满足简单请求条件，先发送OPTIONS请求

**Q: 如何安全地配置CORS？**
A:
- 明确指定允许的源，避免使用通配符
- 谨慎设置`Access-Control-Allow-Credentials`
- 限制允许的方法和头部
- 设置合理的缓存时间

**Q: 为什么需要同源策略？**
A: 同源策略是重要的安全机制，防止恶意网站读取其他网站的敏感数据，如Cookie、LocalStorage等
