HTTP 协议定义了一组方法（又称为请求方式），用于指示客户端要对资源执行的操作。主要的 HTTP 方法包括：

### 常用的 HTTP 方法

1. **GET**  
   - **功能**：请求获取指定资源的表示。GET 方法用于从服务器获取数据，不应该改变服务器上的资源状态。
   - **特点**：安全的、幂等的（多次相同的 GET 请求应返回相同的结果）。

2. **POST**  
   - **功能**：向指定资源提交数据，用于创建或更新资源。POST 方法可能会导致服务器上的资源状态发生改变。
   - **特点**：非幂等的（多次相同的 POST 请求可能会创建多个资源或重复操作）。

3. **PUT**  
   - **功能**：更新指定资源的表示，或者创建资源（如果资源不存在的话）。PUT 方法会用请求中的数据替换指定资源的现有内容。
   - **特点**：幂等的（多次相同的 PUT 请求将产生相同的结果）。

4. **DELETE**  
   - **功能**：请求删除指定的资源。
   - **特点**：幂等的（多次相同的 DELETE 请求应产生相同的结果，尽管删除操作可能会导致资源不存在）。

5. **HEAD**  
   - **功能**：获取指定资源的元数据（如头部信息），不获取资源主体。HEAD 方法与 GET 方法类似，但不返回资源主体部分。
   - **特点**：安全的、幂等的。

6. **OPTIONS**  
   - **功能**：查询服务器支持的 HTTP 方法。OPTIONS 方法用于描述目标资源的通信选项。
   - **特点**：安全的、幂等的。

7. **PATCH**  
   - **功能**：对资源进行部分修改。与 PUT 不同，PATCH 方法只需提交修改的数据，而不是整个资源。
   - **特点**：非幂等的（多次相同的 PATCH 请求可能产生不同的结果）。

8. **TRACE**  
   - **功能**：回显服务器收到的请求，用于诊断和调试。TRACE 方法用于将服务器收到的请求报文原封不动地返回给客户端。
   - **特点**：安全的、幂等的。

9. **CONNECT**  
   - **功能**：将请求的通道建立为一个透明的 TCP 通道，用于 SSL/TLS 连接等。CONNECT 方法用于创建一个隧道，用于在客户端和服务器之间传递数据。
   - **特点**：通常与 HTTPS 相关，用于代理服务器。

这些方法提供了不同的操作功能，允许客户端与服务器之间进行各种交互，满足 Web 应用程序的需求。