# 负载均衡算法与策略

## 负载均衡基本概念

**Q: 什么是负载均衡？**
A: 负载均衡是将网络请求分发到多个服务器上，避免单点过载，提高系统可用性和性能的技术

**Q: 负载均衡的主要目标？**
A:
- **提高可用性**: 避免单点故障
- **提升性能**: 分散请求压力
- **水平扩展**: 支持服务器动态增减
- **资源优化**: 充分利用服务器资源

## 常见负载均衡算法

### 1. 轮询算法 (Round Robin)

**原理**: 按顺序将请求分配给每台服务器

**优点**: 
- 实现简单
- 分配均匀

**缺点**:
- 不考虑服务器性能差异
- 不适合处理时间差异大的请求

**适用场景**: 服务器性能相近，请求处理时间相似

### 2. 加权轮询 (Weighted Round Robin)

**原理**: 根据服务器权重分配请求，权重高的服务器处理更多请求

**优点**:
- 考虑服务器性能差异
- 配置灵活

**缺点**:
- 需要预先配置权重
- 静态权重无法动态调整

**适用场景**: 服务器性能差异明显且相对固定

### 3. 最少连接 (Least Connections)

**原理**: 将请求分配给当前连接数最少的服务器

**优点**:
- 动态考虑服务器负载
- 适合长连接场景

**缺点**:
- 需要维护连接状态
- 实现复杂度较高

**适用场景**: 请求处理时间差异大，长连接应用

### 4. 加权最少连接 (Weighted Least Connections)

**原理**: 结合权重和连接数，选择加权连接数最少的服务器

**计算公式**: `活跃连接数 / 权重`

**优点**:
- 综合考虑性能和负载
- 动态调整能力强

**适用场景**: 服务器性能差异大且负载变化频繁

### 5. IP哈希 (IP Hash)

**原理**: 根据客户端IP计算哈希值，确定目标服务器

**优点**:
- 同一客户端总是访问同一服务器
- 支持会话保持

**缺点**:
- 可能导致负载不均
- 服务器变化时会话丢失

**适用场景**: 需要会话保持的应用

### 6. 最短响应时间 (Least Response Time)

**原理**: 选择响应时间最短的服务器

**优点**:
- 考虑服务器实际性能
- 用户体验最优

**缺点**:
- 需要监控响应时间
- 实现复杂

**适用场景**: 对响应时间敏感的应用

## 负载均衡层次

### 1. 四层负载均衡 (L4)

**工作层次**: 传输层(TCP/UDP)
**特点**:
- 基于IP和端口转发
- 性能高，延迟低
- 不解析应用层内容

**代表产品**: LVS、F5

### 2. 七层负载均衡 (L7)

**工作层次**: 应用层(HTTP/HTTPS)
**特点**:
- 基于内容转发
- 支持更复杂的路由规则
- 可以进行内容缓存

**代表产品**: Nginx、HAProxy

## 负载均衡架构

### 1. DNS负载均衡

**原理**: 通过DNS解析返回不同的IP地址

**优点**:
- 实现简单
- 分布式部署

**缺点**:
- 缓存导致切换延迟
- 无法检测服务器状态

### 2. 硬件负载均衡

**特点**:
- 性能强劲
- 功能丰富
- 成本较高

**代表**: F5、A10

### 3. 软件负载均衡

**特点**:
- 成本低
- 灵活性高
- 易于扩展

**代表**: Nginx、HAProxy、LVS

## 健康检查机制

**Q: 负载均衡如何检测服务器健康状态？**
A:
- **TCP检查**: 检查端口连通性
- **HTTP检查**: 发送HTTP请求检查响应
- **自定义检查**: 调用特定接口检查业务状态

**Q: 服务器故障时如何处理？**
A:
- 自动摘除故障服务器
- 将请求转发到健康服务器
- 故障恢复后自动加入服务

## 会话保持策略

### 1. 客户端IP绑定
- 基于IP哈希
- 简单但可能不均衡

### 2. Cookie绑定
- 在Cookie中标记服务器
- 灵活但依赖客户端支持

### 3. Session复制
- 服务器间同步Session
- 可靠但增加网络开销

### 4. Session集中存储
- 使用Redis等存储Session
- 高可用但增加依赖

## 面试要点

**Q: 如何选择负载均衡算法？**
A: 根据应用特点选择：
- 无状态应用：轮询、最少连接
- 有状态应用：IP哈希、会话保持
- 性能差异大：加权算法
- 响应时间敏感：最短响应时间

**Q: 负载均衡的单点故障如何解决？**
A: 
- 主备模式：Keepalived + VIP
- 集群模式：多个负载均衡器
- DNS轮询：多个负载均衡器IP

**Q: 如何监控负载均衡效果？**
A:
- 服务器负载分布
- 响应时间统计
- 错误率监控
- 连接数监控
