# 分布式链路追踪

## 1. 基础概念

### 核心术语
- **Trace**：一次完整的请求链路
- **Span**：链路中的一个操作单元
- **SpanContext**：跨进程传递的上下文信息
- **Baggage**：跨服务传递的键值对数据

### 链路关系
```
Trace: 用户下单请求
├── Span: API Gateway
├── Span: User Service
│   ├── Span: Database Query
│   └── Span: Cache Query
├── Span: Order Service
│   ├── Span: Inventory Check
│   └── Span: Payment Process
└── Span: Notification Service
```

## 2. OpenTelemetry实现

### 基础配置
```go
package main

import (
    "context"
    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/exporters/jaeger"
    "go.opentelemetry.io/otel/sdk/resource"
    "go.opentelemetry.io/otel/sdk/trace"
    semconv "go.opentelemetry.io/otel/semconv/v1.4.0"
)

func initTracer() (*trace.TracerProvider, error) {
    // 创建Jaeger导出器
    exp, err := jaeger.New(jaeger.WithCollectorEndpoint(
        jaeger.WithEndpoint("http://localhost:14268/api/traces"),
    ))
    if err != nil {
        return nil, err
    }

    // 创建资源
    res := resource.NewWithAttributes(
        semconv.SchemaURL,
        semconv.ServiceNameKey.String("my-service"),
        semconv.ServiceVersionKey.String("1.0.0"),
    )

    // 创建TracerProvider
    tp := trace.NewTracerProvider(
        trace.WithBatcher(exp),
        trace.WithResource(res),
        trace.WithSampler(trace.AlwaysSample()),
    )

    otel.SetTracerProvider(tp)
    return tp, nil
}
```

### HTTP中间件
```go
import (
    "go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
    "go.opentelemetry.io/otel/attribute"
    "go.opentelemetry.io/otel/trace"
)

func TracingMiddleware(next http.Handler) http.Handler {
    return otelhttp.NewHandler(next, "http-server",
        otelhttp.WithMessageEvents(otelhttp.ReadEvents, otelhttp.WriteEvents),
    )
}

// 手动创建Span
func handleRequest(w http.ResponseWriter, r *http.Request) {
    tracer := otel.Tracer("my-service")
    ctx, span := tracer.Start(r.Context(), "handle-request")
    defer span.End()

    // 添加属性
    span.SetAttributes(
        attribute.String("user.id", getUserID(r)),
        attribute.String("request.method", r.Method),
        attribute.String("request.url", r.URL.String()),
    )

    // 调用下游服务
    result, err := callDownstreamService(ctx)
    if err != nil {
        span.RecordError(err)
        span.SetStatus(codes.Error, err.Error())
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }

    span.SetAttributes(attribute.String("result", result))
    w.Write([]byte(result))
}
```

### gRPC集成
```go
import (
    "go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
    "google.golang.org/grpc"
)

// 客户端
func createGRPCClient() (*grpc.ClientConn, error) {
    conn, err := grpc.Dial("localhost:50051",
        grpc.WithUnaryInterceptor(otelgrpc.UnaryClientInterceptor()),
        grpc.WithStreamInterceptor(otelgrpc.StreamClientInterceptor()),
    )
    return conn, err
}

// 服务端
func createGRPCServer() *grpc.Server {
    s := grpc.NewServer(
        grpc.UnaryInterceptor(otelgrpc.UnaryServerInterceptor()),
        grpc.StreamInterceptor(otelgrpc.StreamServerInterceptor()),
    )
    return s
}
```

## 3. 数据库追踪

### SQL追踪
```go
import (
    "database/sql"
    "go.opentelemetry.io/contrib/instrumentation/database/sql/otelsql"
    semconv "go.opentelemetry.io/otel/semconv/v1.4.0"
)

func initDB() (*sql.DB, error) {
    // 注册带追踪的驱动
    driverName, err := otelsql.Register("mysql",
        otelsql.WithAttributes(semconv.DBSystemMySQL),
    )
    if err != nil {
        return nil, err
    }

    // 使用带追踪的驱动
    db, err := sql.Open(driverName, "user:password@tcp(localhost:3306)/dbname")
    if err != nil {
        return nil, err
    }

    return db, nil
}

// 手动添加数据库Span
func queryUser(ctx context.Context, db *sql.DB, userID string) (*User, error) {
    tracer := otel.Tracer("database")
    ctx, span := tracer.Start(ctx, "query-user")
    defer span.End()

    span.SetAttributes(
        attribute.String("db.statement", "SELECT * FROM users WHERE id = ?"),
        attribute.String("db.operation", "SELECT"),
        attribute.String("db.table", "users"),
    )

    var user User
    err := db.QueryRowContext(ctx, "SELECT * FROM users WHERE id = ?", userID).Scan(&user)
    if err != nil {
        span.RecordError(err)
        return nil, err
    }

    return &user, nil
}
```

### Redis追踪
```go
import (
    "github.com/go-redis/redis/v8"
    "go.opentelemetry.io/contrib/instrumentation/github.com/go-redis/redis/v8/otelredis"
)

func initRedis() *redis.Client {
    rdb := redis.NewClient(&redis.Options{
        Addr: "localhost:6379",
    })

    // 添加追踪钩子
    rdb.AddHook(otelredis.NewTracingHook())
    return rdb
}
```

## 4. 上下文传播

### HTTP头传播
```go
import (
    "go.opentelemetry.io/otel/propagation"
)

// 客户端：注入上下文到HTTP头
func makeHTTPRequest(ctx context.Context, url string) (*http.Response, error) {
    req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
    if err != nil {
        return nil, err
    }

    // 注入追踪上下文
    otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(req.Header))

    client := &http.Client{}
    return client.Do(req)
}

// 服务端：从HTTP头提取上下文
func extractContext(r *http.Request) context.Context {
    return otel.GetTextMapPropagator().Extract(r.Context(), propagation.HeaderCarrier(r.Header))
}
```

### 消息队列传播
```go
// Kafka消息追踪
func produceMessage(ctx context.Context, topic string, message []byte) error {
    tracer := otel.Tracer("kafka-producer")
    ctx, span := tracer.Start(ctx, "kafka-produce")
    defer span.End()

    // 创建消息头
    headers := make(map[string]string)
    otel.GetTextMapPropagator().Inject(ctx, propagation.MapCarrier(headers))

    // 发送消息（伪代码）
    kafkaMessage := &KafkaMessage{
        Topic:   topic,
        Value:   message,
        Headers: headers,
    }

    return producer.Send(kafkaMessage)
}

func consumeMessage(message *KafkaMessage) {
    // 提取上下文
    ctx := otel.GetTextMapPropagator().Extract(context.Background(), 
        propagation.MapCarrier(message.Headers))

    tracer := otel.Tracer("kafka-consumer")
    ctx, span := tracer.Start(ctx, "kafka-consume")
    defer span.End()

    // 处理消息
    processMessage(ctx, message.Value)
}
```

## 5. 采样策略

### 采样配置
```go
import (
    "go.opentelemetry.io/otel/sdk/trace"
)

func configureSampling() trace.Sampler {
    // 始终采样
    alwaysSample := trace.AlwaysSample()

    // 从不采样
    neverSample := trace.NeverSample()

    // 比例采样（10%）
    rateSample := trace.TraceIDRatioBased(0.1)

    // 父级采样
    parentSample := trace.ParentBased(rateSample)

    return parentSample
}
```

### 自定义采样器
```go
type CustomSampler struct {
    defaultSampler trace.Sampler
}

func (s *CustomSampler) ShouldSample(p trace.SamplingParameters) trace.SamplingResult {
    // 错误请求总是采样
    if hasError(p.Attributes) {
        return trace.SamplingResult{
            Decision: trace.RecordAndSample,
        }
    }

    // 重要用户的请求总是采样
    if isVIPUser(p.Attributes) {
        return trace.SamplingResult{
            Decision: trace.RecordAndSample,
        }
    }

    // 其他请求使用默认采样策略
    return s.defaultSampler.ShouldSample(p)
}
```

## 6. 性能优化

### 批量导出
```go
func configureBatchExporter() trace.SpanExporter {
    exp, _ := jaeger.New(jaeger.WithCollectorEndpoint())
    
    return trace.NewBatchSpanProcessor(exp,
        trace.WithBatchTimeout(5*time.Second),
        trace.WithMaxExportBatchSize(512),
        trace.WithMaxQueueSize(2048),
    )
}
```

### 资源限制
```go
func configureResourceLimits() *trace.TracerProvider {
    return trace.NewTracerProvider(
        trace.WithSpanLimits(trace.SpanLimits{
            AttributeValueLengthLimit:   1000,
            AttributeCountLimit:         100,
            EventCountLimit:            100,
            LinkCountLimit:             100,
        }),
    )
}
```

## 7. 监控和告警

### 追踪指标
```go
var (
    traceCount = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "traces_total",
            Help: "Total number of traces",
        },
        []string{"service", "operation"},
    )
    
    spanDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "span_duration_seconds",
            Help: "Span duration in seconds",
        },
        []string{"service", "operation"},
    )
)

// 自定义SpanProcessor收集指标
type MetricsSpanProcessor struct{}

func (p *MetricsSpanProcessor) OnStart(parent context.Context, s trace.ReadWriteSpan) {}

func (p *MetricsSpanProcessor) OnEnd(s trace.ReadOnlySpan) {
    duration := s.EndTime().Sub(s.StartTime())
    service := s.Resource().Attributes()["service.name"]
    operation := s.Name()
    
    traceCount.WithLabelValues(service, operation).Inc()
    spanDuration.WithLabelValues(service, operation).Observe(duration.Seconds())
}
```

## 8. 故障排查

### 错误追踪
```go
func handleError(ctx context.Context, err error) {
    span := trace.SpanFromContext(ctx)
    if span != nil {
        span.RecordError(err)
        span.SetStatus(codes.Error, err.Error())
        
        // 添加错误相关属性
        span.SetAttributes(
            attribute.String("error.type", reflect.TypeOf(err).String()),
            attribute.Bool("error.handled", true),
        )
    }
}
```

### 性能分析
```go
func analyzePerformance(ctx context.Context) {
    tracer := otel.Tracer("performance")
    ctx, span := tracer.Start(ctx, "performance-analysis")
    defer span.End()

    // 记录关键性能指标
    start := time.Now()
    
    // 执行业务逻辑
    result := performBusinessLogic(ctx)
    
    duration := time.Since(start)
    span.SetAttributes(
        attribute.Int64("duration_ms", duration.Milliseconds()),
        attribute.Int("result_size", len(result)),
        attribute.String("performance.category", categorizePerformance(duration)),
    )
}
```

## 9. 面试要点

### 核心问题
1. **分布式追踪的作用？**
   - 请求链路可视化、性能瓶颈定位、故障根因分析

2. **如何设计追踪系统？**
   - 上下文传播、采样策略、存储优化、查询性能

3. **追踪数据如何存储？**
   - 时序数据库、列式存储、分布式存储

4. **如何控制追踪开销？**
   - 合理采样、异步导出、批量处理

### 技术选型
- **Jaeger**：CNCF项目，功能完整
- **Zipkin**：Twitter开源，简单易用
- **SkyWalking**：Apache项目，支持多语言
- **云服务**：AWS X-Ray、Google Cloud Trace

### 最佳实践
- **合理设置采样率**：平衡性能和可观测性
- **标准化Span命名**：便于查询和分析
- **添加有意义的属性**：提供足够的上下文信息
- **处理敏感数据**：避免记录敏感信息

### 一句话总结
> 分布式链路追踪通过Trace和Span构建请求调用链，帮助理解分布式系统的行为和性能特征
