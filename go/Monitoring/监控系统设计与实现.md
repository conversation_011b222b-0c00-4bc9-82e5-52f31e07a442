# 监控系统设计与实现

## 1. 监控体系概述

### 监控层次
- **基础设施监控**：CPU、内存、磁盘、网络
- **应用监控**：QPS、延迟、错误率、业务指标
- **业务监控**：用户行为、业务流程、关键指标
- **端到端监控**：全链路性能和可用性

### 监控数据类型
- **Metrics**：数值型指标，如CPU使用率
- **Logs**：事件记录，如错误日志
- **Traces**：请求链路，如分布式追踪
- **Events**：状态变化，如部署、告警

## 2. 指标体系设计

### 黄金指标（Golden Signals）
1. **延迟（Latency）**：请求响应时间
2. **流量（Traffic）**：系统处理的请求量
3. **错误（Errors）**：失败请求的比率
4. **饱和度（Saturation）**：系统资源使用程度

### RED方法
- **Rate**：请求速率
- **Errors**：错误率
- **Duration**：响应时间

### USE方法
- **Utilization**：资源利用率
- **Saturation**：资源饱和度
- **Errors**：错误数量

## 3. 监控架构设计

### 经典架构
```
数据采集 -> 数据传输 -> 数据存储 -> 数据查询 -> 可视化展示 -> 告警通知
```

### 组件选型
- **采集**：Prometheus, Telegraf, Metricbeat
- **存储**：InfluxDB, Prometheus, VictoriaMetrics
- **查询**：PromQL, SQL, GraphQL
- **可视化**：Grafana, Kibana, 自研Dashboard
- **告警**：AlertManager, PagerDuty, 钉钉/企微

## 4. 数据采集策略

### Pull模式
```go
// Prometheus风格的Pull模式
type MetricsHandler struct {
    registry *prometheus.Registry
}

func (h *MetricsHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
    promhttp.HandlerFor(h.registry, promhttp.HandlerOpts{}).ServeHTTP(w, r)
}

// 注册指标
var (
    requestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "HTTP request duration in seconds",
        },
        []string{"method", "endpoint", "status"},
    )
)
```

### Push模式
```go
// 主动推送指标
type MetricsPusher struct {
    endpoint string
    client   *http.Client
}

func (p *MetricsPusher) Push(metrics []Metric) error {
    data, _ := json.Marshal(metrics)
    resp, err := p.client.Post(p.endpoint, "application/json", bytes.NewBuffer(data))
    return err
}
```

### 采集频率优化
- **高频指标**：1-5秒，如QPS、延迟
- **中频指标**：10-30秒，如CPU、内存
- **低频指标**：1-5分钟，如磁盘使用率

## 5. 存储优化

### 时序数据特点
- **时间有序**：按时间顺序写入
- **写多读少**：大量写入，相对较少查询
- **数据压缩**：历史数据可压缩存储
- **TTL管理**：自动清理过期数据

### 存储策略
```go
type RetentionPolicy struct {
    Resolution time.Duration // 数据精度
    Retention  time.Duration // 保留时间
}

var policies = []RetentionPolicy{
    {Resolution: time.Second, Retention: 24 * time.Hour},     // 1秒精度保留1天
    {Resolution: time.Minute, Retention: 7 * 24 * time.Hour}, // 1分钟精度保留7天
    {Resolution: time.Hour, Retention: 30 * 24 * time.Hour},  // 1小时精度保留30天
}
```

## 6. 告警系统设计

### 告警规则
```yaml
groups:
  - name: api_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} for {{ $labels.service }}"
      
      - alert: HighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.5
        for: 5m
        labels:
          severity: warning
```

### 告警策略
- **分级告警**：Critical > Warning > Info
- **告警抑制**：避免告警风暴
- **告警聚合**：相似告警合并
- **静默机制**：维护期间暂停告警

## 7. 可视化设计

### Dashboard设计原则
1. **分层展示**：概览 -> 详细 -> 深入
2. **关键指标突出**：重要指标放在显眼位置
3. **时间范围灵活**：支持多种时间窗口
4. **交互式探索**：支持钻取和过滤

### 图表类型选择
- **时间序列图**：趋势分析
- **饼图/柱状图**：比例分析
- **热力图**：分布分析
- **拓扑图**：依赖关系

## 8. 性能优化

### 查询优化
```go
// 查询缓存
type QueryCache struct {
    cache map[string]CacheEntry
    mutex sync.RWMutex
    ttl   time.Duration
}

func (c *QueryCache) Get(query string) ([]DataPoint, bool) {
    c.mutex.RLock()
    defer c.mutex.RUnlock()
    
    entry, exists := c.cache[query]
    if !exists || time.Since(entry.Timestamp) > c.ttl {
        return nil, false
    }
    return entry.Data, true
}
```

### 数据降采样
- **平均值降采样**：适用于连续指标
- **最大值降采样**：适用于峰值监控
- **计数降采样**：适用于事件统计

## 9. 面试要点

### 核心问题
1. **如何设计高可用监控系统？**
   - 多副本部署、故障转移
   - 监控系统自身的监控

2. **如何处理监控数据爆炸？**
   - 采样策略、数据压缩
   - 分级存储、自动清理

3. **如何避免告警疲劳？**
   - 智能告警、告警抑制
   - 根因分析、告警聚合

4. **监控系统的性能瓶颈在哪？**
   - 数据写入、查询性能
   - 存储容量、网络带宽

### 技术选型
- **开源方案**：Prometheus + Grafana
- **云原生方案**：各云厂商监控服务
- **自研方案**：定制化需求

### 一句话总结
> 监控系统通过采集、存储、查询、可视化、告警五个环节，实现对系统运行状态的全面感知和及时响应
