# Prometheus监控实战

## 1. Prometheus基础

### 核心概念
- **Metric**：指标，时间序列数据
- **Label**：标签，用于区分不同维度
- **Target**：监控目标，被抓取的端点
- **Job**：任务，一组相同类型的目标
- **Instance**：实例，单个监控目标

### 数据模型
```
metric_name{label1="value1", label2="value2"} value timestamp
```

## 2. 指标类型

### Counter（计数器）
```go
// 只增不减的累计指标
var (
    httpRequestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
)

func init() {
    prometheus.MustRegister(httpRequestsTotal)
}

// 使用示例
func handleRequest(method, endpoint, status string) {
    httpRequestsTotal.WithLabelValues(method, endpoint, status).Inc()
}
```

### Gauge（仪表盘）
```go
// 可增可减的瞬时值
var (
    currentConnections = prometheus.NewGauge(
        prometheus.GaugeOpts{
            Name: "current_connections",
            Help: "Current number of connections",
        },
    )
    
    memoryUsage = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "memory_usage_bytes",
            Help: "Memory usage in bytes",
        },
        []string{"type"},
    )
)

// 使用示例
func updateMetrics() {
    currentConnections.Set(float64(getCurrentConnections()))
    memoryUsage.WithLabelValues("heap").Set(float64(getHeapMemory()))
    memoryUsage.WithLabelValues("stack").Set(float64(getStackMemory()))
}
```

### Histogram（直方图）
```go
// 分布统计，自动计算分位数
var (
    requestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "http_request_duration_seconds",
            Help:    "HTTP request duration in seconds",
            Buckets: prometheus.DefBuckets, // 默认桶
            // 自定义桶: []float64{0.1, 0.5, 1, 2, 5, 10}
        },
        []string{"method", "endpoint"},
    )
)

// 使用示例
func measureRequest(method, endpoint string, duration time.Duration) {
    requestDuration.WithLabelValues(method, endpoint).Observe(duration.Seconds())
}
```

### Summary（摘要）
```go
// 客户端计算分位数
var (
    requestSize = prometheus.NewSummaryVec(
        prometheus.SummaryOpts{
            Name: "http_request_size_bytes",
            Help: "HTTP request size in bytes",
            Objectives: map[float64]float64{
                0.5:  0.05,  // 50th percentile with 5% tolerance
                0.9:  0.01,  // 90th percentile with 1% tolerance
                0.99: 0.001, // 99th percentile with 0.1% tolerance
            },
        },
        []string{"method"},
    )
)
```

## 3. 监控中间件

### HTTP监控中间件
```go
func PrometheusMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        start := time.Now()
        
        // 包装ResponseWriter以捕获状态码
        wrapped := &responseWriter{ResponseWriter: w, statusCode: 200}
        
        next.ServeHTTP(wrapped, r)
        
        duration := time.Since(start)
        status := strconv.Itoa(wrapped.statusCode)
        
        // 记录指标
        httpRequestsTotal.WithLabelValues(r.Method, r.URL.Path, status).Inc()
        requestDuration.WithLabelValues(r.Method, r.URL.Path).Observe(duration.Seconds())
    })
}

type responseWriter struct {
    http.ResponseWriter
    statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
    rw.statusCode = code
    rw.ResponseWriter.WriteHeader(code)
}
```

### gRPC监控中间件
```go
func UnaryServerInterceptor() grpc.UnaryServerInterceptor {
    return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
        start := time.Now()
        
        resp, err := handler(ctx, req)
        
        duration := time.Since(start)
        status := "success"
        if err != nil {
            status = "error"
        }
        
        grpcRequestsTotal.WithLabelValues(info.FullMethod, status).Inc()
        grpcRequestDuration.WithLabelValues(info.FullMethod).Observe(duration.Seconds())
        
        return resp, err
    }
}
```

## 4. 业务指标监控

### 自定义业务指标
```go
var (
    // 用户注册数
    userRegistrations = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "user_registrations_total",
            Help: "Total number of user registrations",
        },
        []string{"source", "country"},
    )
    
    // 订单金额
    orderAmount = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "order_amount_dollars",
            Help:    "Order amount in dollars",
            Buckets: []float64{10, 50, 100, 500, 1000, 5000},
        },
        []string{"category", "payment_method"},
    )
    
    // 活跃用户数
    activeUsers = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "active_users",
            Help: "Number of active users",
        },
        []string{"time_window"},
    )
)

// 业务事件记录
func recordUserRegistration(source, country string) {
    userRegistrations.WithLabelValues(source, country).Inc()
}

func recordOrder(category, paymentMethod string, amount float64) {
    orderAmount.WithLabelValues(category, paymentMethod).Observe(amount)
}

func updateActiveUsers() {
    activeUsers.WithLabelValues("1h").Set(float64(getActiveUsers(time.Hour)))
    activeUsers.WithLabelValues("24h").Set(float64(getActiveUsers(24 * time.Hour)))
}
```

## 5. PromQL查询语言

### 基础查询
```promql
# 瞬时查询
http_requests_total

# 带标签过滤
http_requests_total{method="GET", status="200"}

# 正则匹配
http_requests_total{endpoint=~"/api/.*"}

# 范围查询
http_requests_total[5m]

# 速率计算
rate(http_requests_total[5m])

# 聚合函数
sum(rate(http_requests_total[5m])) by (method)
```

### 高级查询
```promql
# 计算错误率
sum(rate(http_requests_total{status=~"5.."}[5m])) / 
sum(rate(http_requests_total[5m])) * 100

# 计算95分位延迟
histogram_quantile(0.95, 
  sum(rate(http_request_duration_seconds_bucket[5m])) by (le)
)

# 预测磁盘空间
predict_linear(disk_free_bytes[1h], 4 * 3600)

# 异常检测
abs(rate(http_requests_total[5m]) - 
    avg_over_time(rate(http_requests_total[5m])[1h:5m])) > 
    2 * stddev_over_time(rate(http_requests_total[5m])[1h:5m])
```

## 6. 告警规则

### 基础告警
```yaml
groups:
  - name: basic_alerts
    rules:
      - alert: HighErrorRate
        expr: |
          sum(rate(http_requests_total{status=~"5.."}[5m])) /
          sum(rate(http_requests_total[5m])) > 0.05
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }}"
      
      - alert: HighLatency
        expr: |
          histogram_quantile(0.95,
            sum(rate(http_request_duration_seconds_bucket[5m])) by (le)
          ) > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High latency detected"
          description: "95th percentile latency is {{ $value }}s"
```

### 复杂告警
```yaml
  - name: advanced_alerts
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
      
      - alert: DiskSpaceLow
        expr: |
          (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Disk space low on {{ $labels.instance }}"
          description: "Only {{ $value }}% disk space remaining"
      
      - alert: MemoryUsageHigh
        expr: |
          (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 80
        for: 10m
        labels:
          severity: warning
```

## 7. 性能优化

### 查询优化
```go
// 避免高基数标签
// 不好的做法
badMetric := prometheus.NewCounterVec(
    prometheus.CounterOpts{Name: "requests_total"},
    []string{"user_id"}, // 用户ID基数太高
)

// 好的做法
goodMetric := prometheus.NewCounterVec(
    prometheus.CounterOpts{Name: "requests_total"},
    []string{"service", "method", "status"}, // 低基数标签
)
```

### 存储优化
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

# 存储配置
storage:
  tsdb:
    retention.time: 15d
    retention.size: 50GB
    wal-compression: true

# 远程存储
remote_write:
  - url: "http://remote-storage/api/v1/write"
    queue_config:
      capacity: 10000
      max_samples_per_send: 1000
```

## 8. 监控最佳实践

### 指标命名规范
```go
// 好的命名
http_requests_total          // 清晰的含义
http_request_duration_seconds // 包含单位
database_connections_active   // 描述性强

// 避免的命名
requests                     // 太模糊
latency                     // 缺少单位
db_conn                     // 缩写不清晰
```

### 标签设计原则
1. **低基数**：标签值数量要控制
2. **有意义**：标签要有业务含义
3. **一致性**：相同概念使用相同标签名
4. **避免动态**：不要使用时间戳等动态值

### 监控覆盖度
- **基础设施**：CPU、内存、磁盘、网络
- **应用层**：QPS、延迟、错误率
- **业务层**：用户行为、业务指标
- **依赖服务**：数据库、缓存、消息队列

## 9. 面试要点

### 核心问题
1. **Prometheus的优势？**
   - Pull模式、多维数据模型、强大的查询语言

2. **如何设计监控指标？**
   - 遵循RED/USE方法、选择合适的指标类型

3. **如何优化Prometheus性能？**
   - 控制标签基数、合理设置抓取间隔、使用远程存储

4. **告警规则如何设计？**
   - 基于SLI/SLO、避免告警疲劳、分级告警

### 技术选型
- **单机部署**：适合中小规模
- **联邦集群**：适合大规模部署
- **远程存储**：长期数据保存
- **高可用方案**：多副本部署

### 一句话总结
> Prometheus通过Pull模式和多维数据模型，提供了强大的监控能力，需要合理设计指标和告警规则以发挥最大价值
