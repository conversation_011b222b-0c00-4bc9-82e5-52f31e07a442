# 告警和事件管理

## 1. 告警系统基础

### 告警级别
- **Critical**：系统不可用，需要立即处理
- **Warning**：系统异常，需要关注
- **Info**：信息通知，无需立即处理

### 告警生命周期
```
触发 -> 发送 -> 确认 -> 处理 -> 解决 -> 关闭
```

## 2. 告警规则设计

### SLI/SLO驱动的告警
```yaml
# 基于SLO的告警规则
groups:
  - name: slo_alerts
    rules:
      # 可用性SLO: 99.9%
      - alert: AvailabilitySLOBreach
        expr: |
          (
            sum(rate(http_requests_total{status!~"5.."}[5m])) /
            sum(rate(http_requests_total[5m]))
          ) < 0.999
        for: 2m
        labels:
          severity: critical
          slo: availability
        annotations:
          summary: "Availability SLO breach"
          description: "Current availability: {{ $value | humanizePercentage }}"
      
      # 延迟SLO: 95%请求 < 500ms
      - alert: LatencySLOBreach
        expr: |
          histogram_quantile(0.95,
            sum(rate(http_request_duration_seconds_bucket[5m])) by (le)
          ) > 0.5
        for: 5m
        labels:
          severity: warning
          slo: latency
```

### 错误预算告警
```go
// 错误预算计算
type ErrorBudget struct {
    SLO           float64       // SLO目标 (如 99.9%)
    TimeWindow    time.Duration // 时间窗口 (如 30天)
    CurrentErrors int64         // 当前错误数
    TotalRequests int64         // 总请求数
}

func (eb *ErrorBudget) RemainingBudget() float64 {
    allowedErrors := float64(eb.TotalRequests) * (1 - eb.SLO)
    usedBudget := float64(eb.CurrentErrors)
    return (allowedErrors - usedBudget) / allowedErrors
}

func (eb *ErrorBudget) BurnRate() float64 {
    currentErrorRate := float64(eb.CurrentErrors) / float64(eb.TotalRequests)
    allowedErrorRate := 1 - eb.SLO
    return currentErrorRate / allowedErrorRate
}
```

### 多窗口多燃烧率告警
```yaml
# 快速燃烧率告警（1小时内消耗5%预算）
- alert: ErrorBudgetFastBurn
  expr: |
    (
      sum(rate(http_requests_total{status=~"5.."}[1h])) /
      sum(rate(http_requests_total[1h]))
    ) > (14.4 * (1 - 0.999))  # 14.4倍正常燃烧率
  for: 2m
  labels:
    severity: critical

# 慢速燃烧率告警（6小时内消耗5%预算）
- alert: ErrorBudgetSlowBurn
  expr: |
    (
      sum(rate(http_requests_total{status=~"5.."}[6h])) /
      sum(rate(http_requests_total[6h]))
    ) > (6 * (1 - 0.999))  # 6倍正常燃烧率
  for: 15m
  labels:
    severity: warning
```

## 3. 告警路由和通知

### AlertManager配置
```yaml
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m
    
    - match:
        team: platform
      receiver: 'platform-team'
    
    - match_re:
        service: ^(database|cache)$
      receiver: 'infrastructure-team'

receivers:
  - name: 'default'
    email_configs:
      - to: '<EMAIL>'
        subject: 'Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/...'
        channel: '#alerts'
        title: 'Critical Alert'
    pagerduty_configs:
      - routing_key: 'your-pagerduty-key'
```

### 自定义告警发送
```go
type AlertSender struct {
    webhookURL string
    client     *http.Client
}

type Alert struct {
    Name        string            `json:"name"`
    Severity    string            `json:"severity"`
    Summary     string            `json:"summary"`
    Description string            `json:"description"`
    Labels      map[string]string `json:"labels"`
    StartsAt    time.Time         `json:"starts_at"`
}

func (as *AlertSender) SendAlert(alert Alert) error {
    payload, err := json.Marshal(alert)
    if err != nil {
        return err
    }

    resp, err := as.client.Post(as.webhookURL, "application/json", bytes.NewBuffer(payload))
    if err != nil {
        return err
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("failed to send alert: %d", resp.StatusCode)
    }

    return nil
}

// 集成钉钉告警
func (as *AlertSender) SendToDingTalk(alert Alert) error {
    message := map[string]interface{}{
        "msgtype": "markdown",
        "markdown": map[string]string{
            "title": fmt.Sprintf("告警: %s", alert.Name),
            "text": fmt.Sprintf(`
### %s
**级别**: %s
**描述**: %s
**时间**: %s
            `, alert.Summary, alert.Severity, alert.Description, alert.StartsAt.Format("2006-01-02 15:04:05")),
        },
    }

    payload, _ := json.Marshal(message)
    resp, err := as.client.Post(as.webhookURL, "application/json", bytes.NewBuffer(payload))
    return err
}
```

## 4. 告警抑制和静默

### 告警抑制规则
```yaml
inhibit_rules:
  # 服务宕机时抑制其他告警
  - source_match:
      alertname: ServiceDown
    target_match:
      service: api-server
    equal: ['instance']
  
  # 数据中心故障时抑制单机告警
  - source_match:
      alertname: DataCenterDown
    target_match_re:
      alertname: ^(HighCPU|HighMemory|DiskFull)$
    equal: ['datacenter']
```

### 动态静默
```go
type SilenceManager struct {
    silences map[string]Silence
    mutex    sync.RWMutex
}

type Silence struct {
    ID        string            `json:"id"`
    Matchers  map[string]string `json:"matchers"`
    StartsAt  time.Time         `json:"starts_at"`
    EndsAt    time.Time         `json:"ends_at"`
    CreatedBy string            `json:"created_by"`
    Comment   string            `json:"comment"`
}

func (sm *SilenceManager) CreateSilence(silence Silence) {
    sm.mutex.Lock()
    defer sm.mutex.Unlock()
    sm.silences[silence.ID] = silence
}

func (sm *SilenceManager) IsSilenced(labels map[string]string) bool {
    sm.mutex.RLock()
    defer sm.mutex.RUnlock()

    now := time.Now()
    for _, silence := range sm.silences {
        if now.Before(silence.StartsAt) || now.After(silence.EndsAt) {
            continue
        }

        if sm.matchesLabels(silence.Matchers, labels) {
            return true
        }
    }
    return false
}
```

## 5. 事件管理流程

### 事件分级
```go
type IncidentSeverity int

const (
    SeverityLow IncidentSeverity = iota
    SeverityMedium
    SeverityHigh
    SeverityCritical
)

type Incident struct {
    ID          string           `json:"id"`
    Title       string           `json:"title"`
    Description string           `json:"description"`
    Severity    IncidentSeverity `json:"severity"`
    Status      string           `json:"status"`
    Assignee    string           `json:"assignee"`
    CreatedAt   time.Time        `json:"created_at"`
    ResolvedAt  *time.Time       `json:"resolved_at,omitempty"`
    Timeline    []Event          `json:"timeline"`
}

type Event struct {
    Timestamp   time.Time `json:"timestamp"`
    Type        string    `json:"type"`
    Description string    `json:"description"`
    User        string    `json:"user"`
}
```

### 自动事件创建
```go
func (im *IncidentManager) HandleAlert(alert Alert) {
    // 检查是否已有相关事件
    existingIncident := im.findRelatedIncident(alert)
    if existingIncident != nil {
        im.updateIncident(existingIncident.ID, alert)
        return
    }

    // 创建新事件
    incident := Incident{
        ID:          generateID(),
        Title:       alert.Summary,
        Description: alert.Description,
        Severity:    mapAlertSeverity(alert.Severity),
        Status:      "open",
        CreatedAt:   time.Now(),
        Timeline: []Event{
            {
                Timestamp:   time.Now(),
                Type:        "created",
                Description: "Incident created from alert",
                User:        "system",
            },
        },
    }

    // 自动分配
    incident.Assignee = im.getOnCallEngineer(alert.Labels["team"])
    
    im.createIncident(incident)
    im.notifyAssignee(incident)
}
```

## 6. 根因分析

### 自动关联分析
```go
type RootCauseAnalyzer struct {
    metrics    MetricsClient
    logs       LogsClient
    traces     TracesClient
    knowledge  KnowledgeBase
}

func (rca *RootCauseAnalyzer) Analyze(incident Incident) (*Analysis, error) {
    timeRange := TimeRange{
        Start: incident.CreatedAt.Add(-15 * time.Minute),
        End:   incident.CreatedAt.Add(5 * time.Minute),
    }

    // 收集相关数据
    metrics := rca.collectMetrics(incident, timeRange)
    logs := rca.collectLogs(incident, timeRange)
    traces := rca.collectTraces(incident, timeRange)

    // 分析异常模式
    anomalies := rca.detectAnomalies(metrics, logs, traces)
    
    // 匹配已知问题
    knownIssues := rca.knowledge.FindSimilar(anomalies)

    return &Analysis{
        Anomalies:    anomalies,
        KnownIssues:  knownIssues,
        Suggestions:  rca.generateSuggestions(anomalies, knownIssues),
        Confidence:   rca.calculateConfidence(anomalies, knownIssues),
    }, nil
}
```

## 7. 告警质量优化

### 告警指标监控
```go
var (
    alertsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "alerts_total",
            Help: "Total number of alerts",
        },
        []string{"alertname", "severity", "team"},
    )
    
    alertResolutionTime = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "alert_resolution_time_seconds",
            Help: "Time to resolve alerts",
        },
        []string{"alertname", "severity"},
    )
    
    falsePositiveRate = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "alert_false_positive_rate",
            Help: "False positive rate of alerts",
        },
        []string{"alertname"},
    )
)
```

### 告警调优
```go
type AlertTuner struct {
    history []AlertEvent
}

type AlertEvent struct {
    AlertName   string
    Timestamp   time.Time
    Resolved    bool
    FalsePositive bool
    ResolutionTime time.Duration
}

func (at *AlertTuner) AnalyzeAlert(alertName string) AlertAnalysis {
    events := at.getEventsForAlert(alertName)
    
    totalAlerts := len(events)
    falsePositives := 0
    var totalResolutionTime time.Duration
    
    for _, event := range events {
        if event.FalsePositive {
            falsePositives++
        }
        if event.Resolved {
            totalResolutionTime += event.ResolutionTime
        }
    }
    
    return AlertAnalysis{
        AlertName:           alertName,
        TotalAlerts:        totalAlerts,
        FalsePositiveRate:  float64(falsePositives) / float64(totalAlerts),
        AvgResolutionTime:  totalResolutionTime / time.Duration(totalAlerts),
        Recommendation:     at.generateRecommendation(alertName, events),
    }
}
```

## 8. 面试要点

### 核心问题
1. **如何设计有效的告警规则？**
   - 基于SLI/SLO、避免告警疲劳、分级处理

2. **如何处理告警风暴？**
   - 告警抑制、根因分析、批量处理

3. **如何评估告警质量？**
   - 误报率、响应时间、覆盖率

4. **事件管理的最佳实践？**
   - 标准化流程、自动化处理、持续改进

### 技术选型
- **开源方案**：AlertManager、PagerDuty
- **云服务**：AWS CloudWatch、Azure Monitor
- **企业方案**：Splunk、Datadog

### 最佳实践
- **告警即代码**：版本控制、代码审查
- **渐进式告警**：多级阈值、时间窗口
- **自动化响应**：自愈机制、自动扩容
- **持续优化**：定期回顾、调优规则

### 一句话总结
> 告警和事件管理通过智能规则、自动化流程和持续优化，确保系统问题能够及时发现、快速响应和有效解决
