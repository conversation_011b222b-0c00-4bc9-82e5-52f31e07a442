# 日志系统设计与实现

## 1. 日志系统核心概念

### 日志级别
- **TRACE**：最详细的信息，通常只在开发时使用
- **DEBUG**：调试信息，用于问题诊断
- **INFO**：一般信息，记录程序运行状态
- **WARN**：警告信息，可能的问题但不影响运行
- **ERROR**：错误信息，需要关注但程序可继续
- **FATAL**：致命错误，程序无法继续运行

### 日志组件
- **Logger**：日志记录器，负责生成日志
- **Appender**：日志输出器，决定日志输出位置
- **Layout/Formatter**：日志格式化器，定义日志格式
- **Filter**：日志过滤器，控制日志输出条件

## 2. 日志设计原则

### 结构化日志
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "INFO",
  "service": "user-service",
  "traceId": "abc123",
  "spanId": "def456",
  "message": "User login successful",
  "userId": "12345",
  "ip": "*************",
  "duration": 150
}
```

### 日志内容要求
1. **时间戳**：精确到毫秒的时间信息
2. **日志级别**：明确的严重程度
3. **上下文信息**：请求ID、用户ID、会话ID等
4. **错误堆栈**：异常时的完整堆栈信息
5. **业务字段**：关键业务数据

## 3. 分布式日志架构

### 集中式日志收集
```
应用服务 -> 日志Agent -> 消息队列 -> 日志处理 -> 存储 -> 查询展示
```

### 组件选型
- **收集**：Filebeat, Fluentd, Logstash
- **传输**：Kafka, RabbitMQ, Redis
- **存储**：Elasticsearch, ClickHouse, HDFS
- **查询**：Kibana, Grafana, 自研平台

## 4. 高性能日志实现

### 异步日志
```go
type AsyncLogger struct {
    buffer   chan LogEntry
    writer   io.Writer
    stopCh   chan struct{}
    wg       sync.WaitGroup
}

func (l *AsyncLogger) Log(entry LogEntry) {
    select {
    case l.buffer <- entry:
    default:
        // 缓冲区满时的处理策略
        l.handleBufferFull(entry)
    }
}

func (l *AsyncLogger) worker() {
    defer l.wg.Done()
    for {
        select {
        case entry := <-l.buffer:
            l.writeEntry(entry)
        case <-l.stopCh:
            return
        }
    }
}
```

### 批量写入优化
- **批量大小**：平衡延迟和吞吐量
- **刷新策略**：定时刷新或缓冲区满时刷新
- **压缩存储**：减少磁盘空间占用

## 5. 日志采样策略

### 采样算法
1. **固定采样**：按固定比例采样
2. **自适应采样**：根据系统负载动态调整
3. **重要性采样**：错误日志优先保留

### 实现示例
```go
type Sampler interface {
    ShouldSample(level LogLevel, message string) bool
}

type RateLimitSampler struct {
    limiter *rate.Limiter
}

func (s *RateLimitSampler) ShouldSample(level LogLevel, message string) bool {
    if level >= ERROR {
        return true // 错误日志总是记录
    }
    return s.limiter.Allow()
}
```

## 6. 日志安全与合规

### 敏感信息处理
- **脱敏规则**：手机号、身份证、密码等
- **加密存储**：敏感日志加密保存
- **访问控制**：基于角色的日志访问权限

### 合规要求
- **数据保留期**：根据法规要求设置保留时间
- **审计日志**：记录系统操作和访问行为
- **数据本地化**：满足数据主权要求

## 7. 监控与告警

### 日志监控指标
- **日志量**：每秒日志条数
- **错误率**：错误日志占比
- **延迟**：日志从产生到可查询的时间
- **存储使用率**：磁盘空间使用情况

### 告警策略
```yaml
alerts:
  - name: high_error_rate
    condition: error_rate > 5%
    duration: 5m
    action: send_notification
  
  - name: log_volume_spike
    condition: log_rate > baseline * 3
    duration: 2m
    action: auto_scale
```

## 8. 面试要点

### 核心问题
1. **如何设计高并发日志系统？**
   - 异步写入、批量处理、分片存储
   - 使用消息队列解耦生产和消费

2. **日志丢失如何处理？**
   - 多副本存储、确认机制
   - 本地缓存兜底、重试机制

3. **如何控制日志存储成本？**
   - 分级存储、压缩算法
   - 采样策略、生命周期管理

4. **分布式链路追踪如何实现？**
   - TraceID和SpanID传递
   - 上下文传播机制

### 技术选型考虑
- **性能要求**：吞吐量、延迟、存储效率
- **可靠性要求**：数据一致性、容错能力
- **成本考虑**：存储成本、运维成本
- **扩展性**：水平扩展能力

### 一句话总结
> 日志系统需要在性能、可靠性、成本之间找到平衡，通过异步处理、批量写入、分级存储等技术实现高效的日志收集、存储和查询
