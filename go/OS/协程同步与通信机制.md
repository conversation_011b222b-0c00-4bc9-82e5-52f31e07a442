# 协程同步与通信机制

## 协程通信方式对比

### 1. 共享内存 vs 消息传递

#### 共享内存模型（传统线程）
```go
package main

import (
    "fmt"
    "sync"
    "time"
)

// 共享内存通信示例
var (
    sharedCounter int
    mutex         sync.Mutex
)

func sharedMemoryExample() {
    var wg sync.WaitGroup
    
    // 多个协程修改共享变量
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            
            for j := 0; j < 1000; j++ {
                mutex.Lock()
                sharedCounter++
                if sharedCounter%1000 == 0 {
                    fmt.Printf("协程 %d: counter = %d\n", id, sharedCounter)
                }
                mutex.Unlock()
            }
        }(i)
    }
    
    wg.Wait()
    fmt.Printf("最终计数: %d\n", sharedCounter)
}
```

#### 消息传递模型（Go推荐）
```go
// Channel通信示例
func channelCommunicationExample() {
    counter := make(chan int, 1)
    counter <- 0 // 初始值
    
    var wg sync.WaitGroup
    
    // 多个协程通过channel通信
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            
            for j := 0; j < 1000; j++ {
                // 从channel读取当前值
                current := <-counter
                current++
                
                if current%1000 == 0 {
                    fmt.Printf("协程 %d: counter = %d\n", id, current)
                }
                
                // 将新值写回channel
                counter <- current
            }
        }(i)
    }
    
    wg.Wait()
    finalCount := <-counter
    fmt.Printf("最终计数: %d\n", finalCount)
}
```

## Channel深度解析

### Channel类型和特性

```go
package main

import (
    "fmt"
    "time"
)

func channelTypesDemo() {
    // 1. 无缓冲channel（同步）
    unbuffered := make(chan int)
    
    go func() {
        fmt.Println("发送方准备发送")
        unbuffered <- 42
        fmt.Println("发送方发送完成")
    }()
    
    time.Sleep(100 * time.Millisecond)
    fmt.Println("接收方准备接收")
    value := <-unbuffered
    fmt.Printf("接收到: %d\n", value)
    
    // 2. 有缓冲channel（异步）
    buffered := make(chan int, 3)
    
    // 可以发送多个值而不阻塞
    buffered <- 1
    buffered <- 2
    buffered <- 3
    
    fmt.Printf("缓冲channel长度: %d, 容量: %d\n", len(buffered), cap(buffered))
    
    // 接收值
    for i := 0; i < 3; i++ {
        value := <-buffered
        fmt.Printf("从缓冲channel接收: %d\n", value)
    }
    
    // 3. 只读和只写channel
    readOnly := make(<-chan int)
    writeOnly := make(chan<- int)
    
    fmt.Printf("只读channel类型: %T\n", readOnly)
    fmt.Printf("只写channel类型: %T\n", writeOnly)
}
```

### Channel的内部实现

```go
// Channel内部结构（简化版）
type hchan struct {
    qcount   uint           // 队列中的元素数量
    dataqsiz uint           // 循环队列的大小
    buf      unsafe.Pointer // 指向循环队列的指针
    elemsize uint16         // 元素大小
    closed   uint32         // 是否关闭
    elemtype *_type         // 元素类型
    sendx    uint           // 发送索引
    recvx    uint           // 接收索引
    recvq    waitq          // 接收等待队列
    sendq    waitq          // 发送等待队列
    lock     mutex          // 互斥锁
}

// 等待队列
type waitq struct {
    first *sudog
    last  *sudog
}
```

### Channel操作的阻塞和非阻塞

```go
package main

import (
    "fmt"
    "time"
)

func channelBlockingDemo() {
    ch := make(chan int, 2)
    
    // 非阻塞发送
    select {
    case ch <- 1:
        fmt.Println("成功发送 1")
    default:
        fmt.Println("发送失败")
    }
    
    select {
    case ch <- 2:
        fmt.Println("成功发送 2")
    default:
        fmt.Println("发送失败")
    }
    
    // 这次会失败，因为缓冲区满了
    select {
    case ch <- 3:
        fmt.Println("成功发送 3")
    default:
        fmt.Println("发送失败，缓冲区满")
    }
    
    // 非阻塞接收
    select {
    case value := <-ch:
        fmt.Printf("接收到: %d\n", value)
    default:
        fmt.Println("接收失败")
    }
    
    // 带超时的操作
    select {
    case ch <- 3:
        fmt.Println("成功发送 3")
    case <-time.After(100 * time.Millisecond):
        fmt.Println("发送超时")
    }
}
```

## 高级同步原语

### 1. sync.WaitGroup

```go
package main

import (
    "fmt"
    "sync"
    "time"
)

func waitGroupExample() {
    var wg sync.WaitGroup
    
    // 启动多个工作协程
    for i := 0; i < 5; i++ {
        wg.Add(1) // 增加等待计数
        
        go func(id int) {
            defer wg.Done() // 完成时减少计数
            
            fmt.Printf("工作协程 %d 开始\n", id)
            time.Sleep(time.Duration(id) * 100 * time.Millisecond)
            fmt.Printf("工作协程 %d 完成\n", id)
        }(i)
    }
    
    fmt.Println("等待所有协程完成...")
    wg.Wait() // 等待计数归零
    fmt.Println("所有协程已完成")
}
```

### 2. sync.Once

```go
package main

import (
    "fmt"
    "sync"
)

var (
    instance *Singleton
    once     sync.Once
)

type Singleton struct {
    value string
}

func GetInstance() *Singleton {
    once.Do(func() {
        fmt.Println("创建单例实例")
        instance = &Singleton{value: "singleton"}
    })
    return instance
}

func onceExample() {
    var wg sync.WaitGroup
    
    // 多个协程同时获取单例
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            
            singleton := GetInstance()
            fmt.Printf("协程 %d 获取到: %s\n", id, singleton.value)
        }(i)
    }
    
    wg.Wait()
}
```

### 3. sync.Cond

```go
package main

import (
    "fmt"
    "sync"
    "time"
)

func condExample() {
    var mutex sync.Mutex
    cond := sync.NewCond(&mutex)
    ready := false
    
    // 等待者协程
    for i := 0; i < 3; i++ {
        go func(id int) {
            mutex.Lock()
            defer mutex.Unlock()
            
            for !ready {
                fmt.Printf("协程 %d 等待条件\n", id)
                cond.Wait() // 等待条件变量
            }
            
            fmt.Printf("协程 %d 条件满足，继续执行\n", id)
        }(i)
    }
    
    // 通知者协程
    time.Sleep(2 * time.Second)
    
    mutex.Lock()
    ready = true
    fmt.Println("条件已满足，通知所有等待者")
    cond.Broadcast() // 唤醒所有等待者
    mutex.Unlock()
    
    time.Sleep(time.Second)
}
```

## 协程池模式

### 工作池实现

```go
package main

import (
    "fmt"
    "sync"
    "time"
)

type WorkerPool struct {
    workerCount int
    jobQueue    chan Job
    wg          sync.WaitGroup
}

type Job struct {
    ID   int
    Data string
}

func NewWorkerPool(workerCount, queueSize int) *WorkerPool {
    return &WorkerPool{
        workerCount: workerCount,
        jobQueue:    make(chan Job, queueSize),
    }
}

func (wp *WorkerPool) Start() {
    for i := 0; i < wp.workerCount; i++ {
        wp.wg.Add(1)
        go wp.worker(i)
    }
}

func (wp *WorkerPool) worker(id int) {
    defer wp.wg.Done()
    
    for job := range wp.jobQueue {
        fmt.Printf("Worker %d 处理任务 %d: %s\n", id, job.ID, job.Data)
        
        // 模拟工作
        time.Sleep(100 * time.Millisecond)
        
        fmt.Printf("Worker %d 完成任务 %d\n", id, job.ID)
    }
}

func (wp *WorkerPool) Submit(job Job) {
    wp.jobQueue <- job
}

func (wp *WorkerPool) Stop() {
    close(wp.jobQueue)
    wp.wg.Wait()
}

func workerPoolExample() {
    pool := NewWorkerPool(3, 10)
    pool.Start()
    
    // 提交任务
    for i := 0; i < 10; i++ {
        job := Job{
            ID:   i,
            Data: fmt.Sprintf("任务数据 %d", i),
        }
        pool.Submit(job)
    }
    
    pool.Stop()
    fmt.Println("所有任务完成")
}
```

## 生产者-消费者模式

```go
package main

import (
    "fmt"
    "math/rand"
    "sync"
    "time"
)

func producerConsumerExample() {
    buffer := make(chan int, 5) // 缓冲区
    var wg sync.WaitGroup
    
    // 生产者
    wg.Add(1)
    go func() {
        defer wg.Done()
        defer close(buffer)
        
        for i := 0; i < 10; i++ {
            item := rand.Intn(100)
            buffer <- item
            fmt.Printf("生产者生产: %d\n", item)
            time.Sleep(100 * time.Millisecond)
        }
        
        fmt.Println("生产者完成")
    }()
    
    // 消费者
    for i := 0; i < 2; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            
            for item := range buffer {
                fmt.Printf("消费者 %d 消费: %d\n", id, item)
                time.Sleep(200 * time.Millisecond)
            }
            
            fmt.Printf("消费者 %d 完成\n", id)
        }(i)
    }
    
    wg.Wait()
    fmt.Println("生产者-消费者完成")
}
```

## 扇入扇出模式

```go
package main

import (
    "fmt"
    "sync"
    "time"
)

// 扇出：一个输入分发到多个处理器
func fanOut(input <-chan int, workers int) []<-chan int {
    outputs := make([]<-chan int, workers)
    
    for i := 0; i < workers; i++ {
        output := make(chan int)
        outputs[i] = output
        
        go func(out chan<- int) {
            defer close(out)
            
            for value := range input {
                // 处理数据
                processed := value * value
                out <- processed
                time.Sleep(100 * time.Millisecond)
            }
        }(output)
    }
    
    return outputs
}

// 扇入：多个输入合并到一个输出
func fanIn(inputs ...<-chan int) <-chan int {
    output := make(chan int)
    var wg sync.WaitGroup
    
    for _, input := range inputs {
        wg.Add(1)
        go func(in <-chan int) {
            defer wg.Done()
            
            for value := range in {
                output <- value
            }
        }(input)
    }
    
    go func() {
        wg.Wait()
        close(output)
    }()
    
    return output
}

func fanInOutExample() {
    // 创建输入数据
    input := make(chan int)
    go func() {
        defer close(input)
        for i := 1; i <= 10; i++ {
            input <- i
        }
    }()
    
    // 扇出到3个工作器
    outputs := fanOut(input, 3)
    
    // 扇入合并结果
    result := fanIn(outputs...)
    
    // 收集结果
    fmt.Println("处理结果:")
    for value := range result {
        fmt.Printf("结果: %d\n", value)
    }
}
```

## 面试常见问题

### Q1: Channel和传统锁的区别是什么？

**答案**：
1. **设计理念**：Channel是"通过通信来共享内存"，锁是"通过共享内存来通信"
2. **死锁风险**：Channel可以避免很多死锁情况
3. **组合性**：Channel更容易组合和扩展
4. **性能**：简单场景下锁可能更快，复杂场景Channel更优

### Q2: 什么时候使用Channel，什么时候使用锁？

**答案**：
- **使用Channel**：协程间通信、数据流处理、生产者消费者模式
- **使用锁**：保护共享状态、简单的临界区、性能敏感场景

### Q3: 如何避免Channel死锁？

**答案**：
1. **避免循环依赖**：确保Channel操作不形成环
2. **使用select**：提供非阻塞操作选项
3. **正确关闭Channel**：发送方负责关闭
4. **使用缓冲Channel**：减少阻塞可能性

### Q4: 协程泄漏的常见原因？

**答案**：
1. **Channel永远阻塞**：没有对应的发送/接收操作
2. **无限循环**：没有退出条件的循环
3. **资源未释放**：文件、网络连接等未关闭
4. **Context未使用**：没有使用context控制生命周期

## 最佳实践

1. **优先使用Channel**：遵循"不要通过共享内存来通信，而要通过通信来共享内存"
2. **正确关闭Channel**：发送方负责关闭，接收方检查关闭状态
3. **使用select处理多路复用**：避免阻塞和死锁
4. **合理设置缓冲区大小**：平衡内存使用和性能
5. **使用context控制生命周期**：优雅地取消和超时处理
