# 内存管理核心概念

## 1. 物理内存与虚拟内存

### 物理内存（Physical Memory）
- **定义**：实际的RAM硬件存储空间
- **特点**：有限、昂贵、直接访问速度快
- **管理**：由操作系统内核直接管理

### 虚拟内存（Virtual Memory）
- **定义**：操作系统提供给进程的抽象内存空间
- **优势**：
  - 进程隔离：每个进程有独立的地址空间
  - 内存保护：防止进程间相互干扰
  - 内存扩展：可以使用磁盘作为内存扩展

## 2. 地址转换机制

### 虚拟地址到物理地址转换
```
虚拟地址 → MMU(内存管理单元) → 物理地址
```

### 页表机制
```
虚拟地址结构：
[页号(Page Number)] [页内偏移(Page Offset)]

页表项结构：
[物理页框号] [存在位] [读写位] [用户位] [脏位] [访问位]
```

## 3. 分页内存管理

### 基本概念
- **页(Page)**：虚拟内存的固定大小块（通常4KB）
- **页框(Page Frame)**：物理内存的固定大小块
- **页表(Page Table)**：虚拟页到物理页框的映射表

### 多级页表
```
64位系统的四级页表：
PGD → PUD → PMD → PTE
```

## 4. 页面置换算法

### FIFO（先进先出）
- **原理**：替换最早进入内存的页面
- **优点**：实现简单
- **缺点**：可能出现Belady异常

### LRU（最近最少使用）
- **原理**：替换最久未使用的页面
- **优点**：性能较好，符合局部性原理
- **缺点**：实现复杂，开销大

### Clock算法（近似LRU）
- **原理**：使用引用位，给页面第二次机会
- **优点**：近似LRU性能，开销较小
- **缺点**：性能略差于LRU

### 工作集模型
- **定义**：W(t, Δ) = {页面 | 页面在时间间隔[t-Δ, t]内被访问}
- **作用**：预测进程的内存需求，防止抖动

## 5. 内存分配算法

### 连续内存分配

#### 首次适应算法（First Fit）
- **原理**：分配第一个足够大的空闲块
- **优点**：速度快，实现简单
- **缺点**：容易产生外部碎片

#### 最佳适应算法（Best Fit）
- **原理**：分配最小的足够大的空闲块
- **优点**：内存利用率高
- **缺点**：搜索时间长，产生小碎片

#### 最坏适应算法（Worst Fit）
- **原理**：分配最大的空闲块
- **优点**：剩余块较大，可用性好
- **缺点**：内存利用率低

### 伙伴系统算法
- **原理**：将内存按2的幂次方大小分块管理
- **优点**：减少外部碎片，分配释放快速
- **缺点**：存在内部碎片，实现复杂

### 内存池技术
- **原理**：预先分配固定大小的内存块
- **优点**：分配释放极快，无碎片
- **缺点**：内存利用率可能较低

## 6. 内存回收机制

### 垃圾回收算法

#### 标记-清除算法
- **原理**：标记所有可达对象，清除未标记对象
- **优点**：不移动对象，实现简单
- **缺点**：产生内存碎片，停顿时间长

#### 复制算法
- **原理**：将堆分为两部分，复制活跃对象到另一部分
- **优点**：无内存碎片，分配简单
- **缺点**：内存利用率只有50%

#### 标记-压缩算法
- **原理**：标记后压缩内存，消除碎片
- **优点**：内存利用率高，无碎片
- **缺点**：需要移动对象，开销大

### 内存压缩
- **目的**：消除内存碎片，提高内存利用率
- **时机**：内存碎片严重时触发
- **代价**：需要更新所有对象引用

## 7. 内存保护机制

### 段保护
- **原理**：基于段的访问控制
- **特权级**：0-3级，数字越小权限越高
- **保护位**：读、写、执行权限控制

### 页保护
- **页表项保护位**：
  - 存在位：页面是否在内存中
  - 读写位：是否允许写入
  - 用户位：用户态是否可访问
  - 执行位：是否允许执行代码

## 8. 常见面试问题

### 虚拟内存的优势
- **进程隔离**：每个进程有独立的地址空间
- **内存保护**：防止进程间相互干扰
- **内存扩展**：可以使用磁盘作为内存扩展
- **内存共享**：多个进程可以共享同一物理内存

### 页面置换算法比较
| 算法 | 优点 | 缺点 |
|------|------|------|
| FIFO | 简单实现 | 可能出现Belady异常 |
| LRU | 性能好 | 实现复杂，开销大 |
| Clock | 近似LRU，开销小 | 性能略差于LRU |

### 内存碎片问题
- **内部碎片**：分配的内存块内部未使用的空间
- **外部碎片**：空闲内存块太小无法满足分配请求
- **解决方案**：伙伴系统、内存池、垃圾回收

### 内存泄漏检测方法
- **静态分析**：代码审查，使用静态分析工具
- **动态检测**：运行时监控内存分配和释放
- **工具辅助**：Valgrind、AddressSanitizer等

## 面试要点总结

1. **基本概念**：物理内存、虚拟内存、地址转换
2. **分页机制**：页表结构、多级页表、页面置换
3. **内存分配**：各种分配算法的优缺点
4. **内存回收**：垃圾回收算法和压缩机制
5. **内存保护**：段保护和页保护机制
6. **问题诊断**：内存泄漏和碎片问题的解决
