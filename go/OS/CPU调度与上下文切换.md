# CPU调度与上下文切换

## CPU调度基础

### 1. 调度的基本概念

CPU调度是操作系统内核的一个核心功能，负责决定在多个可运行进程中选择哪个进程来使用CPU。

#### 调度时机
- **进程阻塞**：等待I/O操作完成
- **进程终止**：进程执行完毕或被终止
- **时间片用完**：抢占式调度中时间片到期
- **高优先级进程就绪**：更高优先级进程变为可运行状态
- **系统调用返回**：从内核态返回用户态时

#### 调度目标
- **公平性**：所有进程都能获得合理的CPU时间
- **效率**：最大化CPU利用率
- **响应时间**：最小化用户感知的延迟
- **吞吐量**：单位时间内完成的任务数量
- **实时性**：满足实时任务的时间约束

### 2. 进程状态转换

```
    创建
     ↓
   [新建] ──调度──→ [就绪] ──调度──→ [运行]
                     ↑              ↓
                     └──时间片用完──┘
                     ↑              ↓
                     └──I/O完成──[阻塞]
                                   ↓
                                [终止]
```

## 经典调度算法

### 1. 先来先服务（FCFS）

```c
// FCFS调度算法实现
struct process {
    int pid;
    int arrival_time;
    int burst_time;
    int waiting_time;
    int turnaround_time;
};

void fcfs_scheduling(struct process processes[], int n) {
    int current_time = 0;
    
    // 按到达时间排序
    sort_by_arrival_time(processes, n);
    
    for (int i = 0; i < n; i++) {
        // 如果当前时间小于进程到达时间，CPU空闲
        if (current_time < processes[i].arrival_time) {
            current_time = processes[i].arrival_time;
        }
        
        // 计算等待时间
        processes[i].waiting_time = current_time - processes[i].arrival_time;
        
        // 执行进程
        current_time += processes[i].burst_time;
        
        // 计算周转时间
        processes[i].turnaround_time = current_time - processes[i].arrival_time;
    }
}
```

**特点**：
- **优点**：实现简单，公平
- **缺点**：平均等待时间长，不适合交互式系统

### 2. 最短作业优先（SJF）

```c
// SJF调度算法实现
void sjf_scheduling(struct process processes[], int n) {
    int current_time = 0;
    int completed = 0;
    bool is_completed[n];
    
    // 初始化完成标记
    for (int i = 0; i < n; i++) {
        is_completed[i] = false;
    }
    
    while (completed < n) {
        int shortest_job = -1;
        int min_burst_time = INT_MAX;
        
        // 找到已到达且未完成的最短作业
        for (int i = 0; i < n; i++) {
            if (!is_completed[i] && 
                processes[i].arrival_time <= current_time &&
                processes[i].burst_time < min_burst_time) {
                min_burst_time = processes[i].burst_time;
                shortest_job = i;
            }
        }
        
        if (shortest_job == -1) {
            // 没有就绪进程，CPU空闲
            current_time++;
            continue;
        }
        
        // 执行最短作业
        processes[shortest_job].waiting_time = 
            current_time - processes[shortest_job].arrival_time;
        current_time += processes[shortest_job].burst_time;
        processes[shortest_job].turnaround_time = 
            current_time - processes[shortest_job].arrival_time;
        
        is_completed[shortest_job] = true;
        completed++;
    }
}
```

**特点**：
- **优点**：平均等待时间最短
- **缺点**：可能导致长作业饥饿，需要预知执行时间

### 3. 轮转调度（RR）

```c
// 轮转调度算法实现
struct queue {
    int front, rear, size;
    int capacity;
    int* array;
};

void round_robin_scheduling(struct process processes[], int n, int time_quantum) {
    struct queue* ready_queue = create_queue(n);
    int current_time = 0;
    int completed = 0;
    int remaining_time[n];
    
    // 初始化剩余时间
    for (int i = 0; i < n; i++) {
        remaining_time[i] = processes[i].burst_time;
    }
    
    // 将到达时间为0的进程加入就绪队列
    for (int i = 0; i < n; i++) {
        if (processes[i].arrival_time == 0) {
            enqueue(ready_queue, i);
        }
    }
    
    while (completed < n) {
        if (is_empty(ready_queue)) {
            current_time++;
            // 检查是否有新进程到达
            for (int i = 0; i < n; i++) {
                if (processes[i].arrival_time == current_time) {
                    enqueue(ready_queue, i);
                }
            }
            continue;
        }
        
        int current_process = dequeue(ready_queue);
        
        // 执行进程
        int execution_time = min(time_quantum, remaining_time[current_process]);
        current_time += execution_time;
        remaining_time[current_process] -= execution_time;
        
        // 检查新到达的进程
        for (int i = 0; i < n; i++) {
            if (processes[i].arrival_time <= current_time && 
                remaining_time[i] > 0 && i != current_process) {
                enqueue(ready_queue, i);
            }
        }
        
        // 如果进程未完成，重新加入队列
        if (remaining_time[current_process] > 0) {
            enqueue(ready_queue, current_process);
        } else {
            // 进程完成
            processes[current_process].turnaround_time = 
                current_time - processes[current_process].arrival_time;
            processes[current_process].waiting_time = 
                processes[current_process].turnaround_time - 
                processes[current_process].burst_time;
            completed++;
        }
    }
}
```

**特点**：
- **优点**：响应时间好，适合交互式系统
- **缺点**：上下文切换开销大

### 4. 多级反馈队列

```c
// 多级反馈队列调度
#define MAX_LEVELS 3

struct multilevel_queue {
    struct queue* queues[MAX_LEVELS];
    int time_quantum[MAX_LEVELS];
};

void multilevel_feedback_scheduling(struct process processes[], int n) {
    struct multilevel_queue* mq = create_multilevel_queue();
    
    // 设置不同级别的时间片
    mq->time_quantum[0] = 8;   // 高优先级，短时间片
    mq->time_quantum[1] = 16;  // 中优先级，中时间片
    mq->time_quantum[2] = 32;  // 低优先级，长时间片
    
    int current_time = 0;
    int completed = 0;
    int remaining_time[n];
    int current_level[n];
    
    // 初始化
    for (int i = 0; i < n; i++) {
        remaining_time[i] = processes[i].burst_time;
        current_level[i] = 0; // 所有进程从最高优先级开始
    }
    
    // 将到达的进程加入最高优先级队列
    for (int i = 0; i < n; i++) {
        if (processes[i].arrival_time == 0) {
            enqueue(mq->queues[0], i);
        }
    }
    
    while (completed < n) {
        int current_process = -1;
        int selected_level = -1;
        
        // 从高优先级队列开始查找
        for (int level = 0; level < MAX_LEVELS; level++) {
            if (!is_empty(mq->queues[level])) {
                current_process = dequeue(mq->queues[level]);
                selected_level = level;
                break;
            }
        }
        
        if (current_process == -1) {
            current_time++;
            continue;
        }
        
        // 执行进程
        int execution_time = min(mq->time_quantum[selected_level], 
                               remaining_time[current_process]);
        current_time += execution_time;
        remaining_time[current_process] -= execution_time;
        
        if (remaining_time[current_process] == 0) {
            // 进程完成
            completed++;
            processes[current_process].turnaround_time = 
                current_time - processes[current_process].arrival_time;
            processes[current_process].waiting_time = 
                processes[current_process].turnaround_time - 
                processes[current_process].burst_time;
        } else {
            // 进程未完成，降级到下一级队列
            int next_level = min(selected_level + 1, MAX_LEVELS - 1);
            enqueue(mq->queues[next_level], current_process);
        }
    }
}
```

**特点**：
- **优点**：兼顾响应时间和吞吐量，适应不同类型的进程
- **缺点**：实现复杂，参数调优困难

## 上下文切换

### 1. 上下文切换的概念

上下文切换是指CPU从一个进程切换到另一个进程时，保存当前进程的状态并恢复目标进程状态的过程。

#### 进程上下文包含：
- **CPU寄存器状态**：通用寄存器、程序计数器、状态寄存器
- **内存管理信息**：页表、段表
- **文件描述符表**
- **信号处理信息**
- **进程优先级和调度信息**

### 2. 上下文切换的实现

```c
// 简化的上下文切换实现
struct cpu_context {
    unsigned long x19;
    unsigned long x20;
    unsigned long x21;
    unsigned long x22;
    unsigned long x23;
    unsigned long x24;
    unsigned long x25;
    unsigned long x26;
    unsigned long x27;
    unsigned long x28;
    unsigned long fp;
    unsigned long sp;
    unsigned long pc;
};

// 保存当前进程上下文，切换到新进程
void context_switch(struct task_struct *prev, struct task_struct *next) {
    // 1. 保存当前进程的CPU状态
    save_cpu_context(&prev->cpu_context);
    
    // 2. 切换内存空间
    switch_mm(prev->mm, next->mm);
    
    // 3. 切换内核栈
    switch_to(prev, next);
    
    // 4. 恢复新进程的CPU状态
    restore_cpu_context(&next->cpu_context);
}

// 汇编实现的底层切换函数
__asm__ (
"switch_to:\n\t"
    "stp x19, x20, [x0, #16 * 0]\n\t"  // 保存寄存器
    "stp x21, x22, [x0, #16 * 1]\n\t"
    "stp x23, x24, [x0, #16 * 2]\n\t"
    "stp x25, x26, [x0, #16 * 3]\n\t"
    "stp x27, x28, [x0, #16 * 4]\n\t"
    "stp x29, x9,  [x0, #16 * 5]\n\t"  // x9 = sp
    "str x30,      [x0, #16 * 6]\n\t"  // x30 = lr
    
    "ldp x19, x20, [x1, #16 * 0]\n\t"  // 恢复寄存器
    "ldp x21, x22, [x1, #16 * 1]\n\t"
    "ldp x23, x24, [x1, #16 * 2]\n\t"
    "ldp x25, x26, [x1, #16 * 3]\n\t"
    "ldp x27, x28, [x1, #16 * 4]\n\t"
    "ldp x29, x9,  [x1, #16 * 5]\n\t"
    "ldr x30,      [x1, #16 * 6]\n\t"
    "mov sp, x9\n\t"
    "ret\n\t"
);
```

### 3. 上下文切换的开销

#### 直接开销
- **寄存器保存/恢复**：几十个CPU周期
- **内存管理单元切换**：刷新TLB，几百个周期
- **缓存失效**：L1/L2/L3缓存失效，数千个周期

#### 间接开销
- **缓存局部性丢失**：新进程的数据不在缓存中
- **分支预测失效**：CPU分支预测器需要重新学习
- **内存预取失效**：内存预取机制需要重新适应

### 4. 减少上下文切换开销的方法

#### 线程vs进程
```c
// 线程切换开销更小
void thread_switch(struct thread *prev, struct thread *next) {
    // 线程共享地址空间，不需要切换页表
    // 只需要切换栈和寄存器
    save_thread_context(prev);
    switch_stack(prev->stack, next->stack);
    restore_thread_context(next);
}
```

#### 协程
```c
// 协程切换开销最小
void coroutine_yield(struct coroutine *from, struct coroutine *to) {
    // 用户态切换，只保存必要的寄存器
    setjmp(from->context);
    longjmp(to->context, 1);
}
```

## 现代调度器

### 1. Linux CFS调度器

```c
// CFS调度器核心数据结构
struct cfs_rq {
    struct load_weight load;
    unsigned int nr_running;
    u64 exec_clock;
    u64 min_vruntime;
    
    struct rb_root tasks_timeline;
    struct rb_node *rb_leftmost;
    
    struct sched_entity *curr, *next, *last, *skip;
};

struct sched_entity {
    struct load_weight load;
    struct rb_node run_node;
    struct list_head group_node;
    unsigned int on_rq;
    
    u64 exec_start;
    u64 sum_exec_runtime;
    u64 vruntime;           // 虚拟运行时间
    u64 prev_sum_exec_runtime;
    
    u64 nr_migrations;
    struct sched_statistics statistics;
};

// CFS调度算法核心
static struct task_struct *pick_next_task_fair(struct rq *rq) {
    struct cfs_rq *cfs_rq = &rq->cfs;
    struct sched_entity *se;
    
    if (!cfs_rq->nr_running)
        return NULL;
    
    // 选择红黑树最左边的节点（vruntime最小）
    se = __pick_first_entity(cfs_rq);
    set_next_entity(cfs_rq, se);
    
    return task_of(se);
}
```

### 2. 实时调度

```c
// 实时调度类
struct rt_rq {
    struct rt_prio_array active;
    unsigned int rt_nr_running;
    int rt_queued;
    
    int rt_throttled;
    u64 rt_time;
    u64 rt_runtime;
    raw_spinlock_t rt_runtime_lock;
};

// FIFO调度
static struct task_struct *pick_next_task_rt(struct rq *rq) {
    struct rt_rq *rt_rq = &rq->rt;
    struct rt_prio_array *array = &rt_rq->active;
    struct task_struct *next;
    struct list_head *queue;
    int idx;
    
    idx = sched_find_first_bit(array->bitmap);
    queue = array->queue + idx;
    next = list_entry(queue->next, struct task_struct, rt.run_list);
    
    return next;
}
```

## 性能分析和优化

### 1. 调度延迟分析

```bash
# 使用perf分析调度延迟
perf sched record -- sleep 10
perf sched latency

# 分析上下文切换
perf stat -e context-switches,cpu-migrations -- ./your_program

# 查看调度事件
perf trace -e 'sched:*' -- ./your_program
```

### 2. CPU亲和性

```c
// 设置CPU亲和性
#include <sched.h>

void set_cpu_affinity(int cpu) {
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(cpu, &cpuset);
    
    if (sched_setaffinity(0, sizeof(cpuset), &cpuset) == -1) {
        perror("sched_setaffinity");
    }
}

// 获取CPU亲和性
void get_cpu_affinity() {
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    
    if (sched_getaffinity(0, sizeof(cpuset), &cpuset) == -1) {
        perror("sched_getaffinity");
        return;
    }
    
    for (int i = 0; i < CPU_SETSIZE; i++) {
        if (CPU_ISSET(i, &cpuset)) {
            printf("CPU %d is set\n", i);
        }
    }
}
```

## 面试常见问题

### 1. 调度算法比较

**问题**：比较不同调度算法的优缺点？

**答案**：
- **FCFS**：简单公平，但平均等待时间长
- **SJF**：平均等待时间最短，但可能饥饿
- **RR**：响应时间好，但上下文切换开销大
- **多级反馈**：综合性能好，但实现复杂

### 2. 上下文切换优化

**问题**：如何减少上下文切换开销？

**答案**：
- **使用线程代替进程**：共享地址空间
- **使用协程**：用户态切换
- **设置CPU亲和性**：减少缓存失效
- **批处理**：减少切换频率
- **异步I/O**：避免阻塞切换

### 3. 实时调度

**问题**：实时系统如何保证时间约束？

**答案**：
- **优先级调度**：高优先级任务优先执行
- **抢占式调度**：立即抢占低优先级任务
- **时间分析**：最坏情况执行时间分析
- **资源预留**：为实时任务预留资源

## 总结

CPU调度和上下文切换是操作系统的核心机制：

1. **调度算法**：根据不同目标选择合适的算法
2. **上下文切换**：理解开销和优化方法
3. **现代调度器**：了解CFS等先进调度器的设计
4. **性能优化**：通过各种技术减少调度开销

掌握这些概念对于系统编程和性能优化至关重要。
