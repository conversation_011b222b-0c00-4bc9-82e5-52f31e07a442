# I/O系统与设备管理

## I/O系统概述

### 1. I/O系统的层次结构

```
用户程序
    ↓
系统调用接口 (read, write, open, close)
    ↓
文件系统
    ↓
通用块层 (Generic Block Layer)
    ↓
I/O调度器 (I/O Scheduler)
    ↓
设备驱动程序 (Device Driver)
    ↓
中断处理程序 (Interrupt Handler)
    ↓
硬件设备 (Hardware Device)
```

### 2. I/O设备分类

#### 按数据传输方式分类
- **字符设备**：以字符为单位进行I/O（键盘、鼠标、串口）
- **块设备**：以块为单位进行I/O（硬盘、SSD、光盘）
- **网络设备**：网络数据传输（网卡、WiFi适配器）

#### 按访问方式分类
- **顺序访问设备**：磁带、打印机
- **随机访问设备**：硬盘、内存
- **直接访问设备**：显示器、网络接口

## I/O控制方式

### 1. 程序直接控制I/O

```c
// 轮询方式的I/O控制
void polling_io_read(char *buffer, int size) {
    int i = 0;
    
    while (i < size) {
        // 轮询设备状态寄存器
        while (!(inb(STATUS_PORT) & DATA_READY)) {
            // 忙等待，浪费CPU时间
            continue;
        }
        
        // 读取数据
        buffer[i] = inb(DATA_PORT);
        i++;
    }
}

void polling_io_write(const char *buffer, int size) {
    int i = 0;
    
    while (i < size) {
        // 轮询设备状态，等待设备就绪
        while (!(inb(STATUS_PORT) & DEVICE_READY)) {
            continue;
        }
        
        // 写入数据
        outb(DATA_PORT, buffer[i]);
        i++;
    }
}
```

**特点**：
- **优点**：实现简单，适合简单设备
- **缺点**：CPU利用率低，不适合高速设备

### 2. 中断驱动I/O

```c
// 中断驱动I/O实现
static char io_buffer[BUFFER_SIZE];
static int buffer_index = 0;
static int bytes_to_read = 0;
static volatile bool io_complete = false;

// 启动中断驱动读操作
void interrupt_driven_read(char *buffer, int size) {
    buffer_index = 0;
    bytes_to_read = size;
    io_complete = false;
    
    // 启用设备中断
    outb(CONTROL_PORT, ENABLE_INTERRUPT);
    
    // 启动I/O操作
    outb(COMMAND_PORT, READ_COMMAND);
    
    // 等待I/O完成
    while (!io_complete) {
        // CPU可以执行其他任务
        schedule();
    }
    
    // 复制数据到用户缓冲区
    memcpy(buffer, io_buffer, size);
}

// 中断处理程序
void io_interrupt_handler(void) {
    if (inb(STATUS_PORT) & DATA_READY) {
        // 读取数据
        io_buffer[buffer_index] = inb(DATA_PORT);
        buffer_index++;
        
        if (buffer_index >= bytes_to_read) {
            // I/O操作完成
            io_complete = true;
            
            // 禁用中断
            outb(CONTROL_PORT, DISABLE_INTERRUPT);
            
            // 唤醒等待的进程
            wake_up_process(waiting_process);
        }
    }
}
```

**特点**：
- **优点**：CPU利用率高，响应及时
- **缺点**：中断开销，不适合高频I/O

### 3. DMA（直接内存访问）

```c
// DMA控制器结构
struct dma_controller {
    uint32_t source_addr;      // 源地址
    uint32_t dest_addr;        // 目标地址
    uint32_t transfer_count;   // 传输字节数
    uint32_t control_reg;      // 控制寄存器
    uint32_t status_reg;       // 状态寄存器
};

// DMA传输设置
void setup_dma_transfer(struct dma_controller *dma,
                       void *src, void *dst, size_t size) {
    // 设置源地址
    dma->source_addr = (uint32_t)src;
    
    // 设置目标地址
    dma->dest_addr = (uint32_t)dst;
    
    // 设置传输大小
    dma->transfer_count = size;
    
    // 配置控制寄存器
    dma->control_reg = DMA_ENABLE | DMA_INTERRUPT_ENABLE | 
                       DMA_MEM_TO_MEM | DMA_AUTO_INIT;
}

// 启动DMA传输
void start_dma_transfer(struct dma_controller *dma) {
    // 启动DMA传输
    dma->control_reg |= DMA_START;
    
    // DMA控制器独立完成数据传输
    // CPU可以执行其他任务
}

// DMA完成中断处理
void dma_complete_interrupt(void) {
    struct dma_controller *dma = get_dma_controller();
    
    if (dma->status_reg & DMA_COMPLETE) {
        // 清除完成标志
        dma->status_reg |= DMA_COMPLETE;
        
        // 通知等待的进程
        complete_io_operation();
        
        // 可以启动下一个DMA传输
        schedule_next_dma_transfer();
    }
}
```

**特点**：
- **优点**：CPU开销最小，适合大量数据传输
- **缺点**：硬件复杂，设置开销

## I/O调度算法

### 1. 电梯调度算法（SCAN）

```c
// 电梯调度算法实现
struct io_request {
    int sector;           // 扇区号
    int direction;        // 读写方向
    void *buffer;         // 数据缓冲区
    struct list_head list; // 链表节点
};

struct elevator_scheduler {
    struct list_head queue;    // 请求队列
    int current_position;      // 当前磁头位置
    int direction;             // 当前移动方向 (1: 向外, -1: 向内)
    spinlock_t lock;          // 队列锁
};

void elevator_add_request(struct elevator_scheduler *sched,
                         struct io_request *req) {
    struct io_request *pos;
    
    spin_lock(&sched->lock);
    
    // 按扇区号排序插入
    list_for_each_entry(pos, &sched->queue, list) {
        if (req->sector < pos->sector) {
            list_add_tail(&req->list, &pos->list);
            goto out;
        }
    }
    
    // 插入到队列末尾
    list_add_tail(&req->list, &sched->queue);
    
out:
    spin_unlock(&sched->lock);
}

struct io_request *elevator_next_request(struct elevator_scheduler *sched) {
    struct io_request *req, *next_req = NULL;
    int min_distance = INT_MAX;
    
    spin_lock(&sched->lock);
    
    // 查找同方向上最近的请求
    list_for_each_entry(req, &sched->queue, list) {
        int distance = abs(req->sector - sched->current_position);
        
        // 检查是否在当前移动方向上
        if ((sched->direction > 0 && req->sector >= sched->current_position) ||
            (sched->direction < 0 && req->sector <= sched->current_position)) {
            
            if (distance < min_distance) {
                min_distance = distance;
                next_req = req;
            }
        }
    }
    
    // 如果当前方向没有请求，改变方向
    if (!next_req) {
        sched->direction = -sched->direction;
        
        // 重新查找
        list_for_each_entry(req, &sched->queue, list) {
            int distance = abs(req->sector - sched->current_position);
            
            if ((sched->direction > 0 && req->sector >= sched->current_position) ||
                (sched->direction < 0 && req->sector <= sched->current_position)) {
                
                if (distance < min_distance) {
                    min_distance = distance;
                    next_req = req;
                }
            }
        }
    }
    
    if (next_req) {
        list_del(&next_req->list);
        sched->current_position = next_req->sector;
    }
    
    spin_unlock(&sched->lock);
    return next_req;
}
```

### 2. 最短寻道时间优先（SSTF）

```c
// SSTF调度算法
struct io_request *sstf_next_request(struct list_head *queue, int current_pos) {
    struct io_request *req, *closest_req = NULL;
    int min_distance = INT_MAX;
    
    // 查找距离当前位置最近的请求
    list_for_each_entry(req, queue, list) {
        int distance = abs(req->sector - current_pos);
        
        if (distance < min_distance) {
            min_distance = distance;
            closest_req = req;
        }
    }
    
    if (closest_req) {
        list_del(&closest_req->list);
    }
    
    return closest_req;
}
```

### 3. 完全公平队列（CFQ）

```c
// CFQ调度器数据结构
struct cfq_queue {
    struct list_head fifo_list;    // FIFO队列
    struct rb_root sort_list;      // 按扇区排序的红黑树
    int allocated_slice;           // 分配的时间片
    int dispatched;               // 已调度的请求数
    int priority;                 // 优先级
    struct list_head queue_list;   // 队列链表节点
};

struct cfq_scheduler {
    struct list_head queue_list;   // 所有队列的链表
    struct cfq_queue *active_queue; // 当前活跃队列
    int time_slice;               // 当前时间片
};

struct io_request *cfq_next_request(struct cfq_scheduler *cfq) {
    struct cfq_queue *cfqq = cfq->active_queue;
    struct io_request *req = NULL;
    
    if (!cfqq || cfq->time_slice <= 0) {
        // 选择下一个队列
        cfqq = cfq_select_queue(cfq);
        cfq->active_queue = cfqq;
        cfq->time_slice = cfqq->allocated_slice;
    }
    
    if (cfqq) {
        // 从活跃队列中取出请求
        req = cfq_dispatch_request(cfqq);
        if (req) {
            cfq->time_slice--;
            cfqq->dispatched++;
        }
    }
    
    return req;
}
```

## 设备驱动程序

### 1. 设备驱动程序结构

```c
// Linux设备驱动程序框架
struct file_operations {
    struct module *owner;
    loff_t (*llseek) (struct file *, loff_t, int);
    ssize_t (*read) (struct file *, char __user *, size_t, loff_t *);
    ssize_t (*write) (struct file *, const char __user *, size_t, loff_t *);
    unsigned int (*poll) (struct file *, struct poll_table_struct *);
    long (*unlocked_ioctl) (struct file *, unsigned int, unsigned long);
    int (*mmap) (struct file *, struct vm_area_struct *);
    int (*open) (struct inode *, struct file *);
    int (*flush) (struct file *, fl_owner_t id);
    int (*release) (struct inode *, struct file *);
    int (*fsync) (struct file *, loff_t, loff_t, int datasync);
    int (*fasync) (int, struct file *, int);
    int (*lock) (struct file *, int, struct file_lock *);
};

// 字符设备驱动示例
static int device_open(struct inode *inode, struct file *file) {
    // 设备打开时的初始化
    printk(KERN_INFO "Device opened\n");
    return 0;
}

static int device_release(struct inode *inode, struct file *file) {
    // 设备关闭时的清理
    printk(KERN_INFO "Device closed\n");
    return 0;
}

static ssize_t device_read(struct file *filp, char *buffer, 
                          size_t length, loff_t *offset) {
    // 从设备读取数据
    int bytes_read = 0;
    
    // 检查是否有数据可读
    if (device_buffer_empty()) {
        return 0; // EOF
    }
    
    // 将数据从内核空间复制到用户空间
    while (length && !device_buffer_empty()) {
        put_user(get_device_byte(), buffer++);
        length--;
        bytes_read++;
    }
    
    return bytes_read;
}

static ssize_t device_write(struct file *filp, const char *buffer,
                           size_t length, loff_t *offset) {
    // 向设备写入数据
    int bytes_written = 0;
    
    // 将数据从用户空间复制到内核空间
    while (length && !device_buffer_full()) {
        char byte;
        get_user(byte, buffer++);
        put_device_byte(byte);
        length--;
        bytes_written++;
    }
    
    return bytes_written;
}

static struct file_operations fops = {
    .read = device_read,
    .write = device_write,
    .open = device_open,
    .release = device_release
};
```

### 2. 中断处理

```c
// 中断处理程序
static irqreturn_t device_interrupt_handler(int irq, void *dev_id) {
    struct device_data *dev = (struct device_data *)dev_id;
    uint32_t status;
    
    // 读取中断状态
    status = readl(dev->base_addr + STATUS_REG);
    
    if (status & RX_INTERRUPT) {
        // 处理接收中断
        handle_rx_interrupt(dev);
    }
    
    if (status & TX_INTERRUPT) {
        // 处理发送中断
        handle_tx_interrupt(dev);
    }
    
    if (status & ERROR_INTERRUPT) {
        // 处理错误中断
        handle_error_interrupt(dev);
    }
    
    // 清除中断标志
    writel(status, dev->base_addr + STATUS_REG);
    
    return IRQ_HANDLED;
}

// 注册中断处理程序
static int register_device_interrupt(struct device_data *dev) {
    int ret;
    
    ret = request_irq(dev->irq, device_interrupt_handler,
                     IRQF_SHARED, "my_device", dev);
    if (ret) {
        printk(KERN_ERR "Failed to register interrupt handler\n");
        return ret;
    }
    
    return 0;
}
```

## 缓冲和缓存

### 1. I/O缓冲

```c
// 环形缓冲区实现
struct ring_buffer {
    char *buffer;
    int size;
    int head;
    int tail;
    int count;
    spinlock_t lock;
    wait_queue_head_t read_wait;
    wait_queue_head_t write_wait;
};

int ring_buffer_write(struct ring_buffer *rb, const char *data, int len) {
    int written = 0;
    unsigned long flags;
    
    spin_lock_irqsave(&rb->lock, flags);
    
    while (len > 0 && rb->count < rb->size) {
        rb->buffer[rb->head] = *data++;
        rb->head = (rb->head + 1) % rb->size;
        rb->count++;
        written++;
        len--;
    }
    
    spin_unlock_irqrestore(&rb->lock, flags);
    
    // 唤醒等待读取的进程
    wake_up_interruptible(&rb->read_wait);
    
    return written;
}

int ring_buffer_read(struct ring_buffer *rb, char *data, int len) {
    int read = 0;
    unsigned long flags;
    
    // 等待数据可用
    wait_event_interruptible(rb->read_wait, rb->count > 0);
    
    spin_lock_irqsave(&rb->lock, flags);
    
    while (len > 0 && rb->count > 0) {
        *data++ = rb->buffer[rb->tail];
        rb->tail = (rb->tail + 1) % rb->size;
        rb->count--;
        read++;
        len--;
    }
    
    spin_unlock_irqrestore(&rb->lock, flags);
    
    // 唤醒等待写入的进程
    wake_up_interruptible(&rb->write_wait);
    
    return read;
}
```

### 2. 页缓存

```c
// 页缓存操作
struct page *find_or_create_page(struct address_space *mapping,
                                pgoff_t index, gfp_t gfp_mask) {
    struct page *page;
    
    // 首先尝试在缓存中查找
    page = find_lock_page(mapping, index);
    if (page)
        return page;
    
    // 如果没有找到，分配新页面
    page = __page_cache_alloc(gfp_mask);
    if (!page)
        return NULL;
    
    // 将页面添加到缓存
    if (add_to_page_cache_lru(page, mapping, index, gfp_mask)) {
        page_cache_release(page);
        return NULL;
    }
    
    return page;
}

// 预读实现
void page_cache_readahead(struct address_space *mapping,
                         struct file_ra_state *ra,
                         struct file *filp,
                         pgoff_t offset,
                         unsigned long req_size) {
    unsigned long max_pages;
    unsigned long start;
    unsigned long size;
    
    // 计算预读窗口
    max_pages = ra->ra_pages;
    start = offset;
    size = min(req_size * 2, max_pages);
    
    // 异步预读页面
    __do_page_cache_readahead(mapping, filp, start, size, 0);
    
    // 更新预读状态
    ra->start = start;
    ra->size = size;
    ra->async_size = size / 4;
}
```

## 性能优化

### 1. I/O合并

```c
// I/O请求合并
bool try_merge_requests(struct io_request *req1, struct io_request *req2) {
    // 检查是否可以合并
    if (req1->sector + req1->size == req2->sector &&
        req1->direction == req2->direction &&
        req1->size + req2->size <= MAX_MERGE_SIZE) {
        
        // 合并请求
        req1->size += req2->size;
        
        // 合并缓冲区
        merge_buffers(req1, req2);
        
        return true;
    }
    
    return false;
}
```

### 2. 异步I/O

```c
// 异步I/O实现
struct aio_context {
    struct list_head pending_list;
    struct work_struct work;
    spinlock_t lock;
};

int submit_aio_request(struct aio_context *ctx, struct io_request *req) {
    unsigned long flags;
    
    spin_lock_irqsave(&ctx->lock, flags);
    list_add_tail(&req->list, &ctx->pending_list);
    spin_unlock_irqrestore(&ctx->lock, flags);
    
    // 调度异步处理
    schedule_work(&ctx->work);
    
    return 0;
}

void aio_work_handler(struct work_struct *work) {
    struct aio_context *ctx = container_of(work, struct aio_context, work);
    struct io_request *req, *tmp;
    LIST_HEAD(local_list);
    
    // 获取待处理的请求
    spin_lock_irq(&ctx->lock);
    list_splice_init(&ctx->pending_list, &local_list);
    spin_unlock_irq(&ctx->lock);
    
    // 处理所有请求
    list_for_each_entry_safe(req, tmp, &local_list, list) {
        process_io_request(req);
        complete_aio_request(req);
    }
}
```

## 面试常见问题

### 1. I/O控制方式比较

**问题**：比较不同I/O控制方式的优缺点？

**答案**：
- **程序直接控制**：简单但CPU利用率低
- **中断驱动**：CPU利用率高但有中断开销
- **DMA**：CPU开销最小，适合大数据传输

### 2. I/O调度算法选择

**问题**：什么情况下选择什么I/O调度算法？

**答案**：
- **FCFS**：简单场景，公平性要求高
- **SSTF**：寻道时间敏感的应用
- **SCAN**：平衡性能和公平性
- **CFQ**：多用户环境，需要公平性

### 3. 设备驱动程序设计

**问题**：设计设备驱动程序需要考虑哪些因素？

**答案**：
- **硬件抽象**：提供统一接口
- **中断处理**：及时响应硬件事件
- **缓冲管理**：平衡性能和内存使用
- **错误处理**：处理各种异常情况
- **并发控制**：保证多进程访问安全

## 总结

I/O系统和设备管理是操作系统的重要组成部分：

1. **I/O控制方式**：从简单轮询到复杂的DMA
2. **调度算法**：平衡性能、公平性和响应时间
3. **设备驱动**：硬件和软件之间的桥梁
4. **缓存机制**：提高I/O性能的关键技术

理解这些概念对于系统编程和性能优化具有重要意义。
