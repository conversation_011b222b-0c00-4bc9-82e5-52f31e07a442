# Linux系统编程与性能调优

## 1. 系统调用核心概念

### 系统调用机制
- **定义**：用户态程序请求内核服务的接口
- **过程**：用户态 → 内核态 → 执行服务 → 返回用户态
- **开销**：上下文切换、参数验证、权限检查

### 常用系统调用
- **文件操作**：open、read、write、close、stat
- **进程管理**：fork、exec、wait、exit
- **内存管理**：mmap、munmap、brk、sbrk
- **网络通信**：socket、bind、listen、accept

### 内存映射（mmap）
- **优势**：减少数据拷贝，提高I/O性能
- **应用**：大文件处理、共享内存、零拷贝技术
- **注意**：页面对齐、同步机制

## 2. 进程管理

### 进程创建
- **fork()**：创建子进程，复制父进程
- **exec()**：替换进程映像
- **clone()**：创建轻量级进程（线程）

### 信号处理
- **常用信号**：SIGINT、SIGTERM、SIGKILL、SIGUSR1/2
- **信号处理**：默认、忽略、自定义处理函数
- **信号安全**：异步安全函数的使用

### 进程资源监控
- **rusage结构**：CPU时间、内存使用、I/O统计
- **/proc文件系统**：进程状态、内存映射、文件描述符

## 3. I/O多路复用

### I/O模型对比
- **阻塞I/O**：简单但效率低
- **非阻塞I/O**：需要轮询，CPU占用高
- **I/O多路复用**：select、poll、epoll
- **异步I/O**：真正的异步，复杂度高

### Epoll机制
- **工作原理**：基于事件通知，内核维护就绪列表
- **触发模式**：
  - 水平触发（LT）：条件满足时持续通知
  - 边缘触发（ET）：状态变化时通知一次
- **优势**：O(1)复杂度，支持大量并发连接

### 高性能服务器设计
- **Reactor模式**：事件驱动的网络编程模式
- **线程池**：处理计算密集型任务
- **连接管理**：连接池、超时处理

## 4. 零拷贝技术

### 零拷贝原理
- **传统I/O**：数据在内核空间和用户空间之间多次拷贝
- **零拷贝**：数据直接在内核空间传输，避免用户空间拷贝
- **优势**：减少CPU使用，提高I/O性能，降低内存带宽消耗

### 零拷贝实现方式
- **sendfile()**：文件到socket的直接传输
- **splice()**：管道间的数据移动
- **mmap()**：内存映射文件
- **write()/writev()**：向量化写入

### 应用场景
- **文件服务器**：静态文件传输
- **代理服务器**：数据转发
- **数据库**：大数据传输

## 5. 内存管理与优化

### 内存池技术
- **原理**：预分配固定大小的内存块，减少分配开销
- **优势**：分配释放快速，减少内存碎片
- **应用**：高频内存分配场景

### 大页内存优化
- **大页内存**：使用2MB或1GB页面替代4KB页面
- **优势**：减少TLB缺失，提高内存访问性能
- **madvise系统调用**：向内核提供内存访问模式建议

### 内存监控
- **关键指标**：Alloc、Sys、HeapAlloc、NumGC
- **工具**：pprof、runtime.MemStats、/proc/meminfo

## 6. 性能监控与调优

### 系统性能监控
- **CPU监控**：/proc/stat、top、htop
- **内存监控**：/proc/meminfo、free、vmstat
- **I/O监控**：iostat、iotop、/proc/diskstats
- **网络监控**：/proc/net/dev、iftop、netstat

### 性能调优技巧
- **CPU亲和性**：绑定进程到特定CPU核心
- **进程优先级**：调整nice值和调度策略
- **内存锁定**：防止关键内存被swap
- **文件系统优化**：使用fadvise提供访问模式建议

## 7. 故障排查与调试

### 系统调用跟踪
- **strace**：跟踪系统调用和信号
- **perf**：性能分析和调优工具
- **ftrace**：内核函数跟踪
- **tcpdump**：网络包分析

### 内存泄漏检测
- **工具**：valgrind、AddressSanitizer、pprof
- **方法**：定期监控内存使用，检测异常增长
- **Go特有**：runtime.MemStats、goroutine泄漏检测

## 8. 面试重点问题

### epoll的工作原理和优势
- **工作原理**：基于事件通知，内核维护就绪文件描述符列表
- **优势**：O(1)复杂度，支持大量并发连接，边缘触发模式
- **应用场景**：高并发网络服务器，如Nginx、Redis

### 零拷贝技术
- **定义**：数据在内核空间和用户空间之间传输时避免不必要的拷贝
- **实现方式**：sendfile、splice、mmap
- **优势**：减少CPU使用，提高I/O性能，降低内存带宽消耗

### Linux系统性能调优
1. **CPU调优**：设置CPU亲和性，调整调度策略
2. **内存调优**：使用大页内存，优化内存分配
3. **I/O调优**：使用异步I/O，优化文件系统参数
4. **网络调优**：调整TCP参数，使用高性能网络模型

### 常用性能监控工具
- **系统级**：top、htop、iotop、iftop、vmstat、iostat
- **进程级**：ps、pstree、lsof、netstat、ss
- **性能分析**：perf、strace、tcpdump、wireshark
- **内存分析**：valgrind、pprof、/proc/meminfo
