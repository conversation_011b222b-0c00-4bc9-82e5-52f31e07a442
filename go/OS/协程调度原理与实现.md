# 协程调度原理与实现

## 协程调度基础概念

### 用户态调度 vs 内核态调度

#### 内核态调度（传统线程）
- **调度器位置**：操作系统内核
- **调度时机**：时间片到期、系统调用、中断
- **上下文切换**：需要从用户态切换到内核态
- **开销**：大（需要保存完整的CPU状态）

#### 用户态调度（协程）
- **调度器位置**：用户程序内部
- **调度时机**：主动让出、I/O阻塞、channel操作
- **上下文切换**：在用户态完成
- **开销**：小（只需保存必要的寄存器）

```go
// 协程调度示例
package main

import (
    "fmt"
    "runtime"
    "time"
)

func main() {
    // 查看调度器信息
    fmt.Printf("GOMAXPROCS: %d\n", runtime.GOMAXPROCS(0))
    fmt.Printf("NumCPU: %d\n", runtime.NumCPU())
    
    // 创建协程观察调度
    for i := 0; i < 5; i++ {
        go func(id int) {
            for j := 0; j < 3; j++ {
                fmt.Printf("协程 %d 执行第 %d 次\n", id, j+1)
                
                // 主动让出CPU
                runtime.Gosched()
                
                // 模拟一些工作
                time.Sleep(100 * time.Millisecond)
            }
        }(i)
    }
    
    time.Sleep(2 * time.Second)
}
```

## Go语言的GPM调度模型

### GPM模型组件

#### G (Goroutine)
```go
// Goroutine的简化结构
type g struct {
    stack       stack   // 协程栈
    stackguard0 uintptr // 栈溢出检查
    m           *m      // 当前运行的M
    sched       gobuf   // 调度信息
    atomicstatus uint32 // 协程状态
    goid        int64   // 协程ID
}

// 协程状态
const (
    _Gidle = iota    // 刚分配，未初始化
    _Grunnable      // 可运行，在运行队列中
    _Grunning       // 正在运行
    _Gsyscall       // 系统调用中
    _Gwaiting       // 等待中（如channel操作）
    _Gdead          // 已结束
)
```

#### P (Processor)
```go
// Processor的简化结构
type p struct {
    id          int32
    status      uint32    // P的状态
    runqhead    uint32    // 本地运行队列头
    runqtail    uint32    // 本地运行队列尾
    runq        [256]guintptr // 本地运行队列
    runnext     guintptr  // 下一个要运行的G
    m           muintptr  // 关联的M
}
```

#### M (Machine)
```go
// Machine的简化结构
type m struct {
    g0      *g        // 调度协程
    curg    *g        // 当前运行的协程
    p       puintptr  // 关联的P
    nextp   puintptr  // 下一个P
    id      int64     // 线程ID
    spinning bool     // 是否在自旋
}
```

### 调度流程详解

```go
// 模拟GPM调度流程
package main

import (
    "fmt"
    "runtime"
    "sync"
    "time"
)

func demonstrateGPMScheduling() {
    // 设置P的数量
    runtime.GOMAXPROCS(2)
    
    var wg sync.WaitGroup
    
    // 创建CPU密集型任务
    for i := 0; i < 4; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            
            for j := 0; j < 5; j++ {
                fmt.Printf("协程 %d 在 P%d 上执行\n", id, getCurrentP())
                
                // CPU密集型工作
                for k := 0; k < 1000000; k++ {
                    _ = k * k
                }
                
                // 检查是否需要让出
                if j%2 == 0 {
                    runtime.Gosched()
                }
            }
        }(i)
    }
    
    wg.Wait()
}

func getCurrentP() int {
    // 这是一个简化的示例，实际无法直接获取P的ID
    return runtime.GOMAXPROCS(0) % 2
}
```

## 协程调度策略

### 1. 工作窃取（Work Stealing）

```go
// 工作窃取算法示例
package main

import (
    "fmt"
    "math/rand"
    "runtime"
    "sync"
    "time"
)

type WorkStealingScheduler struct {
    queues []chan func()
    workers int
}

func NewWorkStealingScheduler(workers int) *WorkStealingScheduler {
    queues := make([]chan func(), workers)
    for i := range queues {
        queues[i] = make(chan func(), 100)
    }
    
    return &WorkStealingScheduler{
        queues: queues,
        workers: workers,
    }
}

func (ws *WorkStealingScheduler) Start() {
    for i := 0; i < ws.workers; i++ {
        go ws.worker(i)
    }
}

func (ws *WorkStealingScheduler) worker(id int) {
    for {
        select {
        case task := <-ws.queues[id]:
            // 执行本地队列的任务
            task()
        default:
            // 本地队列为空，尝试窃取其他队列的任务
            if task := ws.steal(id); task != nil {
                task()
            } else {
                // 没有任务，短暂休眠
                time.Sleep(time.Millisecond)
            }
        }
    }
}

func (ws *WorkStealingScheduler) steal(workerID int) func() {
    // 随机选择一个其他worker的队列进行窃取
    for i := 0; i < ws.workers; i++ {
        if i == workerID {
            continue
        }
        
        select {
        case task := <-ws.queues[i]:
            fmt.Printf("Worker %d 从 Worker %d 窃取任务\n", workerID, i)
            return task
        default:
            continue
        }
    }
    return nil
}

func (ws *WorkStealingScheduler) Submit(task func()) {
    // 随机选择一个队列提交任务
    queueID := rand.Intn(ws.workers)
    select {
    case ws.queues[queueID] <- task:
    default:
        // 队列满了，尝试其他队列
        for i := 0; i < ws.workers; i++ {
            select {
            case ws.queues[i] <- task:
                return
            default:
                continue
            }
        }
    }
}
```

### 2. 抢占式调度

```go
// 抢占式调度示例
package main

import (
    "fmt"
    "runtime"
    "sync"
    "time"
)

func demonstratePreemptiveScheduling() {
    runtime.GOMAXPROCS(1) // 使用单个P来观察抢占
    
    var wg sync.WaitGroup
    
    // 创建一个长时间运行的协程
    wg.Add(1)
    go func() {
        defer wg.Done()
        start := time.Now()
        
        // 长时间的CPU密集型任务
        for i := 0; i < 1000000000; i++ {
            _ = i * i
            
            // 每隔一段时间检查是否被抢占
            if i%100000000 == 0 {
                elapsed := time.Since(start)
                fmt.Printf("长任务运行了 %v\n", elapsed)
            }
        }
        
        fmt.Printf("长任务完成，总耗时: %v\n", time.Since(start))
    }()
    
    // 创建一些短任务
    for i := 0; i < 5; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            fmt.Printf("短任务 %d 开始执行\n", id)
            time.Sleep(100 * time.Millisecond)
            fmt.Printf("短任务 %d 完成\n", id)
        }(i)
    }
    
    wg.Wait()
}
```

## 协程状态转换

```go
// 协程状态转换示例
package main

import (
    "fmt"
    "runtime"
    "time"
)

func goroutineStateTransition() {
    fmt.Printf("初始协程数: %d\n", runtime.NumGoroutine())
    
    // 创建一个会阻塞的协程
    ch := make(chan int)
    
    go func() {
        fmt.Println("协程开始运行 (Grunning)")
        
        fmt.Println("协程等待channel (Gwaiting)")
        value := <-ch
        
        fmt.Printf("协程接收到值: %d (Grunning)\n", value)
        fmt.Println("协程即将结束 (Gdead)")
    }()
    
    fmt.Printf("创建协程后数量: %d\n", runtime.NumGoroutine())
    
    // 让协程等待一段时间
    time.Sleep(time.Second)
    fmt.Println("主协程发送值到channel")
    ch <- 42
    
    // 等待协程结束
    time.Sleep(100 * time.Millisecond)
    fmt.Printf("最终协程数: %d\n", runtime.NumGoroutine())
}
```

## 协程栈管理

### 栈的动态增长

```go
// 协程栈增长示例
package main

import (
    "fmt"
    "runtime"
)

func demonstrateStackGrowth() {
    var m1, m2 runtime.MemStats
    
    runtime.ReadMemStats(&m1)
    fmt.Printf("初始栈内存: %d KB\n", m1.StackInuse/1024)
    
    // 创建深度递归的协程
    go func() {
        deepRecursion(0, 1000)
    }()
    
    runtime.GC()
    runtime.ReadMemStats(&m2)
    fmt.Printf("递归后栈内存: %d KB\n", m2.StackInuse/1024)
    fmt.Printf("栈内存增长: %d KB\n", (m2.StackInuse-m1.StackInuse)/1024)
}

func deepRecursion(current, max int) {
    if current >= max {
        return
    }
    
    // 分配一些栈空间
    var buffer [1024]byte
    _ = buffer
    
    if current%100 == 0 {
        fmt.Printf("递归深度: %d\n", current)
    }
    
    deepRecursion(current+1, max)
}
```

### 栈分裂和合并

```go
// 栈分裂示例
package main

import (
    "fmt"
    "runtime"
)

func stackSplitExample() {
    // 获取初始栈信息
    var stack [8]uintptr
    n := runtime.Stack(stack[:], false)
    fmt.Printf("初始栈大小: %d\n", n)
    
    // 创建需要大栈的协程
    go func() {
        largeStackFunction()
    }()
    
    runtime.GC()
    runtime.Gosched()
}

func largeStackFunction() {
    // 分配大量栈空间
    var largeArray [10000]int
    
    for i := range largeArray {
        largeArray[i] = i
    }
    
    fmt.Printf("大栈函数执行完成\n")
}
```

## 调度器优化技术

### 1. 本地队列优化

```go
// 本地队列优化示例
package main

import (
    "fmt"
    "runtime"
    "sync"
    "time"
)

func localQueueOptimization() {
    runtime.GOMAXPROCS(2)
    
    var wg sync.WaitGroup
    
    // 创建大量短任务
    for i := 0; i < 1000; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            
            // 短任务
            for j := 0; j < 100; j++ {
                _ = j * j
            }
            
            if id%100 == 0 {
                fmt.Printf("任务 %d 完成\n", id)
            }
        }(i)
    }
    
    start := time.Now()
    wg.Wait()
    fmt.Printf("1000个短任务完成，耗时: %v\n", time.Since(start))
}
```

### 2. 系统调用优化

```go
// 系统调用优化示例
package main

import (
    "fmt"
    "os"
    "runtime"
    "sync"
    "time"
)

func syscallOptimization() {
    var wg sync.WaitGroup
    
    // 创建进行系统调用的协程
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            
            // 模拟系统调用
            file, err := os.Open("/dev/null")
            if err != nil {
                fmt.Printf("协程 %d 打开文件失败\n", id)
                return
            }
            defer file.Close()
            
            // 读取操作（系统调用）
            buffer := make([]byte, 1024)
            _, err = file.Read(buffer)
            if err != nil {
                fmt.Printf("协程 %d 读取文件失败\n", id)
                return
            }
            
            fmt.Printf("协程 %d 完成系统调用\n", id)
        }(i)
    }
    
    // 创建CPU密集型协程
    for i := 0; i < 5; i++ {
        wg.Add(1)
        go func(id int) {
            defer wg.Done()
            
            for j := 0; j < 1000000; j++ {
                _ = j * j
            }
            
            fmt.Printf("CPU密集型协程 %d 完成\n", id)
        }(i)
    }
    
    wg.Wait()
}
```

## 面试常见问题

### Q1: Go的协程调度器是如何工作的？

**答案**：
1. **GPM模型**：G(协程)、P(处理器)、M(系统线程)
2. **本地队列**：每个P维护本地运行队列
3. **全局队列**：所有P共享的全局运行队列
4. **工作窃取**：空闲P从其他P窃取任务
5. **抢占式调度**：防止协程长时间占用CPU

### Q2: 协程什么时候会被调度？

**答案**：
1. **主动让出**：调用runtime.Gosched()
2. **系统调用**：进行阻塞系统调用
3. **Channel操作**：channel发送/接收阻塞
4. **时间片到期**：长时间运行被抢占
5. **垃圾回收**：GC期间的调度

### Q3: 如何避免协程泄漏？

**答案**：
1. **正确使用context**：设置超时和取消
2. **关闭channel**：确保协程能正常退出
3. **避免无限循环**：设置退出条件
4. **监控协程数量**：使用runtime.NumGoroutine()
5. **使用sync.WaitGroup**：等待协程完成

### Q4: 协程栈是如何管理的？

**答案**：
1. **初始大小**：2KB起始栈
2. **动态增长**：按需分配更多栈空间
3. **栈分裂**：大栈分裂成多个段
4. **栈收缩**：不使用时释放栈空间
5. **栈拷贝**：必要时拷贝整个栈

## 最佳实践

1. **合理设置GOMAXPROCS**：通常等于CPU核心数
2. **避免长时间CPU密集型任务**：适当调用runtime.Gosched()
3. **正确处理系统调用**：使用非阻塞I/O
4. **监控协程状态**：定期检查协程数量和状态
5. **优化热点路径**：减少不必要的协程创建
