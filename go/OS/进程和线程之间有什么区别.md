# 进程和线程的区别

## 1. **基本定义**

### **进程（Process）**
- **定义**：操作系统分配资源和调度任务的基本单位
- **特征**：拥有独立的内存空间和系统资源
- **组成**：代码段、数据段、堆、栈、PCB（进程控制块）

### **线程（Thread）**
- **定义**：进程中的执行单元，CPU调度的基本单位
- **特征**：共享进程资源，轻量级
- **组成**：程序计数器、寄存器集合、栈空间、TCB（线程控制块）

## 2. **内存空间对比**

### **进程内存布局**
```
高地址
┌─────────────┐
│    内核空间   │
├─────────────┤
│     栈      │ (局部变量、函数参数)
├─────────────┤
│     堆      │ (动态分配内存)
├─────────────┤
│   数据段     │ (全局变量、静态变量)
├─────────────┤
│   代码段     │ (程序代码)
└─────────────┘
低地址
```

### **线程内存共享**
- **共享**：代码段、数据段、堆、文件描述符
- **独立**：栈空间、程序计数器、寄存器

## 3. **资源分配对比**

| 资源类型 | 进程 | 线程 |
|---------|------|------|
| 内存空间 | 独立的虚拟地址空间 | 共享进程地址空间 |
| 文件描述符 | 独立 | 共享 |
| 信号处理 | 独立 | 共享 |
| 程序计数器 | 独立 | 独立 |
| 寄存器 | 独立 | 独立 |
| 栈空间 | 独立 | 独立 |

## 4. **创建开销对比**

- **进程创建**：需要分配独立内存空间、复制父进程资源，开销大
- **线程创建**：共享进程资源，只需分配栈空间，开销小

## 5. **通信机制对比**

### **进程间通信（IPC）**
- **管道**：匿名管道、命名管道
- **消息队列**：异步通信
- **共享内存**：高效但需要同步
- **信号量**：同步原语
- **套接字**：网络通信

### **线程间通信**
- **共享变量**：直接访问共享内存
- **互斥锁**：保证数据一致性
- **条件变量**：线程同步
- **信号量**：资源计数
- **Channel**：Go语言特有的通信方式

## 6. **上下文切换开销**

### **进程上下文切换**
- 保存/恢复所有寄存器
- 切换内存映射和页表
- 刷新TLB缓存
- 开销：微秒级别

### **线程上下文切换**
- 保存/恢复部分寄存器
- 不需要切换内存映射
- 开销：纳秒级别

## 7. **故障隔离性**

### **进程故障隔离**
- 进程崩溃不影响其他进程
- 提供更好的稳定性
- 适合关键系统

### **线程故障传播**
- 线程崩溃可能影响整个进程
- 需要异常处理机制
- 风险相对较高

## 8. **使用场景选择**

### **选择进程的场景**
- 需要强隔离性的应用
- 不同功能模块独立运行
- 容错性要求高的系统
- 微服务架构

### **选择线程的场景**
- 需要频繁数据共享
- 高并发处理
- 实时性要求高
- 计算密集型任务

## 9. **面试要点总结**

### **核心区别**
1. **资源分配**：进程独立，线程共享
2. **创建开销**：进程大，线程小
3. **通信方式**：进程需IPC，线程直接共享
4. **故障隔离**：进程隔离，线程共享风险
5. **调度单位**：进程是资源分配单位，线程是调度单位

### **性能对比**
- **内存使用**：进程 > 线程
- **创建时间**：进程 > 线程
- **切换开销**：进程 > 线程
- **通信效率**：进程 < 线程

### **一句话总结**
> 进程提供隔离性和稳定性，线程提供效率和共享性，选择取决于应用需求的权衡