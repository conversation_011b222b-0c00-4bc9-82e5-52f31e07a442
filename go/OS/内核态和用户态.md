### 内核态和用户态

在操作系统中，程序运行时的状态可以分为两种：**内核态**（Kernel Mode）和**用户态**（User Mode）。

- **内核态**：内核态是指操作系统内核执行程序的状态。在内核态下，程序可以访问硬件资源，如内存、硬盘等，也可以执行所有CPU指令。操作系统的内核（包括设备驱动程序、文件系统等）通常运行在内核态。

- **用户态**：用户态是指普通应用程序运行时的状态。在用户态下，程序只能执行有限的指令集，无法直接访问硬件资源。所有对硬件资源的访问都必须通过系统调用，由操作系统内核代表应用程序执行相应操作。

用户态的程序不能直接访问或操作硬件资源，这是为了保证系统的安全性和稳定性。只有操作系统内核能够直接操作硬件，以防止应用程序因错误或恶意行为而影响整个系统的稳定性。

### 内核态与用户态的切换

**内核态与用户态的切换**通常发生在以下几种情况下：

1. **系统调用**：当用户态的应用程序需要访问硬件资源时，会通过系统调用进入内核态，由操作系统内核执行相应操作，然后返回用户态。
   
2. **中断**：当硬件设备发出中断请求（如键盘输入、硬盘数据读写完成等）时，CPU会切换到内核态来处理中断。处理完成后，系统会根据具体情况恢复到用户态继续执行程序。

3. **异常处理**：当应用程序发生异常（如除零错误、缺页错误等）时，CPU会切换到内核态，由操作系统内核处理异常。

**切换过程**大致如下：

1. **用户态 -> 内核态**：当需要进行系统调用或处理中断时，CPU将程序当前的上下文（如程序计数器、寄存器值等）保存在内核栈中，然后切换到内核态执行相关的操作。

2. **内核态 -> 用户态**：当系统调用或中断处理完成后，操作系统将保存的用户态上下文恢复，并切换回用户态继续执行程序。

### 程序在开始运行时，内核态和用户态都发生了什么？

当一个程序开始运行时，经历了从**内核态**到**用户态**的切换过程，主要涉及以下步骤：

1. **进程创建**（内核态）：当用户启动一个程序时，操作系统内核会创建一个新的进程。这个过程涉及分配进程控制块（PCB）、分配内存空间、设置程序的初始状态等操作。

2. **加载可执行文件**（内核态）：操作系统内核将程序的可执行文件从磁盘加载到内存中。这个过程包括解析可执行文件的格式，分配内存空间并将文件内容加载到内存中相应的位置。

3. **设置用户态的初始环境**（内核态）：操作系统内核为即将进入用户态的程序设置好初始的栈指针、程序计数器（指向程序的入口点），以及必要的寄存器值。

4. **切换到用户态**：当所有的初始化工作完成后，操作系统将CPU的控制权交给程序，切换到用户态开始执行程序的指令。

5. **程序运行**（用户态）：程序在用户态下开始执行。此时，程序无法直接访问硬件资源，所有的硬件访问请求（如文件读写、网络通信等）都通过系统调用由操作系统内核代理执行。

整个过程展示了操作系统如何管理程序的执行，确保系统的安全性和稳定性。