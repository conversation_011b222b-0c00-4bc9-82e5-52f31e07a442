#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端常见场景算法
包含限流器、一致性哈希、LRU缓存、布隆过滤器、分布式锁、断路器等
"""

import time
import uuid
import math
import hashlib
import bisect
from collections import deque
from enum import Enum


class BackendScenarios:
    """后端常见场景算法"""

    @staticmethod
    def rate_limiter_sliding_window(window_size: int, max_requests: int):
        """
        滑动窗口限流器
        应用场景: API限流、防刷

        原理: 维护一个时间窗口，统计窗口内的请求数量
        """
        class SlidingWindowRateLimiter:
            def __init__(self):
                self.requests = deque()

            def is_allowed(self) -> bool:
                now = time.time()

                # 移除过期的请求
                while self.requests and self.requests[0] <= now - window_size:
                    self.requests.popleft()

                # 检查是否超过限制
                if len(self.requests) < max_requests:
                    self.requests.append(now)
                    return True

                return False

        return SlidingWindowRateLimiter()

    @staticmethod
    def consistent_hashing():
        """
        一致性哈希算法
        应用场景: 分布式缓存、负载均衡

        原理: 将服务器和数据都映射到哈希环上，数据存储到顺时针最近的服务器
        """
        class ConsistentHash:
            def __init__(self, replicas=3):
                self.replicas = replicas
                self.ring = {}
                self.sorted_keys = []

            def _hash(self, key: str) -> int:
                return int(hashlib.md5(key.encode()).hexdigest(), 16)

            def add_server(self, server: str):
                for i in range(self.replicas):
                    key = self._hash(f"{server}:{i}")
                    self.ring[key] = server
                    bisect.insort(self.sorted_keys, key)

            def remove_server(self, server: str):
                for i in range(self.replicas):
                    key = self._hash(f"{server}:{i}")
                    if key in self.ring:
                        del self.ring[key]
                        self.sorted_keys.remove(key)

            def get_server(self, data_key: str) -> str:
                if not self.ring:
                    return None

                key = self._hash(data_key)
                idx = bisect.bisect_right(self.sorted_keys, key)

                if idx == len(self.sorted_keys):
                    idx = 0

                return self.ring[self.sorted_keys[idx]]

        return ConsistentHash()

    @staticmethod
    def lru_cache(capacity: int):
        """
        LRU缓存实现
        应用场景: 内存缓存、页面置换

        原理: 双向链表 + 哈希表，O(1)时间复杂度
        """
        class Node:
            def __init__(self, key=0, value=0):
                self.key = key
                self.value = value
                self.prev = None
                self.next = None

        class LRUCache:
            def __init__(self):
                self.capacity = capacity
                self.cache = {}
                # 创建虚拟头尾节点
                self.head = Node()
                self.tail = Node()
                self.head.next = self.tail
                self.tail.prev = self.head

            def _add_node(self, node):
                """在头部添加节点"""
                node.prev = self.head
                node.next = self.head.next
                self.head.next.prev = node
                self.head.next = node

            def _remove_node(self, node):
                """移除节点"""
                prev_node = node.prev
                next_node = node.next
                prev_node.next = next_node
                next_node.prev = prev_node

            def _move_to_head(self, node):
                """移动节点到头部"""
                self._remove_node(node)
                self._add_node(node)

            def _pop_tail(self):
                """弹出尾部节点"""
                last_node = self.tail.prev
                self._remove_node(last_node)
                return last_node

            def get(self, key: int) -> int:
                node = self.cache.get(key)
                if node:
                    self._move_to_head(node)
                    return node.value
                return -1

            def put(self, key: int, value: int):
                node = self.cache.get(key)

                if node:
                    node.value = value
                    self._move_to_head(node)
                else:
                    new_node = Node(key, value)

                    if len(self.cache) >= self.capacity:
                        tail = self._pop_tail()
                        del self.cache[tail.key]

                    self.cache[key] = new_node
                    self._add_node(new_node)

        return LRUCache()

    @staticmethod
    def bloom_filter(capacity: int, error_rate: float = 0.01):
        """
        布隆过滤器实现
        应用场景: 去重、缓存穿透防护

        原理: 位数组 + 多个哈希函数，允许假阳性但不允许假阴性
        """
        class BloomFilter:
            def __init__(self):
                # 计算最优参数
                self.size = int(-capacity * math.log(error_rate) / (math.log(2) ** 2))
                self.hash_count = int(self.size * math.log(2) / capacity)
                self.bit_array = [0] * self.size

            def _hash(self, item: str, seed: int) -> int:
                hash_obj = hashlib.md5(f"{item}{seed}".encode())
                return int(hash_obj.hexdigest(), 16) % self.size

            def add(self, item: str):
                for i in range(self.hash_count):
                    index = self._hash(item, i)
                    self.bit_array[index] = 1

            def contains(self, item: str) -> bool:
                for i in range(self.hash_count):
                    index = self._hash(item, i)
                    if self.bit_array[index] == 0:
                        return False
                return True

        return BloomFilter()

    @staticmethod
    def distributed_lock_redis():
        """
        基于Redis的分布式锁
        应用场景: 分布式系统中的互斥操作

        原理: SET key value NX EX timeout
        """
        class RedisDistributedLock:
            def __init__(self, redis_client, key: str, timeout: int = 10):
                self.redis = redis_client
                self.key = key
                self.timeout = timeout
                self.identifier = str(uuid.uuid4())

            def acquire(self) -> bool:
                """获取锁"""
                # 模拟Redis SET命令: SET key value NX EX timeout
                # 实际使用时需要Redis客户端
                return True  # 简化实现

            def release(self) -> bool:
                """释放锁"""
                # Lua脚本确保原子性，实际使用时执行Lua脚本
                # if redis.call("get", KEYS[1]) == ARGV[1] then
                #     return redis.call("del", KEYS[1])
                # else
                #     return 0
                # end
                return True  # 简化实现

        return RedisDistributedLock

    @staticmethod
    def circuit_breaker(failure_threshold: int = 5, timeout: int = 60):
        """
        断路器模式
        应用场景: 微服务容错、防止雪崩

        原理: 统计失败次数，超过阈值时熔断，定时尝试恢复
        """
        class CircuitState(Enum):
            CLOSED = "CLOSED"
            OPEN = "OPEN"
            HALF_OPEN = "HALF_OPEN"

        class CircuitBreaker:
            def __init__(self):
                self.failure_count = 0
                self.last_failure_time = None
                self.state = CircuitState.CLOSED

            def call(self, func, *args, **kwargs):
                if self.state == CircuitState.OPEN:
                    if time.time() - self.last_failure_time > timeout:
                        self.state = CircuitState.HALF_OPEN
                    else:
                        raise Exception("Circuit breaker is OPEN")

                try:
                    result = func(*args, **kwargs)
                    self._on_success()
                    return result
                except Exception as e:
                    self._on_failure()
                    raise e

            def _on_success(self):
                self.failure_count = 0
                self.state = CircuitState.CLOSED

            def _on_failure(self):
                self.failure_count += 1
                self.last_failure_time = time.time()

                if self.failure_count >= failure_threshold:
                    self.state = CircuitState.OPEN

        return CircuitBreaker()
