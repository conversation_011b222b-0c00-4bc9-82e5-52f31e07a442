#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
常见基础算法实现集合
包含排序算法、搜索算法、数据结构等常见面试题算法
作者: 面试准备
日期: 2024
"""

from .sorting import SortingAlgorithms
from .searching import SearchAlgorithms
from .graph import GraphAlgorithms
from .string_algorithms import StringAlgorithms
from .math_algorithms import MathAlgorithms
from .data_structures import DataStructures
from .backtracking import BacktrackingAlgorithms
from .dynamic_programming import DynamicProgramming
from .backend_scenarios import BackendScenarios

__all__ = [
    'SortingAlgorithms',
    'SearchAlgorithms', 
    'GraphAlgorithms',
    'StringAlgorithms',
    'MathAlgorithms',
    'DataStructures',
    'BacktrackingAlgorithms',
    'DynamicProgramming',
    'BackendScenarios'
]

__version__ = '1.0.0'
__author__ = '面试准备'
