#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序算法集合
包含常见的排序算法实现：冒泡排序、快速排序、归并排序、堆排序等
"""

from typing import List


class SortingAlgorithms:
    """排序算法集合"""
    
    @staticmethod
    def bubble_sort(arr: List[int]) -> List[int]:
        """
        冒泡排序
        时间复杂度: O(n²)
        空间复杂度: O(1)
        稳定性: 稳定
        
        原理: 重复遍历数组，比较相邻元素，如果顺序错误就交换
        """
        n = len(arr)
        arr = arr.copy()  # 不修改原数组
        
        for i in range(n):
            # 标记本轮是否有交换，优化算法
            swapped = False
            
            # 每轮将最大元素"冒泡"到末尾
            for j in range(0, n - i - 1):
                if arr[j] > arr[j + 1]:
                    arr[j], arr[j + 1] = arr[j + 1], arr[j]
                    swapped = True
            
            # 如果没有交换，说明已经有序
            if not swapped:
                break
                
        return arr
    
    @staticmethod
    def quick_sort(arr: List[int]) -> List[int]:
        """
        快速排序
        时间复杂度: 平均O(n log n), 最坏O(n²)
        空间复杂度: O(log n)
        稳定性: 不稳定
        
        原理: 分治法，选择基准元素，将数组分为小于和大于基准的两部分
        """
        if len(arr) <= 1:
            return arr
        
        # 选择中间元素作为基准（避免最坏情况）
        pivot = arr[len(arr) // 2]
        
        # 分割数组
        left = [x for x in arr if x < pivot]
        middle = [x for x in arr if x == pivot]
        right = [x for x in arr if x > pivot]
        
        # 递归排序并合并
        return SortingAlgorithms.quick_sort(left) + middle + SortingAlgorithms.quick_sort(right)
    
    @staticmethod
    def merge_sort(arr: List[int]) -> List[int]:
        """
        归并排序
        时间复杂度: O(n log n)
        空间复杂度: O(n)
        稳定性: 稳定
        
        原理: 分治法，将数组分成两半，递归排序后合并
        """
        if len(arr) <= 1:
            return arr
        
        # 分割数组
        mid = len(arr) // 2
        left = SortingAlgorithms.merge_sort(arr[:mid])
        right = SortingAlgorithms.merge_sort(arr[mid:])
        
        # 合并两个有序数组
        return SortingAlgorithms._merge(left, right)
    
    @staticmethod
    def _merge(left: List[int], right: List[int]) -> List[int]:
        """合并两个有序数组"""
        result = []
        i = j = 0
        
        # 比较两个数组的元素，将较小的加入结果
        while i < len(left) and j < len(right):
            if left[i] <= right[j]:
                result.append(left[i])
                i += 1
            else:
                result.append(right[j])
                j += 1
        
        # 添加剩余元素
        result.extend(left[i:])
        result.extend(right[j:])
        
        return result
    
    @staticmethod
    def heap_sort(arr: List[int]) -> List[int]:
        """
        堆排序
        时间复杂度: O(n log n)
        空间复杂度: O(1)
        稳定性: 不稳定
        
        原理: 构建最大堆，然后依次取出堆顶元素
        """
        arr = arr.copy()
        n = len(arr)
        
        # 构建最大堆
        for i in range(n // 2 - 1, -1, -1):
            SortingAlgorithms._heapify(arr, n, i)
        
        # 依次取出堆顶元素
        for i in range(n - 1, 0, -1):
            arr[0], arr[i] = arr[i], arr[0]  # 将堆顶移到末尾
            SortingAlgorithms._heapify(arr, i, 0)  # 重新调整堆
        
        return arr
    
    @staticmethod
    def _heapify(arr: List[int], n: int, i: int):
        """调整堆结构"""
        largest = i  # 假设父节点最大
        left = 2 * i + 1  # 左子节点
        right = 2 * i + 2  # 右子节点
        
        # 找出最大值的索引
        if left < n and arr[left] > arr[largest]:
            largest = left
        
        if right < n and arr[right] > arr[largest]:
            largest = right
        
        # 如果最大值不是父节点，交换并继续调整
        if largest != i:
            arr[i], arr[largest] = arr[largest], arr[i]
            SortingAlgorithms._heapify(arr, n, largest)
