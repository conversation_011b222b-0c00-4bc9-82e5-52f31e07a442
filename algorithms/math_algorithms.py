#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数学算法集合
包含最大公约数、最小公倍数、素数判断、快速幂等数学算法
"""

from typing import List


class MathAlgorithms:
    """数学算法集合"""

    @staticmethod
    def gcd(a: int, b: int) -> int:
        """
        最大公约数 - 欧几里得算法
        时间复杂度: O(log min(a, b))
        空间复杂度: O(1)

        原理: gcd(a, b) = gcd(b, a % b)
        """
        while b:
            a, b = b, a % b
        return a

    @staticmethod
    def lcm(a: int, b: int) -> int:
        """
        最小公倍数
        原理: lcm(a, b) = a * b / gcd(a, b)
        """
        return abs(a * b) // MathAlgorithms.gcd(a, b)

    @staticmethod
    def is_prime(n: int) -> bool:
        """
        素数判断
        时间复杂度: O(√n)
        空间复杂度: O(1)

        原理: 只需检查到√n
        """
        if n < 2:
            return False
        if n == 2:
            return True
        if n % 2 == 0:
            return False

        for i in range(3, int(n**0.5) + 1, 2):
            if n % i == 0:
                return False
        return True

    @staticmethod
    def sieve_of_eratosthenes(n: int) -> List[int]:
        """
        埃拉托斯特尼筛法 - 找出所有小于n的素数
        时间复杂度: O(n log log n)
        空间复杂度: O(n)

        原理: 标记合数，剩下的就是素数
        """
        if n < 2:
            return []

        is_prime = [True] * n
        is_prime[0] = is_prime[1] = False

        for i in range(2, int(n**0.5) + 1):
            if is_prime[i]:
                for j in range(i*i, n, i):
                    is_prime[j] = False

        return [i for i in range(n) if is_prime[i]]

    @staticmethod
    def fast_power(base: int, exp: int, mod: int = None) -> int:
        """
        快速幂算法
        时间复杂度: O(log exp)
        空间复杂度: O(1)

        原理: 二进制分解指数
        """
        result = 1
        base = base % mod if mod else base

        while exp > 0:
            if exp % 2 == 1:
                result = (result * base) % mod if mod else result * base
            exp = exp >> 1
            base = (base * base) % mod if mod else base * base

        return result
