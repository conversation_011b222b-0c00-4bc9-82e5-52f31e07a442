#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法演示和测试文件
展示各种算法的使用方法和性能测试
"""

import random
import time
from . import (
    SortingAlgorithms, SearchAlgorithms, GraphAlgorithms,
    StringAlgorithms, MathAlgorithms, DataStructures,
    BacktrackingAlgorithms, DynamicProgramming, BackendScenarios
)


def performance_test():
    """性能测试函数"""
    print("=== 算法性能测试 ===")

    # 生成测试数据
    test_sizes = [1000, 5000, 10000]

    for size in test_sizes:
        print(f"\n测试数据大小: {size}")
        test_data = [random.randint(1, 1000) for _ in range(size)]

        # 测试各种排序算法
        algorithms = [
            ("冒泡排序", SortingAlgorithms.bubble_sort),
            ("快速排序", SortingAlgorithms.quick_sort),
            ("归并排序", SortingAlgorithms.merge_sort),
            ("堆排序", SortingAlgorithms.heap_sort),
        ]

        for name, func in algorithms:
            start_time = time.time()
            sorted_data = func(test_data)
            end_time = time.time()

            # 验证排序正确性
            is_correct = sorted_data == sorted(test_data)

            print(f"{name}: {end_time - start_time:.4f}秒 {'✓' if is_correct else '✗'}")


def demo_all_algorithms():
    """演示所有算法"""
    print("=== 基础算法演示 ===")

    # 排序算法演示
    test_array = [64, 34, 25, 12, 22, 11, 90]
    print(f"原数组: {test_array}")

    print(f"冒泡排序: {SortingAlgorithms.bubble_sort(test_array)}")
    print(f"快速排序: {SortingAlgorithms.quick_sort(test_array)}")
    print(f"归并排序: {SortingAlgorithms.merge_sort(test_array)}")
    print(f"堆排序: {SortingAlgorithms.heap_sort(test_array)}")

    # 搜索算法演示
    sorted_array = [11, 12, 22, 25, 34, 64, 90]
    target = 25
    print(f"\n在数组 {sorted_array} 中搜索 {target}:")
    print(f"线性搜索结果: {SearchAlgorithms.linear_search(sorted_array, target)}")
    print(f"二分搜索结果: {SearchAlgorithms.binary_search(sorted_array, target)}")

    # 图算法演示
    print("\n=== 图算法演示 ===")
    graph = {
        'A': ['B', 'C'],
        'B': ['D', 'E'],
        'C': ['F'],
        'D': [],
        'E': ['F'],
        'F': []
    }
    print(f"图结构: {graph}")
    print(f"DFS遍历: {GraphAlgorithms.dfs(graph, 'A')}")
    print(f"BFS遍历: {GraphAlgorithms.bfs(graph, 'A')}")

    # 字符串算法演示
    print("\n=== 字符串算法演示 ===")
    text = "ABABDABACDABABCABCABCABCABC"
    pattern = "ABABCABCABCABC"
    print(f"文本: {text}")
    print(f"模式: {pattern}")
    print(f"KMP搜索结果: {StringAlgorithms.kmp_search(text, pattern)}")

    text1, text2 = "ABCDGH", "AEDFHR"
    print(f"LCS({text1}, {text2}): {StringAlgorithms.longest_common_subsequence(text1, text2)}")

    # 数学算法演示
    print("\n=== 数学算法演示 ===")
    print(f"gcd(48, 18): {MathAlgorithms.gcd(48, 18)}")
    print(f"lcm(12, 18): {MathAlgorithms.lcm(12, 18)}")
    print(f"is_prime(17): {MathAlgorithms.is_prime(17)}")
    print(f"前20个素数: {MathAlgorithms.sieve_of_eratosthenes(20)}")
    print(f"2^10 mod 1000: {MathAlgorithms.fast_power(2, 10, 1000)}")

    # 回溯算法演示
    print("\n=== 回溯算法演示 ===")
    print(f"4皇后问题解的数量: {len(BacktrackingAlgorithms.n_queens(4))}")
    print(f"生成3对括号: {BacktrackingAlgorithms.generate_parentheses(3)}")

    # 动态规划演示
    print("\n=== 动态规划演示 ===")
    print(f"斐波那契数列第10项: {DynamicProgramming.fibonacci(10)}")
    print(f"零钱兑换[1,3,4]组成6: {DynamicProgramming.coin_change([1, 3, 4], 6)}")
    print(f"0-1背包问题: {DynamicProgramming.knapsack_01([2, 1, 3, 2], [12, 10, 20, 15], 5)}")

    # 后端场景演示
    print("\n=== 后端场景算法演示 ===")

    # 限流器演示
    rate_limiter = BackendScenarios.rate_limiter_sliding_window(60, 10)  # 1分钟内最多10个请求
    print(f"限流器测试: {[rate_limiter.is_allowed() for _ in range(12)]}")

    # 一致性哈希演示
    ch = BackendScenarios.consistent_hashing()
    servers = ["server1", "server2", "server3"]
    for server in servers:
        ch.add_server(server)

    data_keys = ["user:1001", "user:1002", "user:1003", "user:1004"]
    print("一致性哈希分布:")
    for key in data_keys:
        server = ch.get_server(key)
        print(f"  {key} -> {server}")

    # LRU缓存演示
    lru = BackendScenarios.lru_cache(3)
    operations = [
        ("put", 1, 1), ("put", 2, 2), ("get", 1),
        ("put", 3, 3), ("get", 2), ("put", 4, 4), ("get", 1)
    ]
    print("LRU缓存操作:")
    for op in operations:
        if op[0] == "put":
            lru.put(op[1], op[2])
            print(f"  put({op[1]}, {op[2]})")
        else:
            result = lru.get(op[1])
            print(f"  get({op[1]}) = {result}")

    # 布隆过滤器演示
    bf = BackendScenarios.bloom_filter(1000, 0.01)
    test_items = ["apple", "banana", "cherry", "date"]
    for item in test_items[:3]:
        bf.add(item)

    print("布隆过滤器测试:")
    for item in test_items:
        exists = bf.contains(item)
        print(f"  {item}: {'可能存在' if exists else '一定不存在'}")

    # 断路器演示
    cb = BackendScenarios.circuit_breaker(3, 5)

    def unreliable_service(should_fail=False):
        if should_fail:
            raise Exception("Service failed")
        return "Success"

    print("断路器测试:")
    # 模拟服务失败
    for i in range(5):
        try:
            result = cb.call(unreliable_service, should_fail=(i < 4))
            print(f"  调用{i+1}: {result}")
        except Exception as e:
            print(f"  调用{i+1}: 失败 - {e}")

    # 数据结构演示
    print("\n=== 数据结构演示 ===")

    # 链表演示
    linked_list = DataStructures.LinkedList()
    for val in [1, 2, 3, 4, 5]:
        linked_list.append(val)
    print(f"链表: {linked_list.to_list()}")

    # 栈演示
    stack = DataStructures.Stack()
    for val in [1, 2, 3]:
        stack.push(val)
    print(f"栈顶元素: {stack.peek()}")
    print(f"出栈: {stack.pop()}")

    # 队列演示
    queue = DataStructures.Queue()
    for val in [1, 2, 3]:
        queue.enqueue(val)
    print(f"队首元素: {queue.front()}")
    print(f"出队: {queue.dequeue()}")

    # 运行性能测试
    performance_test()


if __name__ == "__main__":
    demo_all_algorithms()
