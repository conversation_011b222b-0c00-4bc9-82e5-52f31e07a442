#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图算法集合
包含深度优先搜索(DFS)、广度优先搜索(BFS)、Dijkstra最短路径等算法
"""

from typing import List, Dict
from collections import deque
import heapq


class GraphAlgorithms:
    """图算法集合"""

    @staticmethod
    def dfs(graph: dict, start: str, visited: set = None) -> List[str]:
        """
        深度优先搜索
        时间复杂度: O(V + E)
        空间复杂度: O(V)

        原理: 尽可能深地搜索图的分支
        """
        if visited is None:
            visited = set()

        result = []
        if start not in visited:
            visited.add(start)
            result.append(start)

            for neighbor in graph.get(start, []):
                result.extend(GraphAlgorithms.dfs(graph, neighbor, visited))

        return result

    @staticmethod
    def bfs(graph: dict, start: str) -> List[str]:
        """
        广度优先搜索
        时间复杂度: O(V + E)
        空间复杂度: O(V)

        原理: 逐层搜索图的节点
        """
        visited = set()
        queue = deque([start])
        result = []

        while queue:
            node = queue.popleft()
            if node not in visited:
                visited.add(node)
                result.append(node)

                for neighbor in graph.get(node, []):
                    if neighbor not in visited:
                        queue.append(neighbor)

        return result

    @staticmethod
    def dijkstra(graph: dict, start: str) -> dict:
        """
        Dijkstra最短路径算法
        时间复杂度: O((V + E) log V)
        空间复杂度: O(V)

        原理: 贪心算法，每次选择距离最短的未访问节点
        """
        distances = {node: float('inf') for node in graph}
        distances[start] = 0
        pq = [(0, start)]
        visited = set()

        while pq:
            current_distance, current_node = heapq.heappop(pq)

            if current_node in visited:
                continue

            visited.add(current_node)

            for neighbor, weight in graph.get(current_node, []):
                distance = current_distance + weight

                if distance < distances[neighbor]:
                    distances[neighbor] = distance
                    heapq.heappush(pq, (distance, neighbor))

        return distances
