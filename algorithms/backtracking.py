#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回溯算法集合
包含N皇后问题、生成括号等经典回溯算法
"""

from typing import List


class BacktrackingAlgorithms:
    """回溯算法集合"""

    @staticmethod
    def n_queens(n: int) -> List[List[str]]:
        """
        N皇后问题
        时间复杂度: O(N!)
        空间复杂度: O(N)

        原理: 回溯法，逐行放置皇后，检查冲突
        """
        def is_safe(board, row, col):
            # 检查列
            for i in range(row):
                if board[i][col] == 'Q':
                    return False

            # 检查左上对角线
            for i, j in zip(range(row-1, -1, -1), range(col-1, -1, -1)):
                if board[i][j] == 'Q':
                    return False

            # 检查右上对角线
            for i, j in zip(range(row-1, -1, -1), range(col+1, n)):
                if board[i][j] == 'Q':
                    return False

            return True

        def solve(board, row):
            if row == n:
                return [[''.join(row) for row in board]]

            solutions = []
            for col in range(n):
                if is_safe(board, row, col):
                    board[row][col] = 'Q'
                    solutions.extend(solve(board, row + 1))
                    board[row][col] = '.'

            return solutions

        board = [['.' for _ in range(n)] for _ in range(n)]
        return solve(board, 0)

    @staticmethod
    def generate_parentheses(n: int) -> List[str]:
        """
        生成所有有效的括号组合
        时间复杂度: O(4^n / √n) - 卡特兰数
        空间复杂度: O(4^n / √n)

        原理: 回溯法，确保左括号数量不超过n，右括号数量不超过左括号
        """
        def backtrack(current, left, right):
            if len(current) == 2 * n:
                result.append(current)
                return

            if left < n:
                backtrack(current + '(', left + 1, right)

            if right < left:
                backtrack(current + ')', left, right + 1)

        result = []
        backtrack('', 0, 0)
        return result
