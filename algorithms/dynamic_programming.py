#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态规划算法集合
包含斐波那契数列、零钱兑换、背包问题等经典动态规划算法
"""

from typing import List


class DynamicProgramming:
    """动态规划算法集合"""

    @staticmethod
    def fibonacci(n: int) -> int:
        """
        斐波那契数列 - 动态规划版本
        时间复杂度: O(n)
        空间复杂度: O(1)

        原理: 状态转移方程 f(n) = f(n-1) + f(n-2)
        """
        if n <= 1:
            return n

        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b

        return b

    @staticmethod
    def coin_change(coins: List[int], amount: int) -> int:
        """
        零钱兑换问题
        时间复杂度: O(amount * len(coins))
        空间复杂度: O(amount)

        原理: dp[i] = min(dp[i-coin] + 1) for coin in coins
        """
        dp = [float('inf')] * (amount + 1)
        dp[0] = 0

        for i in range(1, amount + 1):
            for coin in coins:
                if i >= coin:
                    dp[i] = min(dp[i], dp[i - coin] + 1)

        return dp[amount] if dp[amount] != float('inf') else -1

    @staticmethod
    def knapsack_01(weights: List[int], values: List[int], capacity: int) -> int:
        """
        0-1背包问题
        时间复杂度: O(n * capacity)
        空间复杂度: O(capacity)

        原理: 对每个物品选择拿或不拿
        """
        n = len(weights)
        dp = [0] * (capacity + 1)

        for i in range(n):
            for w in range(capacity, weights[i] - 1, -1):
                dp[w] = max(dp[w], dp[w - weights[i]] + values[i])

        return dp[capacity]
