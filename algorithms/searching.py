#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索算法集合
包含线性搜索、二分搜索等常见搜索算法
"""

from typing import List


class SearchAlgorithms:
    """搜索算法集合"""
    
    @staticmethod
    def linear_search(arr: List[int], target: int) -> int:
        """
        线性搜索
        时间复杂度: O(n)
        空间复杂度: O(1)
        
        原理: 逐个检查数组元素
        """
        for i, value in enumerate(arr):
            if value == target:
                return i
        return -1
    
    @staticmethod
    def binary_search(arr: List[int], target: int) -> int:
        """
        二分搜索
        时间复杂度: O(log n)
        空间复杂度: O(1)
        前提: 数组必须有序
        
        原理: 每次比较中间元素，缩小搜索范围
        """
        left, right = 0, len(arr) - 1
        
        while left <= right:
            mid = (left + right) // 2
            
            if arr[mid] == target:
                return mid
            elif arr[mid] < target:
                left = mid + 1
            else:
                right = mid - 1
        
        return -1
    
    @staticmethod
    def binary_search_recursive(arr: List[int], target: int, left: int = 0, right: int = None) -> int:
        """
        递归版本的二分搜索
        """
        if right is None:
            right = len(arr) - 1
        
        if left > right:
            return -1
        
        mid = (left + right) // 2
        
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            return SearchAlgorithms.binary_search_recursive(arr, target, mid + 1, right)
        else:
            return SearchAlgorithms.binary_search_recursive(arr, target, left, mid - 1)
