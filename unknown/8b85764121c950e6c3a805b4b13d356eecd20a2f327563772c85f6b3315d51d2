#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础数据结构实现
包含链表、栈、队列等基础数据结构的实现
"""

from typing import List, Any


class DataStructures:
    """基础数据结构实现"""

    class ListNode:
        """链表节点"""
        def __init__(self, val: int = 0, next_node=None):
            self.val = val
            self.next = next_node
    
    class LinkedList:
        """单向链表实现"""
        
        def __init__(self):
            self.head = None
            self.size = 0
        
        def append(self, val: int):
            """在链表末尾添加元素"""
            new_node = DataStructures.ListNode(val)
            
            if not self.head:
                self.head = new_node
            else:
                current = self.head
                while current.next:
                    current = current.next
                current.next = new_node
            
            self.size += 1
        
        def prepend(self, val: int):
            """在链表开头添加元素"""
            new_node = DataStructures.ListNode(val, self.head)
            self.head = new_node
            self.size += 1
        
        def delete(self, val: int) -> bool:
            """删除指定值的节点"""
            if not self.head:
                return False
            
            if self.head.val == val:
                self.head = self.head.next
                self.size -= 1
                return True
            
            current = self.head
            while current.next:
                if current.next.val == val:
                    current.next = current.next.next
                    self.size -= 1
                    return True
                current = current.next
            
            return False
        
        def find(self, val: int) -> bool:
            """查找指定值"""
            current = self.head
            while current:
                if current.val == val:
                    return True
                current = current.next
            return False
        
        def to_list(self) -> List[int]:
            """转换为Python列表"""
            result = []
            current = self.head
            while current:
                result.append(current.val)
                current = current.next
            return result
    
    class Stack:
        """栈实现"""
        
        def __init__(self):
            self.items = []
        
        def push(self, item: Any):
            """入栈"""
            self.items.append(item)
        
        def pop(self) -> Any:
            """出栈"""
            if self.is_empty():
                raise IndexError("Stack is empty")
            return self.items.pop()
        
        def peek(self) -> Any:
            """查看栈顶元素"""
            if self.is_empty():
                raise IndexError("Stack is empty")
            return self.items[-1]
        
        def is_empty(self) -> bool:
            """检查栈是否为空"""
            return len(self.items) == 0
        
        def size(self) -> int:
            """获取栈大小"""
            return len(self.items)
    
    class Queue:
        """队列实现"""
        
        def __init__(self):
            self.items = []
        
        def enqueue(self, item: Any):
            """入队"""
            self.items.append(item)
        
        def dequeue(self) -> Any:
            """出队"""
            if self.is_empty():
                raise IndexError("Queue is empty")
            return self.items.pop(0)
        
        def front(self) -> Any:
            """查看队首元素"""
            if self.is_empty():
                raise IndexError("Queue is empty")
            return self.items[0]
        
        def is_empty(self) -> bool:
            """检查队列是否为空"""
            return len(self.items) == 0
        
        def size(self) -> int:
            """获取队列大小"""
            return len(self.items)

    class Heap:
        """堆实现 (默认为最小堆)"""

        def __init__(self, min_heap: bool = True):
            """
            :param min_heap: True for min-heap, False for max-heap.
            """
            self.heap: List[Any] = []
            self.is_min_heap = min_heap

        def _parent(self, i: int) -> int:
            return (i - 1) // 2

        def _left_child(self, i: int) -> int:
            return 2 * i + 1

        def _right_child(self, i: int) -> int:
            return 2 * i + 2

        def _swap(self, i: int, j: int):
            self.heap[i], self.heap[j] = self.heap[j], self.heap[i]

        def _compare(self, a: Any, b: Any) -> bool:
            if self.is_min_heap:
                return a < b
            else:
                return a > b

        def push(self, item: Any):
            """入堆"""
            self.heap.append(item)
            self._heapify_up(len(self.heap) - 1)

        def pop(self) -> Any:
            """出堆"""
            if self.is_empty():
                raise IndexError("Heap is empty")

            root = self.heap[0]
            last_item = self.heap.pop()

            if not self.is_empty():
                self.heap[0] = last_item
                self._heapify_down(0)

            return root

        def peek(self) -> Any:
            """查看堆顶元素"""
            if self.is_empty():
                raise IndexError("Heap is empty")
            return self.heap[0]

        def _heapify_up(self, i: int):
            parent_index = self._parent(i)
            if i > 0 and self._compare(self.heap[i], self.heap[parent_index]):
                self._swap(i, parent_index)
                self._heapify_up(parent_index)

        def _heapify_down(self, i: int):
            left = self._left_child(i)
            right = self._right_child(i)
            extreme_index = i

            if left < self.size() and self._compare(self.heap[left], self.heap[extreme_index]):
                extreme_index = left

            if right < self.size() and self._compare(self.heap[right], self.heap[extreme_index]):
                extreme_index = right

            if extreme_index != i:
                self._swap(i, extreme_index)
                self._heapify_down(extreme_index)

        def is_empty(self) -> bool:
            """检查堆是否为空"""
            return len(self.heap) == 0

        def size(self) -> int:
            """获取堆大小"""
            return len(self.heap)
