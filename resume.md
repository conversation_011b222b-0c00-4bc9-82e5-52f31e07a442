# 陈啸天

- 电话：18015528893
- 邮箱：<EMAIL>
- 地址：北京
- 方向：AIGC 应用
- 状态：在职

## 个人简介
专注文本类 AIGC 应用，具备从 0 到 1 构建 AI 长/短篇网文生产体系的经验。带领团队实现规模化生产。个人也有 AI 网文创作和 AI 自媒体号的经验，期望在 AIGC 方向继续深耕，推动内容创作与 AI 技术的深度融合。

## 教育背景
2014.9 - 2018.6 大连理工大学 工商管理 本科

## 核心技能
**AIGC 网文**
- 成功落地 AI 长/短篇网文规模化产线，月产能 200 本，成本降低至 5%，代表作品番茄小说在读量 50 万。
- 具备从 0 到 1 构建 AI 内容生产体系的能力，擅长攻克 AI 长篇创作核心技术难题。

**内容全链路技术经验**
- 熟悉内容创作、内容处理和内容分发全链路，了解内容管线、内容池和推荐系统。
- 熟悉大型爬虫系统架构与逆向风控技术，具备日均处理资讯与视频内容 5000 万+的规模化内容获取能力。

**团队管理与业务创新**
- 具备跨学科的sense、知识和实践的积累，擅长整合内容、数据、AI 等不同领域的能力，产出业务方案。
- 10+ 人跨 AI/内容/数据领域团队管理经验。

**技术栈**
- 编程语言：Python、Golang
- 架构能力：微服务化、API 网关、可观测体系
- AI 技术：prompt、workflow、mcp server
- 数据技术：爬虫系统、内容供需分析、内容理解和特征

## 工作经历
### 2023.6 - 至今 喜马拉雅 · 原创自营内容部 负责人
**核心职责**: 负责 AI 原创网文业务，通过 AI 创新解决版权引入成本高、真人原创效率低的业务痛点。

**主要工作成果**:
- **业务规划**: 基于对公司版权痛点的分析，提出 AI 原创网文规模化自生产方案，构建自有版权的 AI 内容供给体系。
- **团队建设**: 组建 10+ 人跨 AI/内容/数据领域团队，建立跨部门协作机制，推动部门业务发展获得公司认可。
- **协作模式**: 设计喜播学员精修、主播共创等新的内容生产协作模式，建立 500 人规模的外部协作人才库。
- **商业验证**: Q1 上架 547 张专辑，日均 UG 达 3 万，目标 25 年站内 UG 指数/会员收入达到新品的 20%。

### 2021.5 - 2023.5 腾讯视频 · 体育平台研发中心 高级后台开发
**核心职责**: 负责体育后台架构升级与技术体系建设，支撑日均 10 亿+ 流量的稳定运行。

**主要工作成果**:
- **架构升级**: 主导体育后台微服务化改造，重构 106 个接口，覆盖 93% 流量；QPS 提升 100%，响应时间降低 57%，可用性提升至 99.99%。
- **容灾体系**: 设计全链路降级容灾方案，实现 3 分钟内触发告警、 10 分钟内定位问题，保障大型赛事期间系统稳定性。
- **研发效能**: 搭建全链路灰度、接口录制回放等基础能力，建设多环境泳道的测试环境治理体系，实现自动化数据构造，成本从 1–2 天降低至分钟级，环境类 bug 降至 0。

### 2019.4 - 2021.4 一点资讯 · 内容智能部 负责人
**核心职责**: 负责内容智能技术体系建设，通过全网内容获取与智能分析，提升平台内容供给效率。

**主要工作成果**:
- **全网内容池**: 构建覆盖 30+ 主流平台的内容获取体系，日均更新 5000 万+ 内容，自媒体作者覆盖率达到 95%。
- **智能分析**: 建立内容、作者分级体系和特征挖掘能力，为平台提供原创识别、相似度比对等核心服务。
- **业务优化**: 通过数据驱动内容供给策略优化，实现 rctr 提升 18.4%，用户人均时长增加 148 秒，显著提升平台竞争力。

### 2018.7 - 2019.4 百度视频 · 技术平台1部 数据研发
**核心职责**: 负责视频内容数据处理与机器智能剪辑业务，构建自动化视频生产能力。

**主要工作成果**:
- **数据处理**: 建设长短视频、PGC 视频全业务线数据爬取与收录体系，保障平台内容供给稳定性。
- **机器剪辑**: 设计并实现机器剪辑视频业务完整技术方案，日均生产 1000+ 条视频，提升内容生产效率。
- **系统重构**: 重构视频处理流程调度系统，大幅提升处理透明度和系统稳定性，为业务快速发展奠定基础。

## 项目经历
### 2024.1 - 至今 AI 原创长/短篇网文规模化生产项目 负责人
**背景**: 攻克 AI 长/短篇创作的核心技术难题，构建从 0 到 1 的 AI 网文自动化产线，实现规模化商业应用。

**技术创新与项目成果**:
- **第一阶段（0-1 建产线）**: 创新提出融合网文理论、数据案例与 AI 工作流的技术路线，攻克 AI 在长/短篇网文的逻辑接续、情感表达、人设一致性等业界技术难题，成功落地 AI 网文产线。产出质量稳定在市场 90 分位以上，代表作品《让你管账号》在读量 50 万。
- **第二阶段（1-N 扩规模）**: 设计模块化写作技术方案，建设剧情单元素材库，采用状态算法管理续接逻辑，实现 AI 自动化写作。配套开发 AI 工具集和质检体系，确保规模化后质量稳定。月产能突破 200 本，成本降低至行业 5%。
- **有声制作**: 设计多线并行制作策略，通过内容分级实现智能匹配，建立 TTS/AI 制作人/真人主播三条制作线。Q1 上架 547 张专辑，日均 DAU 10 万+。

### 2021.8 - 2022.6 体育接入层升级 负责人
**背景**: 体育后台最初是 PHP 单体应用，集中在一个高度复杂的大项目中。随着业务发展和技术架构的演进，单体应用逐渐演变为接入层的角色，存在框架老，代码乱，性能差，运营难等问题。
- 项目一期，主导体育 PHP 接入层改造方案的设计和评审，将架构分层为 API 网关，接口适配层和领域层。以体育核心接口比赛/内容底层页、推荐信息流的重构为标杆案例，沉淀出通用的代码框架和组件，并提供了从代码设计，到正确性保证，再到灰度上线的全流程指引。
- 项目二期，以网关为起点建立全链路可观测体系。设计并落地一整套限流，降级和全链路过载保护的服务容灾方案。
- 收益：在该方案的指引下，体育在过去的一年中重构了 106 个接口，覆盖体育 93% 的流量，核心接口 QPS 提升 1 倍+，响应时间降低 57%，实现接口告警 3 分钟内触发，问题定位 10 分钟内完成，可用性提升至 99.99%。

### 2020.11 - 2021.4 内容和作者特征挖掘 负责人
- 支持站内外内容的点赞数、评论数等动态指标的分钟级监控。捕获竞品的 Push、热榜等信号。
- 理解内容主体和分类，挖掘内容的时效、原创、地域、稀缺度等属性，分析评论情感，多维度打分，建立全面的内容分级体系。
- 聚合作者近期的内容特征和表现数据，分析作者成长趋势，建设作者画像。挖掘出各领域的潜力作者，为自媒体平台提供价值线索。并提供创作者覆盖度、站内外表现对比等基础能力。
- 基于内容的站内外表现和基础特征，沉淀冷启池、高热池。通过长期实验，优化平台的内容供给策略，并在分发环节提供了热度信号，大幅提升内容分发效率。rctr 提升 18.4%，人均时长增加 148 秒。

### 2020.4 - 2020.10 全网内容池 负责人
- 覆盖大内容平台、垂类 TOP、传统新闻门户在内的 30+ 个站点，提供海量内容，每日更新量 5000 万+，主流站点自媒体作者覆盖率达到 95%。
- 基于 Airflow 分布式调度框架，定义出细分任务和组合链路，细分任务是最小粒度的爬取目标，组合链路将细分任务灵活地拼接，满足具体的爬取需求，增加可复用性。大幅提高开发效率和可维护性。
- 针对反爬和风控，设计一系列反反爬服务和策略，如代理 IP 模块、Cookie 模块、验证码识别、手机群控和浏览器集群等，突破公众号、小红书、抖音等主流平台。
- 设计了一套海量内容存储方案，包括去重、冷热分离和动态指标拉链等。
- 爬虫系统 PaaS 平台化，针对不同场景的内容获取需求，提供个性化爬取链路配置、任务调度和内容回传能力。