# 面试问题快速索引

> 本索引旨在将您准备的所有面试问题进行分类汇总，点击即可快速跳转至对应答案。

---

## 📚 按专题分类

### 🚀 AI/AIGC 与互动叙事
> 涵盖AI内容生产的核心方法论、技术实现、商业思考以及向互动叙事的延伸。

- **核心方法论**
    - [搭建AI网文产线的核心思路和整体业务流程是怎样的？](./project.md#问搭建ai网文产线的核心思路和整体业务流程是怎样的)
    - [如何进行选题和创新？](./project.md#问如何进行选题和创新)
    - [什么是剧情单元化？为什么它对AI写作至关重要？](./project.md#问什么是剧情单元化为什么它对ai写作至关重要)
    - [如何利用素材库支撑内容创作？](./project.md#问如何利用素材库支撑内容创作)
    - [如何将不同书的素材融合到新故事中，并保证剧情连接的逻辑自洽？](./project.md#问如何将不同书的素材融合到新故事中并保证剧情连接的逻辑自洽)
    - [如何评价当前AI网文的质量、潜力与短板？](./project.md#问如何评价当前ai网文的质量潜力和短板)
- **技术实现**
    - [能否从技术架构的视角，描绘一下整个AI网文生产线的全貌？](./project.md#问能否从技术架构的视角描绘一下整个ai网文生产线的全貌)
    - [如何解决AI网文的长上下文记忆问题，并保证人物性格不崩？](./project.md#问如何解决ai网文的长上下文记忆问题并保证人物性格不崩)
    - [AI写作的Prompt要点有哪些？如何迭代优化以避免"AI味"？](./project.md#问ai写作的prompt要点有哪些如何迭代优化以避免ai味)
    - [产线中工作流（Workflow）和智能体（Agent）如何分工？](./project.md#问产线中工作流workflow和智能体agent如何分工)
    - [如何选择使用商业和开源大模型？](./project.md#问如何选择使用商业和开源大模型)
    - [产线的质检体系是如何自动化的？](./project.md#问产线的质检体系是如何自动化的)
- **互动叙事升级**
    - [你的网文生产方法论，如何升级应用于"互动叙事"？](./addon.md#问题一你的网文生产方法论如何升级应用于互动叙事)
    - [技术上，如何保证互动故事中的"状态"和"人设"一致性？](./addon.md#问题三技术上如何保证互动故事中的状态和人设一致性)
- **商业与产品**
    - [如何界定人和AI的分工？如何管理外部协作者？](./project.md#问如何界定人和ai的分工如何管理外部协作者)
    - [为什么选择做产线而不是工具？如果SaaS化，产品形态如何？](./project.md#问为什么选择做产线而不是工具如果saas化产品形态如何)
    - [如何为"普通用户"设计一个"互动故事"创作工具？](./addon.md#问题二如何为普通用户设计一个互动故事创作工具)

---

### 🏛️ 技术架构与系统设计
> 涵盖微服务、领域驱动设计、容灾方案、数据迁移以及具体的系统设计案例。

- **微服务与架构思想**
    - [你提到主导了体育后台的微服务化改造，能谈谈你为什么决定做这次改造吗？](./project_tech.md#问你提到主导了体育后台的微服务化改造能谈谈你为什么决定做这次改造吗主要的技术驱动力是什么)
    - [简历里提到你将架构分层为 API 网关、接口适配层和领域层。能详细解释一下这个架构吗？](./project_tech.md#问简历里提到你将架构分层为-api-网关接口适配层和领域层能详细解释一下这个架构吗各层之间是如何交互的)
    - [你刚才提到了领域驱动设计（DDD），能具体讲讲你是如何理解和应用它的吗？](./project_tech.md#问你刚才提到了领域驱动设计ddd能具体讲讲你是如何理解和应用它的吗特别是在体育这个复杂的业务领域)
    - [在微服务拆分后，你们是如何保证跨服务的数据一致性的？](./project_tech.md#问在微服务拆分后你们是如何保证跨服务的数据一致性的比如一个操作需要同时修改赛事数据和用户数据这两个属于不同的微服务你们是如何处理这种情况的)
- **高可用与稳定性**
    - [你提到了建立了全链路可观测体系，能具体讲讲你们的技术栈和实践吗？](./project_tech.md#问你提到了建立了全链路可观测体系能具体讲讲你们的技术栈和实践吗比如你们是如何将日志指标和追踪关联起来实现快速问题定位的)
    - [你设计了全链路的容灾方案，实现了 99.99% 的可用性。能具体讲讲你是如何设计限流、降级和过载保护的吗？](./project_tech.md#问你设计了全链路的容灾方案实现了-9999-的可用性能具体讲讲你是如何设计限流降级和过载保护的吗)
    - [你提到了"多环境泳道"和"接口录制回放"，这些是如何提升研发效能的？](./project_tech.md#问你提到了多环境泳道和接口录制回放这些是如何提升研发效能的能展开讲讲技术实现吗)
- **数据迁移**
    - [你提到将单体拆分为了领域层，那在为这些新的领域微服务建立独立数据库时，你们是如何处理数据迁移的？](./project_tech.md#问你提到将单体拆分为了领域层那在为这些新的领域微服务建立独立数据库时你们是如何处理数据迁移的这个过程是如何保证对线上业务无感的)
- **系统设计题**
    - [设计短链接服务](./base.md#九-系统设计与架构能力)
    - [设计Feed流系统](./base.md#九-系统设计与架构能力)
    - [设计高可用计数器](./base.md#九-系统设计与架构能力)
    - [设计抢红包系统](./base.md#九-系统设计与架构能力)
    - [设计一个简单的爬虫框架](./base.md#九-系统设计与架构能力)

---

### ⚙️ 底层原理与中间件
> 涵盖计算机网络、操作系统、并发模型以及消息队列、缓存、网关等常用中间件的原理和实践。

- **基础原理**
    - [从腾讯体育项目切入，描述一个用户请求的完整生命周期](./base.md#一-基础篇计算机网络与操作系统)
    - [Go与PHP并发模型对比](./base.md#一-基础篇计算机网络与操作系统)
    - [系统监控与故障排查：当核心服务RT升高或错误率增加时，关注哪些系统指标？](./base.md#一-基础篇计算机网络与操作系统)
    - [如何优化服务器以支持百万级并发连接（C1000K问题）？](./base.md#一-基础篇计算机网络与操作系统)
- **消息队列**
    - [消息队列的选型与Saga实现：为什么选择RocketMQ？如何处理补偿和幂等？](./base.md#二-中间件篇)
    - [消息积压问题：如何监控、处理和预防？](./base.md#五-消息队列与异步处理)
    - [如何实现严格的顺序消息？](./base.md#五-消息队列与异步处理)
    - [死信队列的应用场景有哪些？](./base.md#五-消息队列与异步处理)
- **缓存与性能**
    - [多级缓存架构：如何设计APP、网关、服务、数据层的多级缓存体系？](./base.md#四-缓存与性能优化)
    - [缓存穿透、击穿、雪崩：三者的区别和解决方案？](./base.md#四-缓存与性能优化)
    - [CDN应用与刷新策略：如何处理动态内容和静态内容的缓存？](./base.md#四-缓存与性能优化)
    - [Redis高可用方案（哨兵、集群）的原理与区别？](./base.md#三-数据库与存储)
- **数据库与存储**
    - [分库分表：水平拆分和垂直拆分的选型，以及带来的问题](./base.md#三-数据库与存储)
    - [慢查询优化：从SQL语句、索引、架构层面如何优化？](./base.md#三-数据库与存储)
    - [HBase与时序数据库（如InfluxDB）的选型对比](./base.md#二-中间件篇)
- **API网关**
    - [在OpenResty上开发自定义插件时，如何选择合适的处理阶段？性能优化的关键点是什么？](./base.md#二-中间件篇)

---

### 📊 数据处理与爬虫
> 涵盖大规模分布式爬虫、反爬策略、数据处理与分析应用。

- **爬虫系统**
    - [你负责的"全网内容池"日均更新5000万+内容，其大规模分布式爬虫系统的核心架构是怎样的？](./project_tech.md#问你负责的全网内容池日均更新5000万内容这是一个非常大的体量能介绍一下这个大规模分布式爬虫系统的核心架构吗)
    - [你的分布式爬虫系统是如何进行优先级调度的？如何确保热点新闻的抓取优先级？](./project_tech.md#问你的分布式爬虫系统听起来很灵活但面对海量任务你们是如何进行优先级调度的比如如何确保突发热点新闻的抓取优先级高于常规内容更新)
    - [简历中提到了突破公众号、小红书等主流平台的反爬，具体用了哪些反反爬的策略和技术？](./project_tech.md#问简历中提到了突破公众号小红书等主流平台的反爬这是一个业界难题你们具体用了哪些反反爬的策略和技术)
- **数据应用**
    - [你提到建立了内容和作者的分级体系，能否详细讲讲你们是如何利用全网数据来构建它，并最终转化为业务增长的？](./project_tech.md#问你提到建立了内容和作者的分级体系能否详细讲讲你们是如何利用全网数据来构建它并最终转化为业务增长的)
    - [在内容分级体系中，你提到了"原创度"识别，具体是如何计算一篇文章的原创度的？](./project_tech.md#问在内容分级体系中你提到了原创度识别这是一个难点你们具体是如何计算一篇文章的原创度的如何处理常见的洗稿和伪原创)
    - [去重方案设计：对比Redis Hash和布隆过滤器在大规模去重场景中的优劣？](./base.md#二-中间件篇)

---

### 🧠 管理与综合能力
> 涵盖技术管理、项目管理、商业思维、行业洞察及个人发展规划。

- **技术管理与项目**
    - [如何做技术选型决策？](./base.md#十-管理与领导力)
    - [如何进行项目管理与风险控制？](./base.md#十-管理与领导力)
    - [如何处理线上重大故障？](./base.md#十-管理与领导力)
- **团队与协作**
    - [如何进行跨团队协作，解决部门墙问题？](./base.md#十-管理与领导力)
    - [如何进行团队建设与人才培养？](./base.md#十-管理与领导力)
- **商业与数据**
    - [在项目中如何进行成本控制（ROI分析）？](./base.md#十一-商业思维与业务创新)
    - [如何理解和实践MVP（最小可行产品）？](./base.md#十一-商业思维与业务创新)
    - [如何进行数据驱动决策？](./base.md#十一-商业思维与业务创新)
- **行业与个人**
    - [如何看待AIGC的未来发展趋势？](./base.md#十二-行业洞察与发展趋势)
    - [你的职业规划是怎样的？为什么想从技术转向管理？](./base.md#十三-职业发展与转型逻辑)
    - [你选择公司的标准是什么？](./base.md#十三-职业发展与转型逻辑)

---

### 💻 算法编程题
> 核心高频算法题，点击可跳转至复习计划。详细解法在 `algorithms.py` 中。

- **数组与哈希表**: [题目列表](./leetcode.md#day-1-数组与哈希表)
- **链表**: [题目列表](./leetcode.md#day-2-链表)
- **栈与队列**: [题目列表](./leetcode.md#day-3-栈与队列)
- **二叉树 (基础与遍历)**: [题目列表](./leetcode.md#day-4-二叉树-基础与遍历)
- **二叉树 (构造与搜索)**: [题目列表](./leetcode.md#day-5-二叉树-构造与搜索)
- **查找与排序**: [题目列表](./leetcode.md#day-6-查找与排序)
- **回溯与图**: [题目列表](./leetcode.md#day-7-回溯与图)
- **动态规划 (基础)**: [题目列表](./leetcode.md#day-8-动态规划-基础)
- **动态规划 (进阶)**: [题目列表](./leetcode.md#day-9-动态规划-进阶)
- **字符串、设计与总复习**: [题目列表](./leetcode.md#day-10-字符串设计与总复习)
